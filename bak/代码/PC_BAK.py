import os
import re
import shutil
import time
import threading
import queue
from datetime import datetime, timedelta
import logging
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger()

# 常量定义
AIRCRAFT_PATTERN_FULL = r'[Bb]-[A-Za-z0-9]{4}'  # B-XXXX 格式
AIRCRAFT_PATTERN_SHORT = r'6[A-Za-z0-9]{3}'     # 6XXX 格式
# 日期匹配模式（支持多种格式）
DATE_PATTERNS = [
    r'(20\d{2})[-\/\.](\d{1,2})[-\/\.](\d{1,2})',  # yyyy-mm-dd, yyyy/mm/dd, yyyy.mm.dd
    r'(\d{1,2})[-\/\.](\d{1,2})[-\/\.](20\d{2})',  # mm-dd-yyyy, mm/dd/yyyy, mm.dd.yyyy
    r'(20\d{2})(\d{2})(\d{2})',                     # yyyymmdd
    r'(\d{2})(\d{2})(20\d{2})',                     # mmddyyyy
]

def extract_aircraft_info(folder_path):
    """从文件夹路径中提取飞机号信息"""
    path_parts = folder_path.split(os.sep)
    
    for part in reversed(path_parts):  # 从最深层开始查找
        if not part:
            continue
            
        # 首先查找完整的B-XXXX格式
        match = re.search(AIRCRAFT_PATTERN_FULL, part, re.I)
        if match:
            aircraft = match.group(0).upper()
            if not aircraft.startswith('B-'):
                aircraft = 'B-' + aircraft[2:]  # 确保是B-开头
            logger.info(f"    找到完整飞机号: {aircraft}")
            return aircraft
        
        # 然后查找6XXX格式
        match = re.search(AIRCRAFT_PATTERN_SHORT, part)
        if match:
            aircraft = 'B-' + match.group(0).upper()
            logger.info(f"    找到短格式飞机号: {aircraft}")
            return aircraft
    
    logger.info(f"    未找到飞机号信息")
    return None

def extract_date_info(folder_path):
    """从文件夹路径中提取日期信息"""
    path_parts = folder_path.split(os.sep)
    
    for part in reversed(path_parts):  # 从最深层开始查找
        if not part:
            continue
            
        for pattern in DATE_PATTERNS:
            match = re.search(pattern, part)
            if match:
                groups = match.groups()
                
                # 根据不同的模式处理日期
                if len(groups) == 3:
                    if len(groups[0]) == 4:  # yyyy-mm-dd 格式
                        year, month, day = groups
                    elif len(groups[2]) == 4:  # mm-dd-yyyy 格式
                        month, day, year = groups
                    else:  # 其他格式，尝试智能识别
                        # 如果第一个数字大于12，可能是年份
                        if int(groups[0]) > 12:
                            year, month, day = groups
                        else:
                            month, day, year = groups
                    
                    try:
                        # 验证日期有效性
                        year = int(year)
                        month = int(month)
                        day = int(day)
                        
                        # 确保年份是4位数
                        if year < 100:
                            if year < 80:
                                year += 2000
                            else:
                                year += 1900
                        
                        # 验证月份和日期范围
                        if 1 <= month <= 12 and 1 <= day <= 31:
                            date_str = f"{year:04d}-{month:02d}-{day:02d}"
                            logger.info(f"    找到日期信息: {date_str} (从 '{part}' 提取)")
                            return date_str
                    except ValueError:
                        continue
    
    logger.info(f"    未找到日期信息")
    return None

def parse_mixed_format_file(file_path):
    """解析混合格式文件（文本+二进制）"""
    try:
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)
        logger.info(f"  文件大小: {file_size_mb:.2f} MB")

        # 尝试多种编码方式
        encodings_to_try = ['utf-8', 'latin1', 'cp1252', 'utf-8-sig', 'ascii']

        best_records = []
        best_encoding = None

        for encoding in encodings_to_try:
            try:
                records = extract_records_with_encoding(file_path, encoding)
                if records and len(records) > len(best_records):
                    best_records = records
                    best_encoding = encoding
                    # 如果找到足够多的记录，就使用这个编码
                    if len(records) > 50:
                        break
            except Exception as e:
                continue

        # 检查是否需要启动第二种识别方式
        has_valid_aircraft = False
        has_valid_date = False

        if best_records:
            # 检查是否有有效的飞机号和日期信息
            for record in best_records:
                if record.get('ac') and record.get('ac') != 'None':
                    has_valid_aircraft = True
                if record.get('date'):
                    has_valid_date = True
                if has_valid_aircraft and has_valid_date:
                    break

        # 如果现有方法没有找到完整信息，启动第二种识别方式
        cc_records = []
        if not best_records or not (has_valid_aircraft and has_valid_date):
            logger.info(f"  现有方法未找到完整信息，启动CC B-关键字搜索...")
            try:
                cc_records = parse_cc_format(file_path, best_encoding or 'latin1')
                if cc_records:
                    logger.info(f"  CC格式解析找到 {len(cc_records)} 条记录")
                else:
                    logger.info(f"  CC格式解析未找到记录")
            except Exception as e:
                logger.warning(f"  CC格式解析失败: {str(e)}")

        # 合并两种方式的记录
        all_records = best_records + cc_records

        if not all_records:
            logger.warning(f"  两种解析方式都未找到任何有效记录")
            return None, 0

        logger.info(f"  总共找到 {len(all_records)} 条记录（原方式: {len(best_records)}, CC方式: {len(cc_records)}）")

        # 按时间排序
        sorted_records = sorted(all_records, key=lambda x: x['datetime'])

        # 找到绝对最新的记录
        latest_record = sorted_records[-1]

        # 如果最新记录的飞机号为空，需要组合
        if not latest_record.get('ac') or latest_record.get('ac') == 'None':
            # 找到最新的完整记录（有飞机号的）
            complete_records = [r for r in sorted_records if r.get('ac') and r.get('ac') != 'None']
            if complete_records:
                latest_complete_record = max(complete_records, key=lambda x: x['datetime'])
                latest_record['ac'] = latest_complete_record['ac']
                latest_record['ac_source'] = 'combined'
                logger.info(f"  组合逻辑: 最新时间记录飞机号为空，使用最新完整记录的飞机号: {latest_complete_record['ac']}")
                logger.info(f"  最新完整记录时间: {latest_complete_record['datetime']}")
            else:
                # 如果文件中完全没有完整飞机号，尝试使用文件夹中的飞机号
                logger.info(f"  文件中完全没有完整飞机号记录，将在处理函数中使用文件夹飞机号")

        # 显示最新的几条记录用于验证
        logger.info(f"  最新的3条记录:")
        for i, record in enumerate(sorted_records[-3:]):
            idx = len(sorted_records) - 3 + i + 1
            ac_info = record['ac'] if record.get('ac') else 'None'
            source_info = ""
            if record.get('ac_source') == 'combined':
                source_info = " (组合)"
            elif record.get('ac_source') == 'latest_complete':
                source_info = " (推断)"
            elif record.get('parse_method') == 'cc_format':
                source_info = " (CC格式)"
            logger.info(f"    {idx}: {record['datetime']} - DATE={record['date']} TIME={record['time']} A/C={ac_info}{source_info}")

        # 返回最新记录
        result = {k: v for k, v in latest_record.items() if k not in ['datetime', 'line_num', 'ac_source', 'raw_line', 'parse_method']}

        # 如果是组合的飞机号，添加标记
        if latest_record.get('ac_source') == 'combined':
            result['ac_combined'] = True

        return result, len(all_records)

    except Exception as e:
        logger.error(f"  文件解析异常: {str(e)}")
        import traceback
        logger.error(f"  详细错误: {traceback.format_exc()}")
        return None, 0

def extract_records_with_encoding(file_path, encoding):
    """使用指定编码提取记录 - 改进版本处理飞机号缺失的情况"""
    # 预编译正则表达式
    date_time_pattern = re.compile(r'DATE:\s*(\d{2})/(\d{2})/(\d{2})\s+TIME:\s*(\d{2}:\d{2}:\d{2})', re.I)
    ac_pattern = re.compile(r'A/C:\s*(B-[A-Z0-9]{4})', re.I)
    ac_empty_pattern = re.compile(r"A/C:\s*['\s]*", re.I)  # 匹配空的飞机号
    
    complete_records = []  # 有完整飞机号的记录
    time_only_records = []  # 只有时间没有飞机号的记录
    valid_aircraft_codes = set()  # 收集所有有效的飞机号
    
    line_count = 0
    current_record = {}
    
    with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
        for line in f:
            line_count += 1
            
            # 跳过二进制行
            if is_binary_line(line):
                continue
            
            line = line.strip()
            if not line:
                continue
            
            # 查找日期时间行
            date_time_match = date_time_pattern.search(line)
            if date_time_match:
                year, month, day, time = date_time_match.groups()
                current_record = {
                    'date': f"{year}/{month}/{day}",
                    'time': time,
                    'line_num': line_count,
                    'raw_line': line
                }
                continue
            
            # 查找飞机号行
            if current_record and 'ac' not in current_record:
                # 检查是否有完整的飞机号
                ac_match = ac_pattern.search(line)
                if ac_match:
                    aircraft_code = ac_match.group(1).upper()
                    current_record['ac'] = aircraft_code
                    valid_aircraft_codes.add(aircraft_code)
                    
                    # 转换日期时间
                    dt = convert_to_datetime(
                        current_record['date'].split('/')[0],
                        current_record['date'].split('/')[1],
                        current_record['date'].split('/')[2],
                        current_record['time']
                    )
                    
                    if dt:
                        current_record['datetime'] = dt
                        complete_records.append(current_record.copy())
                    
                    current_record = {}
                    continue
                
                # 检查是否是空的飞机号行
                ac_empty_match = ac_empty_pattern.search(line)
                if ac_empty_match:
                    # 转换日期时间
                    dt = convert_to_datetime(
                        current_record['date'].split('/')[0],
                        current_record['date'].split('/')[1],
                        current_record['date'].split('/')[2],
                        current_record['time']
                    )
                    
                    if dt:
                        current_record['datetime'] = dt
                        current_record['ac'] = None  # 标记为空飞机号
                        time_only_records.append(current_record.copy())
                    
                    current_record = {}
    
    # 简化逻辑：保持原始记录状态，在最终处理时再组合
    logger.info(f"    完整记录: {len(complete_records)} 条")
    logger.info(f"    缺失飞机号记录: {len(time_only_records)} 条")

    # 合并所有记录，保持原始状态
    all_records = complete_records + time_only_records

    logger.info(f"    总记录: {len(all_records)} 条")

    return all_records

def is_binary_line(line):
    """判断一行是否为二进制数据"""
    if len(line) < 5:
        return False
    
    # 计算非打印字符的比例
    non_printable_count = sum(1 for c in line if ord(c) < 32 or ord(c) > 126)
    non_printable_ratio = non_printable_count / len(line)
    
    # 如果非打印字符超过30%，认为是二进制行
    return non_printable_ratio > 0.3

def convert_to_datetime(year, month, day, time_str):
    """将日期时间字符串转换为datetime对象"""
    try:
        year_int = int(year)
        # 修正年份转换逻辑：对于航班数据，69应该是1969年，不是2069年
        if year_int < 50:  # 00-49 认为是20xx年
            full_year = 2000 + year_int
        elif year_int < 100:  # 50-99 认为是19xx年
            full_year = 1900 + year_int
        else:
            full_year = year_int

        month = month.zfill(2)
        day = day.zfill(2)

        dt = datetime.strptime(f"{full_year}-{month}-{day} {time_str}", "%Y-%m-%d %H:%M:%S")
        return dt
    except Exception as e:
        return None

def convert_month_abbr_to_number(month_abbr):
    """将英文月份缩写转换为数字"""
    month_map = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
        'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
        'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
    }
    return month_map.get(month_abbr.upper(), None)

def save_results_to_excel(results, output_path):
    """保存结果到Excel文件，优先使用pandas，备用openpyxl"""
    try:
        if PANDAS_AVAILABLE:
            # 使用pandas保存
            df = pd.DataFrame(results)[[
                'folder_path', 'folder_aircraft', 'folder_date',
                'msg_aircraft', 'msg_date', 'msg_time',
                'total_records', 'date_diff', 'status'
            ]]
            df.to_excel(output_path, index=False)
            return True
        elif OPENPYXL_AVAILABLE:
            # 使用openpyxl保存
            wb = Workbook()
            ws = wb.active

            # 写入表头
            headers = ['folder_path', 'folder_aircraft', 'folder_date',
                      'msg_aircraft', 'msg_date', 'msg_time',
                      'total_records', 'date_diff', 'status']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据
            for row, result in enumerate(results, 2):
                for col, header in enumerate(headers, 1):
                    ws.cell(row=row, column=col, value=result.get(header, ''))

            wb.save(output_path)
            return True
        else:
            logger.error("无法保存Excel文件：pandas和openpyxl都不可用")
            return False
    except Exception as e:
        logger.error(f"保存Excel文件失败: {str(e)}")
        return False

def parse_cc_format(file_path, encoding):
    """解析CC B-格式的记录
    格式示例: "CC B-30CQ MAR21 122040 ZUGY ZPJH 1807"
    对应: CC 飞机号 月份日期 时间 起飞机场 着陆机场 航班号后四位

    注意：MAR21表示3月21日，年份使用程序执行时的当前年份
    """
    try:
        logger.info(f"    开始CC格式解析，使用编码: {encoding}")

        # CC格式的正则表达式
        # CC B-30CQ MAR21 122040 ZUGY ZPJH 1807
        cc_pattern = re.compile(r'CC\s+(B-[A-Z0-9]{4})\s+([A-Z]{3})(\d{2})\s+(\d{6})\s+([A-Z]{4})\s+([A-Z]{4})\s+(\d{4})', re.I)

        records = []
        line_count = 0

        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line in f:
                line_count += 1

                # 跳过二进制行
                if is_binary_line(line):
                    continue

                line = line.strip()
                if not line:
                    continue

                # 查找CC格式
                cc_match = cc_pattern.search(line)
                if cc_match:
                    aircraft, month_abbr, day_str, time_str, dep_airport, arr_airport, flight_suffix = cc_match.groups()

                    # 转换月份
                    month_num = convert_month_abbr_to_number(month_abbr)
                    if not month_num:
                        logger.warning(f"    无法识别月份: {month_abbr}")
                        continue

                    # 解析日期：MAR21中的21是日期，不是年份
                    day_int = int(day_str)
                    if day_int < 1 or day_int > 31:
                        logger.warning(f"    无效日期: {day_int}")
                        continue

                    # 智能年份判断：航班数据不可能来自未来
                    current_date = datetime.now()
                    current_year = current_date.year

                    # 先尝试使用当前年份构建日期
                    try:
                        test_date = datetime(current_year, int(month_num), day_int)
                        # 如果构建的日期比当前日期晚，说明应该是去年的数据
                        if test_date > current_date:
                            current_year = current_year - 1
                            logger.info(f"    日期 {month_abbr}{day_str} 比当前日期晚，使用上一年: {current_year}")
                    except ValueError:
                        # 如果日期无效（如2月30日），使用当前年份
                        logger.warning(f"    无效日期组合: {current_year}-{month_num}-{day_int}，使用当前年份")

                    # 解析时间 (HHMMSS)
                    if len(time_str) == 6:
                        hour = time_str[:2]
                        minute = time_str[2:4]
                        second = time_str[4:6]
                        formatted_time = f"{hour}:{minute}:{second}"
                    else:
                        logger.warning(f"    时间格式错误: {time_str}")
                        continue

                    # 构建日期字符串（与原有格式保持一致：yy/mm/dd）
                    year_suffix = str(current_year)[-2:]  # 取年份后两位
                    date_str = f"{year_suffix}/{month_num}/{day_int:02d}"

                    # 转换为datetime对象
                    try:
                        dt = datetime.strptime(f"{current_year}-{month_num}-{day_int:02d} {formatted_time}", "%Y-%m-%d %H:%M:%S")
                    except Exception as e:
                        logger.warning(f"    日期时间转换失败: {current_year}-{month_num}-{day_int:02d} {formatted_time}, 错误: {e}")
                        continue

                    record = {
                        'date': date_str,
                        'time': formatted_time,
                        'ac': aircraft.upper(),
                        'line_num': line_count,
                        'raw_line': line,
                        'datetime': dt,
                        'parse_method': 'cc_format',
                        'dep_airport': dep_airport,
                        'arr_airport': arr_airport,
                        'flight_suffix': flight_suffix,
                        'month_abbr': month_abbr,
                        'day_str': day_str
                    }

                    records.append(record)
                    logger.info(f"    找到CC记录: {aircraft} {month_abbr}{day_str} {formatted_time} {dep_airport}->{arr_airport} EU{flight_suffix}")

        logger.info(f"    CC格式解析完成，找到 {len(records)} 条记录")
        return records

    except Exception as e:
        logger.error(f"    CC格式解析异常: {str(e)}")
        import traceback
        logger.error(f"    详细错误: {traceback.format_exc()}")
        return []

def is_date_within_week(folder_date, msg_date):
    """检查两个日期是否在一周内（含7天）"""
    try:
        if not folder_date or not msg_date:
            return False
        date1 = datetime.strptime(folder_date, '%Y-%m-%d')
        date2 = datetime.strptime(msg_date, '%Y-%m-%d')
        return abs((date1 - date2).days) <= 7
    except Exception as e:
        logger.warning(f"日期比较失败: {folder_date} vs {msg_date} - {str(e)}")
        return False

def convert_utc_to_beijing(utc_date, utc_time):
    """UTC转北京时间"""
    try:
        match = re.match(r'(\d{2})[/\-\.](\d{2})[/\-\.](\d{2})', utc_date)
        if not match:
            return None, None
        
        year, month, day = match.groups()
        dt = convert_to_datetime(year, month, day, utc_time)
        if not dt:
            return None, None
        
        beijing_dt = dt + timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d'), beijing_dt.strftime('%H:%M:%S')
    except Exception as e:
        logger.warning(f"UTC转换失败: {utc_date} {utc_time} - {str(e)}")
        return None, None

def process_folder(folder_path, file_index, total_files):
    """处理单个文件夹"""
    folder_name = os.path.basename(folder_path)

    try:
        logger.info(f"\n{'='*60}")
        logger.info(f"[{file_index}/{total_files}] 开始处理: {folder_name}")
        logger.info(f"  完整路径: {folder_path}")

        # 提取飞机号和日期信息
        logger.info(f"  提取飞机号信息...")
        folder_ac = extract_aircraft_info(folder_path)

        logger.info(f"  提取日期信息...")
        folder_date = extract_date_info(folder_path)

        # 查找MSG.DAT文件
        msg_dat_path = os.path.join(folder_path, 'MSG.DAT')
        if not os.path.exists(msg_dat_path):
            logger.warning(f"[{file_index}/{total_files}] 未找到MSG.DAT文件: {folder_name}")
            return None

        # 检查文件访问权限
        if not os.access(msg_dat_path, os.R_OK):
            logger.warning(f"[{file_index}/{total_files}] 文件无读取权限: {folder_name}")
            return None

        logger.info(f"  开始解析MSG.DAT文件...")
        record, total_records = parse_mixed_format_file(msg_dat_path)

        if not record:
            logger.warning(f"[{file_index}/{total_files}] 未找到有效记录: {folder_name}")
            return None

    except Exception as e:
        logger.error(f"[{file_index}/{total_files}] 处理文件夹时发生异常: {folder_name} - {str(e)}")
        return None

    # 转换UTC时间为北京时间
    beijing_date, beijing_time = convert_utc_to_beijing(record['date'], record['time'])
    if not beijing_date:
        logger.warning(f"[{file_index}/{total_files}] 日期格式无效: {folder_name}")
        return None

    # 最终飞机号处理：保持MSG.DAT的真实性，没有就留空
    final_aircraft = record['ac']
    if not final_aircraft or final_aircraft == 'None':
        final_aircraft = None
        logger.info(f"  MSG.DAT文件中没有飞机号信息，保持为空")

    # 检查匹配条件
    is_ac_match = False
    is_date_match = False

    if folder_ac and final_aircraft:
        is_ac_match = (final_aircraft.upper() == folder_ac.upper())

    if folder_date and beijing_date:
        is_date_match = is_date_within_week(folder_date, beijing_date)

    # 确定状态
    if folder_ac and folder_date:
        status = '一致' if (is_ac_match and is_date_match) else '不一致'
    elif folder_ac:
        status = '一致' if is_ac_match else '不一致'
    elif folder_date:
        status = '一致' if is_date_match else '不一致'
    else:
        status = '信息不足'

    # 计算日期差
    date_diff = None
    if folder_date and beijing_date:
        try:
            date_diff = abs((datetime.strptime(folder_date, '%Y-%m-%d') -
                            datetime.strptime(beijing_date, '%Y-%m-%d')).days)
        except:
            date_diff = 999

    # 输出日志
    logger.info(f"\n[{file_index}/{total_files}] 处理完成: {folder_name}")
    logger.info(f"  找到记录数: {total_records}")

    # 显示飞机号信息
    ac_info = final_aircraft or 'None'
    if record.get('ac_source') == 'combined':
        ac_info += " (组合)"
        logger.info(f"  最新记录: A/C={ac_info} DATE={record['date']} TIME={record['time']} (最新时间+最新完整飞机号)")
    else:
        logger.info(f"  最新记录: A/C={ac_info} DATE={record['date']} TIME={record['time']}")

    logger.info(f"  北京时间: {beijing_date} {beijing_time}")
    logger.info(f"  文件夹飞机号: {folder_ac or '未找到'}")
    logger.info(f"  文件夹日期: {folder_date or '未找到'}")
    logger.info(f"  匹配结果: {status}")
    if date_diff is not None:
        logger.info(f"  日期差: {date_diff}天")

    return {
        'folder_path': folder_path,
        'folder_aircraft': folder_ac or '',
        'folder_date': folder_date or '',
        'msg_aircraft': final_aircraft or '',  # 如果MSG.DAT中没有飞机号，这里就是空字符串
        'msg_date': beijing_date,
        'msg_time': beijing_time,
        'total_records': total_records,
        'status': status,
        'date_diff': date_diff
    }

# 移除未使用的main函数，GUI版本中不需要命令行接口

# ==================== GUI界面类 ====================

class FlightAnalyzerGUI:
    """航班数据分析器GUI界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("PC_BAK")
        self.root.geometry("900x750")
        self.root.configure(bg='#f0f0f0')

        # 数据队列用于线程间通信
        self.data_queue = queue.Queue()
        self.progress_queue = queue.Queue()

        # 分析结果存储
        self.analysis_results = []
        self.is_analyzing = False
        self.msg_reading_completed = False

        # 时间统计
        self.start_time = None
        self.current_file_start_time = None
        self.processed_files = 0
        self.total_files = 0

        # 创建界面
        self.create_widgets()

        # 启动队列监听
        self.check_queue()
        # 启动时间更新定时器
        self.update_time_display()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=3, pady=(0, 20), sticky=(tk.W, tk.E))

        title_label = ttk.Label(title_frame, text="PC卡数据分析与文件复制工具",
                               font=('Microsoft YaHei', 18, 'bold'),
                               foreground='#2c3e50')
        title_label.pack()

        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(1, weight=1)

        # 输入文件夹选择
        ttk.Label(config_frame, text="输入文件夹:", font=('Microsoft YaHei', 10)).grid(
            row=0, column=0, sticky=tk.W, pady=8, padx=(0, 10))

        self.input_path_var = tk.StringVar(value="Z:\\PC卡")
        input_entry = ttk.Entry(config_frame, textvariable=self.input_path_var,
                               width=60, font=('Consolas', 10))
        input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=8)

        input_browse_btn = ttk.Button(config_frame, text="浏览...",
                                     command=self.browse_input_folder,
                                     width=10)
        input_browse_btn.grid(row=0, column=2, pady=8)

        # 目标文件夹选择
        ttk.Label(config_frame, text="目标文件夹:", font=('Microsoft YaHei', 10)).grid(
            row=1, column=0, sticky=tk.W, pady=8, padx=(0, 10))

        self.output_path_var = tk.StringVar(value="Z:\\DATA_BAK\\FDIMU_PC")
        output_entry = ttk.Entry(config_frame, textvariable=self.output_path_var,
                                width=60, font=('Consolas', 10))
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=8)

        output_browse_btn = ttk.Button(config_frame, text="浏览...",
                                      command=self.browse_output_folder,
                                      width=10)
        output_browse_btn.grid(row=1, column=2, pady=8)

        # 控制按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=15)

        # MSG读取按钮
        self.msg_read_btn = ttk.Button(button_frame, text="📖 MSG读取",
                                      command=self.start_msg_reading,
                                      style='Accent.TButton',
                                      width=15)
        self.msg_read_btn.pack(side=tk.LEFT, padx=(0, 10))

        # PC卡复制按钮
        self.copy_btn = ttk.Button(button_frame, text="📁 PC卡复制",
                                  command=self.start_file_copy,
                                  state='disabled',
                                  width=15)
        self.copy_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_btn = ttk.Button(button_frame, text="⏹ 停止",
                                  command=self.stop_analysis,
                                  state='disabled',
                                  width=12)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 清空结果按钮
        self.clear_btn = ttk.Button(button_frame, text="🗑 清空结果",
                                   command=self.clear_results,
                                   width=12)
        self.clear_btn.pack(side=tk.LEFT)

        # 进度显示区域
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)

        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪 - 请选择输入文件夹并点击'MSG读取'开始分析")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                font=('Microsoft YaHei', 10),
                                foreground='#2980b9')
        status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 详细进度信息
        self.progress_detail_var = tk.StringVar(value="")
        progress_detail_label = ttk.Label(progress_frame, textvariable=self.progress_detail_var,
                                         font=('Microsoft YaHei', 9),
                                         foreground='#7f8c8d')
        progress_detail_label.grid(row=2, column=0, sticky=tk.W)

        # 结果显示区域
        self.create_results_area(main_frame)

    def create_results_area(self, parent):
        """创建结果显示区域"""
        # 结果框架
        results_frame = ttk.LabelFrame(parent, text="分析结果", padding="10")
        results_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # 创建Treeview - 添加数据复制列
        columns = ('folder_name', 'folder_aircraft', 'folder_date',
                  'msg_aircraft', 'msg_date', 'total_records', 'date_diff',
                  'final_aircraft', 'final_date', 'copy_progress')

        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=25)

        # 定义列标题和宽度 - 添加数据复制列
        column_config = {
            'folder_name': ('PC卡数据', 110),
            'folder_aircraft': ('飞机号_MCC', 65),
            'folder_date': ('日期_MCC', 65),
            'msg_aircraft': ('飞机号_MSG', 65),
            'msg_date': ('日期_MSG', 65),
            'total_records': ('记录数', 45),
            'date_diff': ('日期差', 40),
            'final_aircraft': ('飞机号_BAK', 65),
            'final_date': ('日期_BAK', 65),
            'copy_progress': ('数据复制', 60)
        }

        for col, (heading, width) in column_config.items():
            self.tree.heading(col, text=heading)
            # PC卡数据列左对齐，其他列居中对齐
            if col == 'folder_name':
                self.tree.column(col, width=width, minwidth=50, anchor='w')
            else:
                self.tree.column(col, width=width, minwidth=50, anchor='center')

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # 配置不一致行的样式
        self.tree.tag_configure('inconsistent', background='#ffebee', foreground='#c62828')
        self.tree.tag_configure('consistent', background='#e8f5e8', foreground='#2e7d32')
        # 配置正在复制行的样式（蓝色高亮）
        self.tree.tag_configure('copying', background='#e3f2fd', foreground='#1565c0')

        # 绑定双击编辑事件
        self.tree.bind('<Double-1>', self.on_item_double_click)

        # 统计信息
        stats_frame = ttk.Frame(results_frame)
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        stats_frame.columnconfigure(1, weight=1)

        # 左侧统计信息
        self.stats_var = tk.StringVar(value="统计信息: 等待分析...")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var,
                               font=('Microsoft YaHei', 9),
                               foreground='#34495e')
        stats_label.grid(row=0, column=0, sticky=tk.W)

        # 右侧时间信息
        self.time_stats_var = tk.StringVar(value="")
        time_stats_label = ttk.Label(stats_frame, textvariable=self.time_stats_var,
                                    font=('Microsoft YaHei', 9),
                                    foreground='#7f8c8d')
        time_stats_label.grid(row=0, column=1, sticky=tk.E)

    def browse_input_folder(self):
        """浏览输入文件夹"""
        folder = filedialog.askdirectory(
            title="选择输入文件夹",
            initialdir=self.input_path_var.get()
        )
        if folder:
            self.input_path_var.set(folder)

    def browse_output_folder(self):
        """浏览输出文件夹"""
        folder = filedialog.askdirectory(
            title="选择目标文件夹",
            initialdir=self.output_path_var.get()
        )
        if folder:
            self.output_path_var.set(folder)

    def start_msg_reading(self):
        """开始MSG读取"""
        input_path = self.input_path_var.get().strip()

        if not input_path:
            messagebox.showerror("错误", "请选择输入文件夹")
            return

        if not os.path.exists(input_path):
            messagebox.showerror("错误", f"输入文件夹不存在:\n{input_path}")
            return

        # 设置分析状态
        self.is_analyzing = True
        self.msg_reading_completed = False
        self.msg_read_btn.configure(state='disabled')
        self.copy_btn.configure(state='disabled')
        self.stop_btn.configure(state='normal')

        # 清空之前的结果
        self.clear_results()

        # 记录开始时间
        self.start_time = time.time()
        self.processed_files = 0

        # 启动分析线程
        self.analysis_thread = threading.Thread(
            target=self.analyze_folders,
            args=(input_path,),
            daemon=True
        )
        self.analysis_thread.start()

    def start_file_copy(self):
        """开始文件复制"""
        if not self.msg_reading_completed:
            messagebox.showwarning("警告", "请先完成MSG读取")
            return

        output_path = self.output_path_var.get().strip()
        if not output_path:
            messagebox.showerror("错误", "请选择目标文件夹")
            return

        if not self.analysis_results:
            messagebox.showwarning("警告", "没有可复制的数据")
            return

        # 设置复制状态
        self.is_analyzing = True
        self.copy_btn.configure(state='disabled')
        self.msg_read_btn.configure(state='disabled')
        self.stop_btn.configure(state='normal')

        # 记录开始时间
        self.start_time = time.time()
        self.processed_files = 0
        self.total_files = len(self.analysis_results)

        # 启动文件复制线程
        self.copy_thread = threading.Thread(
            target=self.copy_files,
            args=(output_path,),
            daemon=True
        )
        self.copy_thread.start()

    def stop_analysis(self):
        """停止分析"""
        self.is_analyzing = False
        self.progress_queue.put(("status", "正在停止分析..."))
        self.stop_btn.configure(state='disabled')

    def clear_results(self):
        """清空结果"""
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.analysis_results.clear()
        self.progress_bar['value'] = 0
        self.progress_bar['maximum'] = 100
        self.stats_var.set("统计信息: 等待分析...")
        self.progress_detail_var.set("")
        self.time_stats_var.set("")
        self.msg_reading_completed = False

    def analyze_folders(self, input_path):
        """分析文件夹（在后台线程中运行）"""
        try:
            # 扫描文件夹
            self.progress_queue.put(("status", "正在扫描文件夹..."))
            folders_to_process = []

            for root, dirs, files in os.walk(input_path):
                if not self.is_analyzing:
                    break
                if 'MSG.DAT' in files:
                    folders_to_process.append(root)

            total_folders = len(folders_to_process)
            if total_folders == 0:
                self.progress_queue.put(("status", "未找到包含MSG.DAT的文件夹"))
                self.progress_queue.put(("enable_button", None))
                return

            self.progress_queue.put(("status", f"找到 {total_folders} 个文件夹，开始分析..."))
            self.progress_queue.put(("progress_max", total_folders))

            # 处理每个文件夹
            self.total_files = total_folders
            successful_count = 0
            for index, folder_path in enumerate(folders_to_process, 1):
                if not self.is_analyzing:
                    break

                folder_name = os.path.basename(folder_path)
                self.processed_files = index - 1

                # 记录当前文件开始时间
                self.current_file_start_time = time.time()

                # 更新进度
                self.progress_queue.put(("progress", index))
                self.progress_queue.put(("status", f"正在处理: {folder_name} ({index}/{total_folders})"))
                self.progress_queue.put(("update_time", None))

                # 添加初始行到表格
                initial_data = {
                    'folder_path': folder_path,
                    'folder_name': folder_name,
                    'folder_aircraft': '',
                    'folder_date': '',
                    'msg_aircraft': '0%',
                    'msg_date': '',
                    'total_records': '',
                    'date_diff': '',
                    'final_aircraft': '',
                    'final_date': '',
                    'copy_progress': ''
                }
                self.data_queue.put(("add_row", initial_data))

                # 分析文件夹
                result = self.process_single_folder(folder_path, index, total_folders)

                if result:
                    self.data_queue.put(("update_row", (index - 1, result)))
                    self.analysis_results.append(result)
                    successful_count += 1
                else:
                    # 处理失败的情况
                    logger.warning(f"文件夹处理失败: {folder_name}")
                    failed_data = initial_data.copy()
                    failed_data.update({
                        'msg_aircraft': '处理失败',
                        'final_aircraft': '处理失败',
                        'copy_progress': ''
                    })
                    self.data_queue.put(("update_row", (index - 1, failed_data)))

                self.processed_files = index
                self.progress_queue.put(("update_time", None))

            if self.is_analyzing:
                self.progress_queue.put(("status", f"MSG读取完成！共处理 {successful_count}/{total_folders} 个文件夹"))
                self.progress_queue.put(("update_stats", None))
                self.progress_queue.put(("msg_completed", None))
            else:
                self.progress_queue.put(("status", f"分析已停止。已处理 {successful_count}/{index} 个文件夹"))

        except Exception as e:
            self.progress_queue.put(("status", f"分析过程中发生错误: {str(e)}"))
        finally:
            self.progress_queue.put(("enable_button", None))

    def process_single_folder(self, folder_path, file_index, total_files):
        """处理单个文件夹"""
        try:
            if not self.is_analyzing:
                return None

            folder_name = os.path.basename(folder_path)
            logger.info(f"开始处理文件夹: {folder_name}")

            # 提取文件夹信息
            folder_ac = extract_aircraft_info(folder_path)
            folder_date = extract_date_info(folder_path)
            logger.info(f"文件夹信息 - 飞机号: {folder_ac}, 日期: {folder_date}")

            # 查找MSG.DAT文件
            msg_dat_path = os.path.join(folder_path, 'MSG.DAT')
            if not os.path.exists(msg_dat_path):
                logger.warning(f"MSG.DAT文件不存在: {msg_dat_path}")
                return None

            if not os.access(msg_dat_path, os.R_OK):
                logger.warning(f"MSG.DAT文件无法读取: {msg_dat_path}")
                return None

            # 解析MSG.DAT文件（带进度更新）
            self.progress_queue.put(("detail", f"解析MSG.DAT文件..."))
            logger.info(f"开始解析MSG.DAT文件: {msg_dat_path}")

            # 解析MSG.DAT文件
            record, total_records = parse_mixed_format_file(msg_dat_path)

            # 简单的进度更新
            self.data_queue.put(("update_progress", (file_index - 1, "100%")))

            if not record:
                logger.warning(f"MSG.DAT文件解析失败，未找到有效记录: {msg_dat_path}")
                return None

            logger.info(f"MSG.DAT解析成功，找到 {total_records} 条记录，最新记录: {record}")

            # 转换时间
            beijing_date, beijing_time = convert_utc_to_beijing(record['date'], record['time'])
            if not beijing_date:
                logger.warning(f"时间转换失败: {record['date']} {record['time']}")
                return None

            logger.info(f"时间转换成功: {record['date']} {record['time']} -> {beijing_date} {beijing_time}")

            # 确定最终的飞机号和日期
            final_aircraft = record['ac'] if record['ac'] else (folder_ac or '')
            final_date = beijing_date
            logger.info(f"最终信息 - 飞机号: {final_aircraft}, 日期: {final_date}")

            # 检查匹配条件
            is_ac_match = False
            is_date_match = False

            if folder_ac and record['ac']:
                is_ac_match = (record['ac'].upper() == folder_ac.upper())

            if folder_date and beijing_date:
                is_date_match = is_date_within_week(folder_date, beijing_date)

            # 确定状态
            if folder_ac and folder_date:
                status = '一致' if (is_ac_match and is_date_match) else '不一致'
            elif folder_ac:
                status = '一致' if is_ac_match else '不一致'
            elif folder_date:
                status = '一致' if is_date_match else '不一致'
            else:
                status = '信息不足'

            logger.info(f"匹配结果 - 飞机号匹配: {is_ac_match}, 日期匹配: {is_date_match}, 状态: {status}")

            # 计算日期差
            date_diff = None
            if folder_date and beijing_date:
                try:
                    date_diff = abs((datetime.strptime(folder_date, '%Y-%m-%d') -
                                   datetime.strptime(beijing_date, '%Y-%m-%d')).days)
                except:
                    date_diff = 999

            result = {
                'folder_path': folder_path,
                'folder_name': os.path.basename(folder_path),
                'folder_aircraft': folder_ac or '',
                'folder_date': folder_date or '',
                'msg_aircraft': record['ac'] or '',
                'msg_date': beijing_date,
                'msg_time': beijing_time,
                'total_records': total_records,
                'status': status,
                'date_diff': date_diff,
                'final_aircraft': final_aircraft,
                'final_date': final_date,
                'copy_progress': ''
            }

            logger.info(f"文件夹处理成功: {folder_name}")
            return result

        except Exception as e:
            logger.error(f"处理文件夹失败: {folder_path}, 错误: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    # 移除重复的parse_msg_file_with_progress方法，使用parse_mixed_format_file替代

    # 移除重复的extract_records_with_progress方法，使用parse_mixed_format_file替代

    def copy_files(self, output_path):
        """复制文件"""
        try:
            self.progress_queue.put(("status", "开始文件复制..."))

            successful_count = 0
            for index, result in enumerate(self.analysis_results):
                if not self.is_analyzing:
                    break

                self.processed_files = index
                self.current_file_start_time = time.time()

                # 更新进度
                self.progress_queue.put(("progress", index + 1))
                self.progress_queue.put(("status", f"正在复制: {result['folder_name']} ({index + 1}/{len(self.analysis_results)})"))
                self.progress_queue.put(("update_time", None))

                # 开始复制单个文件夹
                success = self.copy_single_folder(result, output_path, index)
                if success:
                    successful_count += 1

                self.processed_files = index + 1
                self.progress_queue.put(("update_time", None))

            if self.is_analyzing:
                self.progress_queue.put(("status", f"文件复制完成！共复制 {successful_count}/{len(self.analysis_results)} 个文件夹"))
            else:
                self.progress_queue.put(("status", f"文件复制已停止。已复制 {successful_count}/{index + 1} 个文件夹"))

        except Exception as e:
            self.progress_queue.put(("status", f"文件复制过程中发生错误: {str(e)}"))
        finally:
            self.progress_queue.put(("enable_button", None))

    def copy_single_folder(self, result, output_path, row_index):
        """复制单个文件夹"""
        try:
            # 读取飞机号_BAK和日期_BAK
            ac_bak = result.get('final_aircraft', '').strip()
            date_bak = result.get('final_date', '').strip()

            if not ac_bak or not date_bak:
                self.data_queue.put(("update_copy_progress", (row_index, "信息不足")))
                return False

            # 解析年份
            try:
                year = date_bak.split('-')[0]
                month_day = date_bak.replace('-', '')[4:]  # 去掉年份和横线，得到mmdd
            except:
                self.data_queue.put(("update_copy_progress", (row_index, "日期格式错误")))
                return False

            # 构建目标路径
            target_base = os.path.join(output_path, year, ac_bak)
            target_folder = os.path.join(target_base, f"{year}-{month_day}")

            # 创建目标目录
            os.makedirs(target_folder, exist_ok=True)

            # 获取源文件夹路径
            source_folder = result['folder_path']

            # 扫描需要复制的文件和文件夹
            files_to_copy = []
            folders_to_copy = []

            for item in os.listdir(source_folder):
                item_path = os.path.join(source_folder, item)

                if os.path.isfile(item_path):
                    # 检查文件扩展名
                    ext = os.path.splitext(item)[1].upper()
                    if ext in ['.DAT', '.QAR', '.QA2']:
                        files_to_copy.append(item_path)
                elif os.path.isdir(item_path):
                    # 检查文件夹名称
                    if item.upper().endswith(('.REP', '.REC', '.QAR')):
                        folders_to_copy.append(item_path)

            total_items = len(files_to_copy) + len(folders_to_copy)
            if total_items == 0:
                self.data_queue.put(("update_copy_progress", (row_index, "无文件")))
                return True

            # 复制文件和文件夹
            copied_items = 0

            # 复制文件
            for file_path in files_to_copy:
                if not self.is_analyzing:
                    break

                filename = os.path.basename(file_path)
                target_file = os.path.join(target_folder, filename)

                try:
                    # 检查目标文件是否已存在
                    if os.path.exists(target_file):
                        print(f"跳过已存在的文件: {filename}")
                        copied_items += 1
                        # 更新进度
                        progress = int((copied_items / total_items) * 100)
                        self.data_queue.put(("update_copy_progress", (row_index, f"{progress}%")))
                        continue

                    shutil.copy2(file_path, target_file)
                    copied_items += 1

                    # 更新进度
                    progress = int((copied_items / total_items) * 100)
                    self.data_queue.put(("update_copy_progress", (row_index, f"{progress}%")))

                except Exception as e:
                    print(f"复制文件失败: {file_path} -> {target_file}, 错误: {e}")
                    copied_items += 1  # 即使失败也要更新进度

            # 复制文件夹
            for folder_path in folders_to_copy:
                if not self.is_analyzing:
                    break

                folder_name = os.path.basename(folder_path)
                target_subfolder = os.path.join(target_folder, folder_name)

                try:
                    # 检查目标文件夹是否已存在
                    if os.path.exists(target_subfolder):
                        print(f"跳过已存在的文件夹: {folder_name}")
                        copied_items += 1
                        # 更新进度
                        progress = int((copied_items / total_items) * 100)
                        self.data_queue.put(("update_copy_progress", (row_index, f"{progress}%")))
                        continue

                    shutil.copytree(folder_path, target_subfolder)
                    copied_items += 1

                    # 更新进度
                    progress = int((copied_items / total_items) * 100)
                    self.data_queue.put(("update_copy_progress", (row_index, f"{progress}%")))

                except Exception as e:
                    print(f"复制文件夹失败: {folder_path} -> {target_subfolder}, 错误: {e}")
                    copied_items += 1  # 即使失败也要更新进度

            if self.is_analyzing:
                self.data_queue.put(("update_copy_progress", (row_index, "完成")))
                return True
            else:
                self.data_queue.put(("update_copy_progress", (row_index, "中断")))
                return False

        except Exception as e:
            self.data_queue.put(("update_copy_progress", (row_index, "失败")))
            print(f"复制文件夹失败: {str(e)}")
            return False

    def on_item_double_click(self, event):
        """处理双击编辑事件"""
        if not self.tree.selection():
            return

        item = self.tree.selection()[0]
        column = self.tree.identify_column(event.x)

        # 只允许编辑飞机号_BAK和日期_BAK列
        if column in ['#8', '#9']:  # 对应final_aircraft和final_date列
            self.edit_cell(item, column)

    def edit_cell(self, item, column):
        """编辑单元格"""
        # 获取当前值
        current_value = self.tree.set(item, self.tree['columns'][int(column[1:]) - 1])

        # 创建编辑窗口
        edit_window = tk.Toplevel(self.root)
        edit_window.title("编辑")
        edit_window.geometry("300x100")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # 居中显示
        edit_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        # 创建输入框
        ttk.Label(edit_window, text="新值:").pack(pady=10)
        entry_var = tk.StringVar(value=current_value)
        entry = ttk.Entry(edit_window, textvariable=entry_var, width=30)
        entry.pack(pady=5)
        entry.focus()
        entry.select_range(0, tk.END)

        # 按钮框架
        btn_frame = ttk.Frame(edit_window)
        btn_frame.pack(pady=10)

        def save_edit():
            new_value = entry_var.get().strip()
            col_name = self.tree['columns'][int(column[1:]) - 1]
            self.tree.set(item, col_name, new_value)

            # 更新对应的分析结果
            item_index = self.tree.index(item)
            if item_index < len(self.analysis_results):
                if col_name == 'final_aircraft':
                    self.analysis_results[item_index]['final_aircraft'] = new_value
                elif col_name == 'final_date':
                    self.analysis_results[item_index]['final_date'] = new_value

            edit_window.destroy()

        def cancel_edit():
            edit_window.destroy()

        ttk.Button(btn_frame, text="保存", command=save_edit).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=cancel_edit).pack(side=tk.LEFT, padx=5)

        # 绑定回车键
        entry.bind('<Return>', lambda e: save_edit())

    def check_queue(self):
        """检查队列中的消息"""
        try:
            # 检查数据队列
            while True:
                try:
                    msg_type, data = self.data_queue.get_nowait()
                    if msg_type == "add_row":
                        self.add_tree_row(data)
                    elif msg_type == "update_row":
                        row_index, row_data = data
                        self.update_tree_row(row_index, row_data)
                    elif msg_type == "update_progress":
                        row_index, progress_text = data
                        self.update_progress_in_row(row_index, progress_text)
                    elif msg_type == "update_copy_progress":
                        row_index, progress_text = data
                        self.update_copy_progress_in_row(row_index, progress_text)
                except queue.Empty:
                    break

            # 检查进度队列
            while True:
                try:
                    msg_type, data = self.progress_queue.get_nowait()
                    if msg_type == "status":
                        self.status_var.set(data)
                    elif msg_type == "progress":
                        self.progress_bar['value'] = data
                    elif msg_type == "progress_max":
                        self.progress_bar['maximum'] = data
                    elif msg_type == "detail":
                        self.progress_detail_var.set(data)
                    elif msg_type == "enable_button":
                        self.msg_read_btn.configure(state='normal')
                        self.stop_btn.configure(state='disabled')
                        self.is_analyzing = False
                    elif msg_type == "msg_completed":
                        self.msg_reading_completed = True
                        self.copy_btn.configure(state='normal')
                        self.msg_read_btn.configure(state='disabled')
                    elif msg_type == "update_stats":
                        self.update_statistics()
                    elif msg_type == "update_time":
                        self.update_time_statistics()
                except queue.Empty:
                    break

        except Exception as e:
            print(f"Queue check error: {e}")

        # 继续检查
        self.root.after(100, self.check_queue)

    def add_tree_row(self, data):
        """添加新行到树形视图"""
        values = (
            data['folder_name'],
            data['folder_aircraft'],
            data['folder_date'],
            data['msg_aircraft'],
            data['msg_date'],
            data['total_records'],
            data['date_diff'],
            data['final_aircraft'],
            data['final_date'],
            data.get('copy_progress', '')
        )
        item = self.tree.insert('', 'end', values=values)

        # 滚动到最新添加的行
        self.tree.see(item)

    def update_tree_row(self, row_index, data):
        """更新树形视图中的行"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]

            values = (
                data['folder_name'],
                data['folder_aircraft'],
                data['folder_date'],
                data['msg_aircraft'],
                data['msg_date'],
                data['total_records'],
                data.get('date_diff', ''),
                data['final_aircraft'],
                data['final_date'],
                data.get('copy_progress', '')
            )

            self.tree.item(item, values=values)

            # 根据状态设置行颜色
            status = data.get('status', '')
            if status == '不一致':
                self.tree.item(item, tags=('inconsistent',))
            elif status == '一致':
                self.tree.item(item, tags=('consistent',))

    def update_progress_in_row(self, row_index, progress_text):
        """更新行中的进度显示"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]
            # 只更新MSG飞机号列显示进度
            current_values = list(self.tree.item(item, 'values'))
            current_values[3] = progress_text  # msg_aircraft列
            self.tree.item(item, values=current_values)

    def update_copy_progress_in_row(self, row_index, progress_text):
        """更新行中的复制进度显示"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]
            # 更新数据复制列显示进度
            current_values = list(self.tree.item(item, 'values'))
            current_values[9] = progress_text  # copy_progress列
            self.tree.item(item, values=current_values)

            # 自动滚动到当前正在复制的行，确保在窗口中可见
            self.tree.see(item)

            # 高亮当前正在处理的行
            if progress_text not in ['完成', '失败', '中断', '无文件', '信息不足', '日期格式错误']:
                # 如果是进度百分比，添加复制中的标记
                if progress_text.endswith('%') or progress_text == '0%':
                    self.tree.item(item, tags=('copying',))
            else:
                # 复制完成后移除复制中的标记，恢复原有状态
                current_tags = list(self.tree.item(item, 'tags'))
                if 'copying' in current_tags:
                    current_tags.remove('copying')
                    # 根据原有状态设置标记
                    folder_aircraft = current_values[1]  # 文件夹飞机号
                    msg_aircraft = current_values[3]     # MSG飞机号
                    if folder_aircraft and msg_aircraft:
                        # 检查是否一致
                        if folder_aircraft.upper() == msg_aircraft.upper():
                            current_tags.append('consistent')
                        else:
                            current_tags.append('inconsistent')
                    self.tree.item(item, tags=current_tags)

    def update_statistics(self):
        """更新统计信息"""
        total = len(self.analysis_results)
        if total == 0:
            self.stats_var.set("统计信息: 无数据")
            return

        consistent = sum(1 for r in self.analysis_results if r.get('status') == '一致')
        inconsistent = sum(1 for r in self.analysis_results if r.get('status') == '不一致')
        insufficient = sum(1 for r in self.analysis_results if r.get('status') == '信息不足')

        stats_text = f"统计信息: 总计 {total} 条 | 一致 {consistent} 条 | 不一致 {inconsistent} 条 | 信息不足 {insufficient} 条"
        self.stats_var.set(stats_text)

    def update_time_statistics(self):
        """更新时间统计信息"""
        if not self.start_time:
            return

        current_time = time.time()
        total_elapsed = current_time - self.start_time

        # 当前文件已用时间
        current_file_elapsed = 0
        if self.current_file_start_time:
            current_file_elapsed = current_time - self.current_file_start_time

        # 计算预估剩余时间
        remaining_time = 0
        if self.processed_files > 0 and self.total_files > 0:
            avg_time_per_file = total_elapsed / self.processed_files
            remaining_files = self.total_files - self.processed_files
            remaining_time = avg_time_per_file * remaining_files

        # 格式化时间显示
        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.0f}秒"
            elif seconds < 3600:
                return f"{seconds//60:.0f}分{seconds%60:.0f}秒"
            else:
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                return f"{hours:.0f}时{minutes:.0f}分"

        time_text = f"总用时: {format_time(total_elapsed)} | 预估剩余: {format_time(remaining_time)} | 当前文件: {format_time(current_file_elapsed)}"
        self.time_stats_var.set(time_text)

    def update_time_display(self):
        """定时更新时间显示"""
        if self.is_analyzing and self.start_time:
            self.update_time_statistics()

        # 每秒更新一次
        self.root.after(1000, self.update_time_display)

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_analyzing:
            if messagebox.askokcancel("确认", "分析正在进行中，确定要退出吗？"):
                self.is_analyzing = False
                self.root.destroy()
        else:
            self.root.destroy()

# ==================== GUI启动函数 ====================

def start_gui():
    """启动GUI界面"""
    # 创建主窗口
    root = tk.Tk()

    # 设置样式
    style = ttk.Style()
    style.theme_use('clam')  # 使用现代主题

    # 创建应用
    app = FlightAnalyzerGUI(root)

    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    start_gui()
