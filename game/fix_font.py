#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体修复工具 - 解决中文显示乱码问题
"""

import pygame
import sys
import os

def check_system_fonts():
    """检查系统字体"""
    print("=== 检查系统字体 ===")
    
    # 检查字体文件
    font_files = [
        ("黑体", "C:/Windows/Fonts/simhei.ttf"),
        ("微软雅黑", "C:/Windows/Fonts/msyh.ttf"),
        ("宋体", "C:/Windows/Fonts/simsun.ttc"),
        ("楷体", "C:/Windows/Fonts/kaiti.ttf"),
        ("微软雅黑粗体", "C:/Windows/Fonts/msyhbd.ttf"),
    ]
    
    available_files = []
    for name, path in font_files:
        if os.path.exists(path):
            print(f"✓ 找到字体文件: {name} - {path}")
            available_files.append((name, path))
        else:
            print(f"✗ 字体文件不存在: {name} - {path}")
    
    return available_files

def test_font_rendering():
    """测试字体渲染"""
    print("\n=== 测试字体渲染 ===")
    
    pygame.init()
    
    # 测试文本
    test_texts = [
        "吞食天地2",
        "开始游戏",
        "读取存档", 
        "游戏设置",
        "退出游戏",
        "刘备",
        "关羽",
        "张飞"
    ]
    
    # 获取可用字体文件
    available_files = check_system_fonts()
    
    if not available_files:
        print("✗ 没有找到任何中文字体文件")
        return None
    
    # 测试每个字体文件
    working_fonts = []
    
    for font_name, font_path in available_files:
        try:
            font = pygame.font.Font(font_path, 24)
            print(f"\n测试字体: {font_name}")
            
            all_success = True
            for text in test_texts:
                try:
                    surface = font.render(text, True, (255, 255, 255))
                    if surface.get_width() > 0:
                        print(f"  ✓ {text} (宽度: {surface.get_width()})")
                    else:
                        print(f"  ✗ {text} (渲染失败)")
                        all_success = False
                except Exception as e:
                    print(f"  ✗ {text} (错误: {e})")
                    all_success = False
            
            if all_success:
                working_fonts.append((font_name, font_path))
                print(f"  ✓ {font_name} 完全支持中文渲染")
            
        except Exception as e:
            print(f"✗ 无法加载字体 {font_name}: {e}")
    
    pygame.quit()
    return working_fonts

def create_fixed_ui():
    """创建修复后的UI文件"""
    print("\n=== 创建修复后的UI ===")
    
    working_fonts = test_font_rendering()
    
    if not working_fonts:
        print("✗ 没有找到可用的中文字体")
        return False
    
    # 选择最佳字体
    best_font = working_fonts[0]
    print(f"选择最佳字体: {best_font[0]} - {best_font[1]}")
    
    # 创建修复后的UI代码
    ui_fix_code = f'''
# 修复后的字体加载代码
def _load_best_chinese_font(self, size):
    """加载最佳中文字体"""
    # 使用测试确认的最佳字体
    best_font_path = r"{best_font[1]}"
    
    try:
        if os.path.exists(best_font_path):
            font = pygame.font.Font(best_font_path, size)
            # 测试渲染
            test_surface = font.render("测试", True, (255, 255, 255))
            if test_surface.get_width() > 0:
                return font
    except Exception as e:
        print(f"加载最佳字体失败: {{e}}")
    
    # 备用方案
    return pygame.font.Font(None, size)
'''
    
    print("修复代码已生成")
    return True

def apply_font_fix():
    """应用字体修复"""
    print("\n=== 应用字体修复 ===")
    
    # 读取当前UI文件
    ui_file = "ui.py"
    if not os.path.exists(ui_file):
        print(f"✗ UI文件不存在: {ui_file}")
        return False
    
    # 获取最佳字体
    working_fonts = test_font_rendering()
    if not working_fonts:
        print("✗ 没有找到可用字体")
        return False
    
    best_font_name, best_font_path = working_fonts[0]
    
    # 创建修复后的字体加载方法
    font_fix_method = f'''
    def _load_best_font(self, size):
        """加载最佳中文字体"""
        # 使用测试确认的字体: {best_font_name}
        font_path = r"{best_font_path}"
        
        try:
            if os.path.exists(font_path):
                font = pygame.font.Font(font_path, size)
                return font
        except Exception as e:
            print(f"加载字体失败: {{e}}")
        
        # 备用字体
        try:
            return pygame.font.SysFont("SimHei", size)
        except:
            return pygame.font.Font(None, size)
'''
    
    print(f"最佳字体: {best_font_name}")
    print(f"字体路径: {best_font_path}")
    print("请手动将以下代码添加到ui.py中:")
    print(font_fix_method)
    
    return True

def main():
    """主函数"""
    print("字体修复工具启动...")
    print("正在诊断中文字体显示问题...\n")
    
    # 检查系统
    available_fonts = check_system_fonts()
    
    if not available_fonts:
        print("\n❌ 系统中没有找到中文字体文件")
        print("建议:")
        print("1. 确保Windows系统完整安装")
        print("2. 检查C:/Windows/Fonts/目录")
        print("3. 重新安装中文语言包")
        return
    
    # 测试字体渲染
    working_fonts = test_font_rendering()
    
    if working_fonts:
        print(f"\n✅ 找到 {len(working_fonts)} 个可用的中文字体")
        print("推荐字体:")
        for i, (name, path) in enumerate(working_fonts, 1):
            print(f"  {i}. {name}")
        
        # 应用修复
        apply_font_fix()
        
        print("\n🔧 修复建议:")
        print("1. 重新运行游戏测试中文显示")
        print("2. 如果仍有问题，请检查pygame版本")
        print("3. 确保系统编码设置为UTF-8")
        
    else:
        print("\n❌ 所有字体都无法正常渲染中文")
        print("可能的原因:")
        print("1. pygame版本过旧")
        print("2. 系统字体损坏")
        print("3. Python编码设置问题")

if __name__ == "__main__":
    main()
