#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地图系统 - 世界地图、城市、移动
"""

from typing import Dict, List, Tuple, Optional
from enum import Enum
import random
import pygame

from battle import Enemy

class TerrainType(Enum):
    GRASS = "草地"
    FOREST = "森林"
    MOUNTAIN = "山地"
    WATER = "水域"
    CITY = "城市"
    CASTLE = "城堡"
    VILLAGE = "村庄"

class MapTile:
    """地图格子"""
    
    def __init__(self, x: int, y: int, terrain: TerrainType, name: str = ""):
        self.x = x
        self.y = y
        self.terrain = terrain
        self.name = name
        self.can_walk = terrain != TerrainType.WATER
        self.encounter_rate = self._get_encounter_rate()
        
        # 特殊属性
        self.is_city = terrain in [TerrainType.CITY, TerrainType.CASTLE, TerrainType.VILLAGE]
        self.city_data = None
        
        if self.is_city:
            self._init_city_data()
    
    def _get_encounter_rate(self) -> float:
        """获取遭遇敌人的概率"""
        rates = {
            TerrainType.GRASS: 0.1,
            TerrainType.FOREST: 0.15,
            TerrainType.MOUNTAIN: 0.2,
            TerrainType.WATER: 0.0,
            TerrainType.CITY: 0.0,
            TerrainType.CASTLE: 0.0,
            TerrainType.VILLAGE: 0.0,
        }
        return rates.get(self.terrain, 0.1)
    
    def _init_city_data(self):
        """初始化城市数据"""
        self.city_data = {
            'shops': True,
            'inn': True,
            'characters': [],  # 可招募的角色
            'quests': [],      # 任务
        }

class WorldMap:
    """世界地图"""
    
    def __init__(self, game_data):
        self.game_data = game_data
        self.width = 20
        self.height = 15
        self.tiles = {}
        
        # 玩家位置
        self.player_x = 10
        self.player_y = 7
        
        # 战斗相关
        self.pending_battle = None
        self.last_battle_pos = None
        
        # 生成地图
        self._generate_map()
        
        # 摄像机
        self.camera_x = 0
        self.camera_y = 0
        self.view_width = 15
        self.view_height = 11
    
    def _generate_map(self):
        """生成地图"""
        # 创建基础地形
        for y in range(self.height):
            for x in range(self.width):
                # 简单的地形生成算法
                terrain = self._get_terrain_for_position(x, y)
                self.tiles[(x, y)] = MapTile(x, y, terrain)
        
        # 添加城市
        cities = [
            (5, 3, TerrainType.CITY, "洛阳"),
            (15, 5, TerrainType.CITY, "长安"),
            (3, 10, TerrainType.VILLAGE, "新野"),
            (17, 10, TerrainType.CASTLE, "成都"),
            (10, 2, TerrainType.VILLAGE, "徐州"),
        ]
        
        for x, y, terrain, name in cities:
            if (x, y) in self.tiles:
                self.tiles[(x, y)] = MapTile(x, y, terrain, name)
    
    def _get_terrain_for_position(self, x: int, y: int) -> TerrainType:
        """根据位置获取地形类型"""
        # 简单的地形生成
        if x == 0 or x == self.width - 1 or y == 0 or y == self.height - 1:
            return TerrainType.MOUNTAIN
        
        # 使用简单的噪声函数
        noise = (x * 7 + y * 11) % 100
        
        if noise < 60:
            return TerrainType.GRASS
        elif noise < 80:
            return TerrainType.FOREST
        else:
            return TerrainType.MOUNTAIN
    
    def move_player(self, dx: int, dy: int) -> bool:
        """移动玩家"""
        new_x = self.player_x + dx
        new_y = self.player_y + dy
        
        # 检查边界
        if not (0 <= new_x < self.width and 0 <= new_y < self.height):
            return False
        
        # 检查是否可以行走
        tile = self.tiles.get((new_x, new_y))
        if not tile or not tile.can_walk:
            return False
        
        # 移动玩家
        self.player_x = new_x
        self.player_y = new_y
        
        # 更新摄像机
        self._update_camera()
        
        # 检查随机遭遇
        self._check_random_encounter(tile)
        
        return True
    
    def _update_camera(self):
        """更新摄像机位置"""
        # 让摄像机跟随玩家
        self.camera_x = max(0, min(self.width - self.view_width, 
                                  self.player_x - self.view_width // 2))
        self.camera_y = max(0, min(self.height - self.view_height, 
                                  self.player_y - self.view_height // 2))
    
    def _check_random_encounter(self, tile: MapTile):
        """检查随机遭遇"""
        # 避免在同一位置连续遭遇
        current_pos = (self.player_x, self.player_y)
        if current_pos == self.last_battle_pos:
            return
        
        if random.random() < tile.encounter_rate:
            self._trigger_random_battle(tile)
    
    def _trigger_random_battle(self, tile: MapTile):
        """触发随机战斗"""
        # 根据地形生成不同的敌人
        enemy_count = random.randint(1, 3)
        enemies = []
        
        for _ in range(enemy_count):
            enemy_level = random.randint(1, 5)
            enemy_name = self._get_random_enemy_name(tile.terrain)
            enemy_type = "soldier"
            
            if random.random() < 0.1:  # 10%概率遇到精英
                enemy_type = "elite"
                enemy_name = f"精英{enemy_name}"
            
            enemies.append(Enemy(enemy_name, enemy_level, enemy_type))
        
        self.pending_battle = {
            'enemies': enemies,
            'terrain': tile.terrain
        }
        
        self.last_battle_pos = (self.player_x, self.player_y)
    
    def _get_random_enemy_name(self, terrain: TerrainType) -> str:
        """根据地形获取随机敌人名称"""
        enemy_names = {
            TerrainType.GRASS: ["流寇", "山贼", "野兽"],
            TerrainType.FOREST: ["森林盗贼", "野狼", "熊"],
            TerrainType.MOUNTAIN: ["山贼", "强盗", "猛虎"],
        }
        
        names = enemy_names.get(terrain, ["敌兵", "盗贼"])
        return random.choice(names)
    
    def handle_confirm(self):
        """处理确认操作"""
        current_tile = self.tiles.get((self.player_x, self.player_y))
        
        if current_tile and current_tile.is_city:
            self._enter_city(current_tile)
    
    def _enter_city(self, tile: MapTile):
        """进入城市"""
        print(f"进入 {tile.name}")
        # 这里可以触发城市界面
        # 暂时只是打印信息
    
    def should_enter_battle(self) -> bool:
        """是否应该进入战斗"""
        return self.pending_battle is not None
    
    def get_battle_data(self) -> Optional[Dict]:
        """获取战斗数据"""
        battle_data = self.pending_battle
        self.pending_battle = None
        return battle_data
    
    def handle_battle_result(self, result: Dict):
        """处理战斗结果"""
        if result['result'] == 'victory':
            print(f"战斗胜利! 获得 {result.get('exp', 0)} 经验值")
        elif result['result'] == 'defeat':
            print("战斗失败...")
            # 可以实现复活机制或者游戏结束
        elif result['result'] == 'escaped':
            print("成功逃脱")
    
    def update(self):
        """更新地图逻辑"""
        pass
    
    def get_current_tile(self) -> Optional[MapTile]:
        """获取当前位置的地图格子"""
        return self.tiles.get((self.player_x, self.player_y))
    
    def get_visible_tiles(self) -> List[Tuple[int, int, MapTile]]:
        """获取可见的地图格子"""
        visible_tiles = []
        
        for y in range(self.camera_y, min(self.camera_y + self.view_height, self.height)):
            for x in range(self.camera_x, min(self.camera_x + self.view_width, self.width)):
                tile = self.tiles.get((x, y))
                if tile:
                    visible_tiles.append((x, y, tile))
        
        return visible_tiles
    
    def get_terrain_char(self, terrain: TerrainType) -> str:
        """获取地形显示字符"""
        chars = {
            TerrainType.GRASS: ".",
            TerrainType.FOREST: "T",
            TerrainType.MOUNTAIN: "^",
            TerrainType.WATER: "~",
            TerrainType.CITY: "C",
            TerrainType.CASTLE: "H",
            TerrainType.VILLAGE: "V",
        }
        return chars.get(terrain, "?")
    
    def get_terrain_color(self, terrain: TerrainType) -> Tuple[int, int, int]:
        """获取地形颜色"""
        colors = {
            TerrainType.GRASS: (34, 139, 34),      # 森林绿
            TerrainType.FOREST: (0, 100, 0),       # 深绿
            TerrainType.MOUNTAIN: (139, 69, 19),   # 棕色
            TerrainType.WATER: (0, 191, 255),      # 深天蓝
            TerrainType.CITY: (255, 215, 0),       # 金色
            TerrainType.CASTLE: (128, 0, 128),     # 紫色
            TerrainType.VILLAGE: (255, 165, 0),    # 橙色
        }
        return colors.get(terrain, (128, 128, 128))
    
    def render(self, screen: pygame.Surface):
        """渲染地图"""
        # 基础渲染，具体的渲染由UI系统处理
        pass
