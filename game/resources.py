#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理 - 图像、音效资源加载
"""

import pygame
import os
from typing import Dict, Optional, Tuple
from enum import Enum

class ResourceType(Enum):
    IMAGE = "image"
    SOUND = "sound"
    MUSIC = "music"
    FONT = "font"

class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        # 资源缓存
        self.images = {}
        self.sounds = {}
        self.music = {}
        self.fonts = {}
        
        # 资源路径
        self.resource_paths = {
            "images": "assets/images",
            "sounds": "assets/sounds", 
            "music": "assets/music",
            "fonts": "assets/fonts"
        }
        
        # 确保资源目录存在
        self._ensure_directories()
        
        # 初始化pygame音频
        try:
            pygame.mixer.init()
            self.sound_enabled = True
        except:
            print("警告: 无法初始化音频系统")
            self.sound_enabled = False
        
        # 加载默认资源
        self._load_default_resources()
    
    def _ensure_directories(self):
        """确保资源目录存在"""
        for path in self.resource_paths.values():
            if not os.path.exists(path):
                os.makedirs(path)
    
    def _load_default_resources(self):
        """加载默认资源"""
        # 创建默认的像素风格图像
        self._create_default_images()
        
        # 加载默认字体
        self._load_default_fonts()
    
    def _create_default_images(self):
        """创建默认图像"""
        # 创建基础的像素风格图像
        
        # 玩家角色图像 (16x16)
        player_surface = pygame.Surface((16, 16))
        player_surface.fill((255, 0, 0))  # 红色
        pygame.draw.circle(player_surface, (255, 255, 255), (8, 6), 3)  # 头部
        pygame.draw.rect(player_surface, (0, 0, 255), (6, 9, 4, 7))     # 身体
        self.images["player"] = player_surface
        
        # 敌人图像 (16x16)
        enemy_surface = pygame.Surface((16, 16))
        enemy_surface.fill((128, 0, 128))  # 紫色
        pygame.draw.circle(enemy_surface, (255, 255, 255), (8, 6), 3)   # 头部
        pygame.draw.rect(enemy_surface, (64, 64, 64), (6, 9, 4, 7))     # 身体
        self.images["enemy"] = enemy_surface
        
        # 地形图像 (32x32)
        tile_size = 32
        
        # 草地
        grass_surface = pygame.Surface((tile_size, tile_size))
        grass_surface.fill((34, 139, 34))
        for i in range(0, tile_size, 4):
            for j in range(0, tile_size, 4):
                if (i + j) % 8 == 0:
                    pygame.draw.rect(grass_surface, (0, 100, 0), (i, j, 2, 2))
        self.images["grass"] = grass_surface
        
        # 森林
        forest_surface = pygame.Surface((tile_size, tile_size))
        forest_surface.fill((0, 100, 0))
        pygame.draw.circle(forest_surface, (34, 139, 34), (16, 16), 12)
        pygame.draw.rect(forest_surface, (139, 69, 19), (14, 20, 4, 8))
        self.images["forest"] = forest_surface
        
        # 山地
        mountain_surface = pygame.Surface((tile_size, tile_size))
        mountain_surface.fill((139, 69, 19))
        points = [(16, 4), (8, 28), (24, 28)]
        pygame.draw.polygon(mountain_surface, (160, 82, 45), points)
        self.images["mountain"] = mountain_surface
        
        # 水域
        water_surface = pygame.Surface((tile_size, tile_size))
        water_surface.fill((0, 191, 255))
        for i in range(0, tile_size, 8):
            pygame.draw.line(water_surface, (135, 206, 250), (0, i), (tile_size, i), 2)
        self.images["water"] = water_surface
        
        # 城市
        city_surface = pygame.Surface((tile_size, tile_size))
        city_surface.fill((255, 215, 0))
        pygame.draw.rect(city_surface, (139, 69, 19), (8, 8, 16, 16))
        pygame.draw.polygon(city_surface, (255, 0, 0), [(16, 8), (12, 4), (20, 4)])
        self.images["city"] = city_surface
        
        # 城堡
        castle_surface = pygame.Surface((tile_size, tile_size))
        castle_surface.fill((128, 0, 128))
        pygame.draw.rect(castle_surface, (64, 64, 64), (6, 10, 20, 16))
        pygame.draw.rect(castle_surface, (32, 32, 32), (4, 8, 6, 8))
        pygame.draw.rect(castle_surface, (32, 32, 32), (22, 8, 6, 8))
        self.images["castle"] = castle_surface
        
        # 村庄
        village_surface = pygame.Surface((tile_size, tile_size))
        village_surface.fill((255, 165, 0))
        pygame.draw.rect(village_surface, (139, 69, 19), (10, 12, 12, 12))
        pygame.draw.polygon(village_surface, (255, 0, 0), [(16, 12), (10, 6), (22, 6)])
        self.images["village"] = village_surface
        
        # UI元素
        # 按钮背景
        button_surface = pygame.Surface((100, 30))
        button_surface.fill((64, 64, 64))
        pygame.draw.rect(button_surface, (128, 128, 128), (0, 0, 100, 30), 2)
        self.images["button"] = button_surface
        
        # 选中的按钮背景
        button_selected_surface = pygame.Surface((100, 30))
        button_selected_surface.fill((128, 128, 0))
        pygame.draw.rect(button_selected_surface, (255, 255, 0), (0, 0, 100, 30), 2)
        self.images["button_selected"] = button_selected_surface
    
    def _load_default_fonts(self):
        """加载默认字体"""
        try:
            # 尝试加载系统字体
            self.fonts["default_small"] = pygame.font.Font(None, 18)
            self.fonts["default_medium"] = pygame.font.Font(None, 24)
            self.fonts["default_large"] = pygame.font.Font(None, 36)

            # 尝试加载中文字体
            chinese_font = self._load_chinese_font(24)
            if chinese_font:
                self.fonts["chinese"] = chinese_font
                self.fonts["chinese_small"] = self._load_chinese_font(18)
                self.fonts["chinese_large"] = self._load_chinese_font(36)
            else:
                # 如果没有找到中文字体，使用默认字体
                self.fonts["chinese"] = self.fonts["default_medium"]
                self.fonts["chinese_small"] = self.fonts["default_small"]
                self.fonts["chinese_large"] = self.fonts["default_large"]

        except Exception as e:
            print(f"加载字体失败: {e}")
            # 使用pygame默认字体
            self.fonts["default_small"] = pygame.font.Font(None, 18)
            self.fonts["default_medium"] = pygame.font.Font(None, 24)
            self.fonts["default_large"] = pygame.font.Font(None, 36)
            self.fonts["chinese"] = pygame.font.Font(None, 24)
            self.fonts["chinese_small"] = pygame.font.Font(None, 18)
            self.fonts["chinese_large"] = pygame.font.Font(None, 36)

    def _load_chinese_font(self, size):
        """加载中文字体 - 优先使用宋体"""
        # 直接使用宋体字体文件（测试确认可用）
        simsun_path = "C:/Windows/Fonts/simsun.ttc"

        try:
            if os.path.exists(simsun_path):
                font = pygame.font.Font(simsun_path, size)
                # 测试字体是否支持中文
                test_surface = font.render("测试", True, (255, 255, 255))
                if test_surface.get_width() > 0:
                    print(f"成功加载宋体字体文件: {simsun_path} (大小: {size})")
                    return font
        except Exception as e:
            print(f"加载宋体字体文件失败: {e}")

        # 备用方案：尝试系统宋体
        try:
            font = pygame.font.SysFont("SimSun", size)
            test_surface = font.render("测试", True, (255, 255, 255))
            if test_surface.get_width() > 0:
                print(f"成功加载系统宋体字体 (大小: {size})")
                return font
        except Exception as e:
            print(f"加载系统宋体失败: {e}")

        print(f"警告: 无法加载大小为{size}的中文字体，将使用默认字体")
        return None
    
    def load_image(self, name: str, filename: str) -> bool:
        """加载图像文件"""
        if name in self.images:
            return True
        
        file_path = os.path.join(self.resource_paths["images"], filename)
        
        if not os.path.exists(file_path):
            print(f"图像文件不存在: {file_path}")
            return False
        
        try:
            image = pygame.image.load(file_path)
            self.images[name] = image
            print(f"加载图像: {name}")
            return True
        except Exception as e:
            print(f"加载图像失败 {name}: {e}")
            return False
    
    def get_image(self, name: str) -> Optional[pygame.Surface]:
        """获取图像"""
        return self.images.get(name)
    
    def load_sound(self, name: str, filename: str) -> bool:
        """加载音效文件"""
        if not self.sound_enabled:
            return False
        
        if name in self.sounds:
            return True
        
        file_path = os.path.join(self.resource_paths["sounds"], filename)
        
        if not os.path.exists(file_path):
            print(f"音效文件不存在: {file_path}")
            return False
        
        try:
            sound = pygame.mixer.Sound(file_path)
            self.sounds[name] = sound
            print(f"加载音效: {name}")
            return True
        except Exception as e:
            print(f"加载音效失败 {name}: {e}")
            return False
    
    def play_sound(self, name: str, volume: float = 1.0):
        """播放音效"""
        if not self.sound_enabled or name not in self.sounds:
            return
        
        try:
            sound = self.sounds[name]
            sound.set_volume(volume)
            sound.play()
        except Exception as e:
            print(f"播放音效失败 {name}: {e}")
    
    def load_music(self, name: str, filename: str) -> bool:
        """加载背景音乐"""
        if not self.sound_enabled:
            return False
        
        file_path = os.path.join(self.resource_paths["music"], filename)
        
        if not os.path.exists(file_path):
            print(f"音乐文件不存在: {file_path}")
            return False
        
        try:
            self.music[name] = file_path
            print(f"加载音乐: {name}")
            return True
        except Exception as e:
            print(f"加载音乐失败 {name}: {e}")
            return False
    
    def play_music(self, name: str, loops: int = -1, volume: float = 0.7):
        """播放背景音乐"""
        if not self.sound_enabled or name not in self.music:
            return
        
        try:
            pygame.mixer.music.load(self.music[name])
            pygame.mixer.music.set_volume(volume)
            pygame.mixer.music.play(loops)
        except Exception as e:
            print(f"播放音乐失败 {name}: {e}")
    
    def stop_music(self):
        """停止背景音乐"""
        if self.sound_enabled:
            pygame.mixer.music.stop()
    
    def get_font(self, name: str) -> Optional[pygame.font.Font]:
        """获取字体"""
        return self.fonts.get(name)
    
    def create_text_surface(self, text: str, font_name: str = "chinese", 
                           color: Tuple[int, int, int] = (255, 255, 255)) -> Optional[pygame.Surface]:
        """创建文本表面"""
        font = self.get_font(font_name)
        if not font:
            font = self.get_font("default_medium")
        
        if not font:
            return None
        
        try:
            return font.render(text, True, color)
        except Exception as e:
            print(f"创建文本表面失败: {e}")
            return None
    
    def cleanup(self):
        """清理资源"""
        self.images.clear()
        self.sounds.clear()
        self.music.clear()
        self.fonts.clear()
        
        if self.sound_enabled:
            pygame.mixer.quit()
