#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏状态 - 存档、读档、游戏进度
"""

import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime

class GameState:
    """游戏状态管理类"""
    
    def __init__(self):
        self.save_directory = "saves"
        self.current_save_slot = 1
        self.max_save_slots = 10
        
        # 游戏状态数据
        self.game_data = {
            "player_name": "玩家",
            "play_time": 0,  # 游戏时间（秒）
            "current_chapter": 1,
            "story_progress": {},
            "player_position": {"x": 10, "y": 7, "map": "world"},
            "party_data": [],
            "inventory": {},
            "gold": 1000,
            "flags": {},  # 游戏标志位
            "visited_cities": [],
            "completed_battles": [],
            "unlocked_characters": ["刘备", "关羽", "张飞"],
        }
        
        # 确保存档目录存在
        self._ensure_save_directory()
    
    def _ensure_save_directory(self):
        """确保存档目录存在"""
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
    
    def save_game(self, slot: int = None) -> bool:
        """保存游戏"""
        if slot is None:
            slot = self.current_save_slot
        
        if not (1 <= slot <= self.max_save_slots):
            print(f"无效的存档槽位: {slot}")
            return False
        
        try:
            # 更新保存时间
            self.game_data["save_time"] = datetime.now().isoformat()
            self.game_data["save_slot"] = slot
            
            # 保存文件路径
            save_file = os.path.join(self.save_directory, f"save_{slot:02d}.json")
            
            # 写入文件
            with open(save_file, 'w', encoding='utf-8') as f:
                json.dump(self.game_data, f, ensure_ascii=False, indent=2)
            
            print(f"游戏已保存到槽位 {slot}")
            return True
            
        except Exception as e:
            print(f"保存游戏失败: {e}")
            return False
    
    def load_game(self, slot: int) -> bool:
        """读取游戏"""
        if not (1 <= slot <= self.max_save_slots):
            print(f"无效的存档槽位: {slot}")
            return False
        
        save_file = os.path.join(self.save_directory, f"save_{slot:02d}.json")
        
        if not os.path.exists(save_file):
            print(f"存档文件不存在: {save_file}")
            return False
        
        try:
            with open(save_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            
            # 验证存档数据
            if self._validate_save_data(loaded_data):
                self.game_data = loaded_data
                self.current_save_slot = slot
                print(f"游戏已从槽位 {slot} 读取")
                return True
            else:
                print("存档数据损坏")
                return False
                
        except Exception as e:
            print(f"读取游戏失败: {e}")
            return False
    
    def _validate_save_data(self, data: Dict[str, Any]) -> bool:
        """验证存档数据的完整性"""
        required_keys = [
            "player_name", "play_time", "current_chapter",
            "player_position", "party_data", "inventory", "gold"
        ]
        
        for key in required_keys:
            if key not in data:
                print(f"存档数据缺少必要字段: {key}")
                return False
        
        return True
    
    def get_save_info(self, slot: int) -> Optional[Dict[str, Any]]:
        """获取存档信息"""
        if not (1 <= slot <= self.max_save_slots):
            return None
        
        save_file = os.path.join(self.save_directory, f"save_{slot:02d}.json")
        
        if not os.path.exists(save_file):
            return None
        
        try:
            with open(save_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取关键信息
            save_info = {
                "slot": slot,
                "player_name": data.get("player_name", "未知"),
                "chapter": data.get("current_chapter", 1),
                "play_time": data.get("play_time", 0),
                "save_time": data.get("save_time", "未知"),
                "level": self._get_party_average_level(data.get("party_data", [])),
                "location": self._get_location_name(data.get("player_position", {})),
            }
            
            return save_info
            
        except Exception as e:
            print(f"读取存档信息失败: {e}")
            return None
    
    def _get_party_average_level(self, party_data: list) -> int:
        """获取队伍平均等级"""
        if not party_data:
            return 1
        
        total_level = sum(char.get("level", 1) for char in party_data)
        return total_level // len(party_data)
    
    def _get_location_name(self, position: Dict[str, Any]) -> str:
        """获取位置名称"""
        map_name = position.get("map", "world")
        x = position.get("x", 0)
        y = position.get("y", 0)
        
        # 这里可以根据坐标查找具体的地点名称
        # 暂时返回坐标信息
        return f"{map_name} ({x}, {y})"
    
    def list_saves(self) -> List[Dict[str, Any]]:
        """列出所有存档"""
        saves = []
        
        for slot in range(1, self.max_save_slots + 1):
            save_info = self.get_save_info(slot)
            if save_info:
                saves.append(save_info)
            else:
                saves.append({
                    "slot": slot,
                    "empty": True
                })
        
        return saves
    
    def delete_save(self, slot: int) -> bool:
        """删除存档"""
        if not (1 <= slot <= self.max_save_slots):
            return False
        
        save_file = os.path.join(self.save_directory, f"save_{slot:02d}.json")
        
        if os.path.exists(save_file):
            try:
                os.remove(save_file)
                print(f"存档槽位 {slot} 已删除")
                return True
            except Exception as e:
                print(f"删除存档失败: {e}")
                return False
        
        return True
    
    def update_player_position(self, x: int, y: int, map_name: str = "world"):
        """更新玩家位置"""
        self.game_data["player_position"] = {
            "x": x,
            "y": y,
            "map": map_name
        }
    
    def get_player_position(self) -> Dict[str, Any]:
        """获取玩家位置"""
        return self.game_data["player_position"].copy()
    
    def update_party_data(self, party_data: list):
        """更新队伍数据"""
        self.game_data["party_data"] = party_data
    
    def get_party_data(self) -> list:
        """获取队伍数据"""
        return self.game_data["party_data"].copy()
    
    def add_gold(self, amount: int):
        """增加金币"""
        self.game_data["gold"] = max(0, self.game_data["gold"] + amount)
    
    def get_gold(self) -> int:
        """获取金币数量"""
        return self.game_data["gold"]
    
    def add_item(self, item_name: str, quantity: int = 1):
        """添加物品"""
        if item_name in self.game_data["inventory"]:
            self.game_data["inventory"][item_name] += quantity
        else:
            self.game_data["inventory"][item_name] = quantity
    
    def remove_item(self, item_name: str, quantity: int = 1) -> bool:
        """移除物品"""
        if item_name not in self.game_data["inventory"]:
            return False
        
        if self.game_data["inventory"][item_name] < quantity:
            return False
        
        self.game_data["inventory"][item_name] -= quantity
        
        if self.game_data["inventory"][item_name] <= 0:
            del self.game_data["inventory"][item_name]
        
        return True
    
    def get_item_count(self, item_name: str) -> int:
        """获取物品数量"""
        return self.game_data["inventory"].get(item_name, 0)
    
    def get_inventory(self) -> Dict[str, int]:
        """获取背包内容"""
        return self.game_data["inventory"].copy()
    
    def set_flag(self, flag_name: str, value: Any):
        """设置游戏标志"""
        self.game_data["flags"][flag_name] = value
    
    def get_flag(self, flag_name: str, default: Any = None) -> Any:
        """获取游戏标志"""
        return self.game_data["flags"].get(flag_name, default)
    
    def add_visited_city(self, city_name: str):
        """添加已访问城市"""
        if city_name not in self.game_data["visited_cities"]:
            self.game_data["visited_cities"].append(city_name)
    
    def is_city_visited(self, city_name: str) -> bool:
        """检查城市是否已访问"""
        return city_name in self.game_data["visited_cities"]
    
    def unlock_character(self, character_name: str):
        """解锁角色"""
        if character_name not in self.game_data["unlocked_characters"]:
            self.game_data["unlocked_characters"].append(character_name)
    
    def is_character_unlocked(self, character_name: str) -> bool:
        """检查角色是否已解锁"""
        return character_name in self.game_data["unlocked_characters"]
    
    def add_play_time(self, seconds: int):
        """增加游戏时间"""
        self.game_data["play_time"] += seconds
    
    def get_play_time_formatted(self) -> str:
        """获取格式化的游戏时间"""
        total_seconds = self.game_data["play_time"]
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def advance_chapter(self):
        """推进章节"""
        self.game_data["current_chapter"] += 1
    
    def get_current_chapter(self) -> int:
        """获取当前章节"""
        return self.game_data["current_chapter"]
