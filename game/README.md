# 游戏集合 (Game Collection)

这个目录包含了多个使用Python和Pygame开发的经典游戏。

## 游戏列表

### ⚔️ 吞食天地2 (Sangokushi 2) - 新增！
- **文件**: `sangokushi2.py`
- **启动**: `python sangokushi2.py` 或运行 `启动吞食天地2.bat`
- **特色功能**:
  - 🏛️ 经典三国题材RPG游戏
  - ⚔️ 回合制战斗系统
  - 👥 角色培养和装备系统
  - 🗺️ 世界地图探索
  - 🏰 城市访问和角色招募
  - 💾 完整的存档系统
  - 🎨 复古像素风格

### 🧩 俄罗斯方块 (Tetris)
- **文件**: `tetris.py` / `俄罗斯方块.py`
- **启动**: `python tetris.py` 或运行 `启动俄罗斯方块.bat`
- **特色功能**:
  - 🎯 七种标准方块类型 (I, O, T, S, Z, J, L)
  - 🔄 方块旋转和移动
  - 📊 计分系统和等级系统
  - 👀 下一个方块预览
  - ⏸️ 暂停/继续功能

### 🐍 贪吃蛇 (Snake Game)
- **文件**: `贪吃蛇.py`
- **启动**: `python 贪吃蛇.py` 或运行 `启动贪吃蛇.bat`
- **特色功能**:
  - 🎮 经典贪吃蛇游戏体验
  - 🍎 随机食物生成
  - 📊 实时分数和长度显示
  - ⏸️ 游戏暂停功能
  - 🔄 重新开始功能
  - 🎨 美观的网格界面设计

## 安装要求

- Python 3.6+
- Pygame 2.0+

## 安装步骤

1. 确保已安装Python 3.6或更高版本
2. 安装Pygame库：
   ```bash
   pip install pygame
   ```
   或者使用requirements.txt：
   ```bash
   pip install -r requirements.txt
   ```

## 运行游戏

### 俄罗斯方块
```bash
python tetris.py
# 或
python 俄罗斯方块.py
# 或双击
启动俄罗斯方块.bat
```

### 吞食天地2
```bash
python sangokushi2.py
# 或双击
启动吞食天地2.bat
# 运行测试
python test_sangokushi.py
```

### 贪吃蛇
```bash
python 贪吃蛇.py
# 或双击
启动贪吃蛇.bat
```

## 游戏控制

### 吞食天地2控制
| 按键 | 功能 |
|------|------|
| W/S 或 ↑/↓ | 菜单选择/地图移动 |
| A/D 或 ←/→ | 地图移动 |
| SPACE 或 ENTER | 确认/进入 |
| ESC | 返回/退出 |

### 俄罗斯方块控制
| 按键 | 功能 |
|------|------|
| A 或 ← | 向左移动 |
| D 或 → | 向右移动 |
| S 或 ↓ | 软降（加速下降） |
| W 或 ↑ | 旋转方块 |
| SPACE | 硬降（瞬间下降到底部） |
| P | 暂停/继续游戏 |
| R | 重新开始游戏 |

### 贪吃蛇控制
| 按键 | 功能 |
|------|------|
| W 或 ↑ | 向上移动 |
| S 或 ↓ | 向下移动 |
| A 或 ← | 向左移动 |
| D 或 → | 向右移动 |
| SPACE | 暂停/继续游戏 |
| R | 重新开始游戏 |

## 游戏规则

### 俄罗斯方块规则
1. **目标**：通过移动和旋转下降的方块，填满水平行来消除它们
2. **计分**：
   - 单行消除：100分 × 当前等级
   - 双行消除：300分 × 当前等级
   - 三行消除：500分 × 当前等级
   - 四行消除：800分 × 当前等级
   - 硬降奖励：每格2分
3. **升级**：每消除10行升一级，等级越高下降速度越快
4. **游戏结束**：当新方块无法放置时游戏结束

### 贪吃蛇规则
1. **目标**：控制蛇吃食物，让蛇变得越来越长
2. **计分**：每吃一个食物得10分
3. **游戏结束**：蛇撞到墙壁或撞到自己的身体时游戏结束
4. **移动**：蛇会持续向当前方向移动，不能反向移动

## 文件结构

```
game/
├── tetris.py           # 俄罗斯方块主游戏文件
├── 俄罗斯方块.py        # 俄罗斯方块启动器
├── 启动俄罗斯方块.bat   # 俄罗斯方块批处理启动器
├── 贪吃蛇.py           # 贪吃蛇游戏文件
├── 启动贪吃蛇.bat      # 贪吃蛇批处理启动器
├── test_tetris.py      # 俄罗斯方块测试文件
├── test_snake.py       # 贪吃蛇测试文件
├── requirements.txt    # 依赖包列表
└── README.md          # 说明文档
```

## 技术特性

- 面向对象设计
- 模块化代码结构
- 流畅的游戏体验
- 标准的游戏机制实现
- 清晰的代码注释
- 完整的单元测试

## 测试

运行游戏测试：
```bash
# 测试俄罗斯方块
python test_tetris.py

# 测试贪吃蛇
python test_snake.py
```

## 开发计划

后续优化方向：
- [ ] 添加音效和背景音乐
- [ ] 添加更多视觉效果
- [ ] 实现保存最高分功能
- [ ] 添加不同难度模式
- [ ] 优化界面美观度
- [ ] 添加更多经典游戏（如扫雷、推箱子等）

## 许可证

本项目仅供学习和娱乐使用。

---

享受游戏吧！🎮
