#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
吞食天地2数据提取器 - 基于ROM分析结果
"""

import os
import struct
from typing import List, Dict, Tuple

class SangokushiDataExtractor:
    """吞食天地2数据提取器"""
    
    def __init__(self, rom_path: str):
        self.rom_path = rom_path
        self.prg_data = None
        self.chr_data = None
        self._load_rom()
    
    def _load_rom(self):
        """加载ROM数据"""
        with open(self.rom_path, 'rb') as f:
            # 读取文件头
            header = f.read(16)
            
            # 跳过trainer（如果存在）
            if header[6] & 4:
                f.read(512)
            
            # 读取PRG-ROM数据
            prg_size = header[4] * 16384
            self.prg_data = f.read(prg_size)
            
            # 读取CHR-ROM数据（如果存在）
            chr_size = header[5] * 8192
            if chr_size > 0:
                self.chr_data = f.read(chr_size)
            
            print(f"ROM加载完成: PRG={len(self.prg_data)} bytes, CHR={len(self.chr_data) if self.chr_data else 0} bytes")
    
    def extract_map_data(self) -> List[Dict]:
        """提取地图数据"""
        print("=== 提取地图数据 ===")
        
        # 基于分析结果，重点关注这些偏移位置
        map_candidates = [
            (0xC100, 32, 32),  # 发现的第一个地图数据
            (0xC200, 32, 32),  # 发现的第二个地图数据
            (0x2A800, 32, 32), # 后面的地图数据
        ]
        
        extracted_maps = []
        
        for offset, width, height in map_candidates:
            if offset < len(self.prg_data):
                map_size = width * height
                map_data = self.prg_data[offset:offset + map_size]
                
                if len(map_data) == map_size:
                    # 分析地图数据
                    unique_tiles = len(set(map_data))
                    
                    # 转换为2D数组
                    map_2d = []
                    for y in range(height):
                        row = []
                        for x in range(width):
                            tile_id = map_data[y * width + x]
                            row.append(tile_id)
                        map_2d.append(row)
                    
                    map_info = {
                        'offset': offset,
                        'width': width,
                        'height': height,
                        'unique_tiles': unique_tiles,
                        'data': map_2d,
                        'raw_data': map_data
                    }
                    
                    extracted_maps.append(map_info)
                    print(f"地图 0x{offset:X}: {width}x{height}, {unique_tiles}种瓦片")
        
        return extracted_maps
    
    def extract_character_data(self) -> List[Dict]:
        """提取角色数据"""
        print("\n=== 提取角色数据 ===")
        
        # 在PRG数据中查找可能的角色属性表
        # 通常角色数据会有固定的结构，如：名称、等级、HP、攻击力等
        
        characters = []
        
        # 查找重复的数据结构（可能是角色表）
        for offset in range(0, len(self.prg_data) - 100, 16):  # 假设每个角色16字节
            # 检查是否像角色数据
            data_block = self.prg_data[offset:offset + 16]
            
            # 简单的启发式检查
            if self._looks_like_character_data(data_block):
                char_info = self._parse_character_data(data_block, offset)
                if char_info:
                    characters.append(char_info)
        
        print(f"发现 {len(characters)} 个可能的角色数据")
        return characters[:20]  # 返回前20个
    
    def _looks_like_character_data(self, data: bytes) -> bool:
        """检查数据是否像角色数据"""
        if len(data) < 16:
            return False
        
        # 检查数值范围是否合理
        # 假设前几个字节是属性值
        for i in range(4):
            if data[i] > 200:  # 属性值通常不会太大
                return False
        
        # 检查是否有太多的0或255
        zero_count = data.count(0)
        ff_count = data.count(255)
        
        if zero_count > 12 or ff_count > 12:
            return False
        
        return True
    
    def _parse_character_data(self, data: bytes, offset: int) -> Dict:
        """解析角色数据"""
        try:
            # 假设的数据结构（需要根据实际情况调整）
            char_info = {
                'offset': offset,
                'hp': data[0],
                'mp': data[1],
                'attack': data[2],
                'defense': data[3],
                'intelligence': data[4],
                'agility': data[5],
                'level': data[6],
                'raw_data': data.hex()
            }
            
            # 过滤明显不合理的数据
            if char_info['level'] > 0 and char_info['level'] < 100:
                return char_info
        except:
            pass
        
        return None
    
    def extract_text_data(self) -> List[str]:
        """提取文本数据"""
        print("\n=== 提取文本数据 ===")
        
        # 查找可能的中文文本
        # FC游戏通常使用自定义字符编码
        
        text_segments = []
        
        # 查找连续的非零字节（可能是文本）
        i = 0
        while i < len(self.prg_data) - 10:
            if self.prg_data[i] != 0 and self.prg_data[i] < 128:
                start = i
                while i < len(self.prg_data) and self.prg_data[i] != 0 and self.prg_data[i] < 128:
                    i += 1
                
                if i - start >= 8:  # 至少8个字符
                    text_data = self.prg_data[start:i]
                    text_segments.append({
                        'offset': start,
                        'length': len(text_data),
                        'data': text_data.hex(),
                        'ascii': ''.join(chr(b) if 32 <= b <= 126 else '.' for b in text_data)
                    })
            else:
                i += 1
        
        print(f"发现 {len(text_segments)} 个文本段")
        return text_segments[:50]  # 返回前50个
    
    def create_game_data_from_rom(self) -> Dict:
        """基于ROM数据创建游戏数据"""
        print("\n=== 基于ROM创建游戏数据 ===")
        
        # 提取各种数据
        maps = self.extract_map_data()
        characters = self.extract_character_data()
        texts = self.extract_text_data()
        
        # 分析第一个地图作为世界地图
        world_map = None
        if maps:
            world_map = maps[0]
            print(f"使用地图: {world_map['width']}x{world_map['height']}")
        
        # 创建基于ROM的游戏数据
        game_data = {
            'world_map': world_map,
            'characters': characters,
            'text_segments': texts,
            'rom_info': {
                'prg_size': len(self.prg_data),
                'chr_size': len(self.chr_data) if self.chr_data else 0,
                'total_maps': len(maps)
            }
        }
        
        return game_data
    
    def generate_map_visualization(self, map_data: Dict, output_file: str):
        """生成地图可视化"""
        if not map_data:
            return
        
        width = map_data['width']
        height = map_data['height']
        data = map_data['data']
        
        # 创建简单的ASCII地图
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"地图大小: {width}x{height}\n")
            f.write(f"瓦片种类: {map_data['unique_tiles']}\n")
            f.write(f"数据偏移: 0x{map_data['offset']:X}\n\n")
            
            # 生成字符映射
            unique_tiles = sorted(set(tile for row in data for tile in row))
            tile_chars = {}
            chars = ".#*+@%&$"
            for i, tile in enumerate(unique_tiles):
                tile_chars[tile] = chars[i % len(chars)]
            
            # 绘制地图
            for row in data:
                line = ''.join(tile_chars.get(tile, '?') for tile in row)
                f.write(line + '\n')
            
            # 添加图例
            f.write('\n图例:\n')
            for tile, char in tile_chars.items():
                f.write(f'{char} = 瓦片ID {tile}\n')

def main():
    """主函数"""
    rom_path = "game/sangokushi2.nes"
    
    if not os.path.exists(rom_path):
        print(f"ROM文件不存在: {rom_path}")
        return
    
    print("开始深度分析吞食天地2 ROM数据...")
    
    # 创建数据提取器
    extractor = SangokushiDataExtractor(rom_path)
    
    # 提取游戏数据
    game_data = extractor.create_game_data_from_rom()
    
    # 生成地图可视化
    if game_data['world_map']:
        extractor.generate_map_visualization(
            game_data['world_map'], 
            'rom_analysis/world_map.txt'
        )
        print("世界地图已保存到 rom_analysis/world_map.txt")
    
    # 保存提取的数据
    import json
    with open('rom_analysis/extracted_data.json', 'w', encoding='utf-8') as f:
        # 转换不能序列化的数据
        serializable_data = {
            'rom_info': game_data['rom_info'],
            'characters': game_data['characters'],
            'text_segments': game_data['text_segments'][:10],  # 只保存前10个
            'world_map_info': {
                'width': game_data['world_map']['width'],
                'height': game_data['world_map']['height'],
                'unique_tiles': game_data['world_map']['unique_tiles'],
                'offset': game_data['world_map']['offset']
            } if game_data['world_map'] else None
        }
        json.dump(serializable_data, f, indent=2, ensure_ascii=False)
    
    print("提取的数据已保存到 rom_analysis/extracted_data.json")
    print("\n=== 分析完成 ===")
    print("现在可以基于这些数据重新创建更准确的游戏!")

if __name__ == "__main__":
    main()
