#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏数据 - 武将数据、城市数据、物品数据
"""

from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class WeaponData:
    """武器数据"""
    name: str
    attack_bonus: int
    price: int
    description: str

@dataclass
class ArmorData:
    """防具数据"""
    name: str
    defense_bonus: int
    price: int
    description: str

@dataclass
class ItemData:
    """物品数据"""
    name: str
    item_type: str  # "consumable", "key", "misc"
    effect: str
    price: int
    description: str

@dataclass
class CityData:
    """城市数据"""
    name: str
    city_type: str  # "city", "castle", "village"
    population: int
    prosperity: int
    ruler: str
    description: str
    available_characters: List[str]
    available_items: List[str]

class GameData:
    """游戏数据管理类"""
    
    def __init__(self):
        self.weapons = {}
        self.armors = {}
        self.items = {}
        self.cities = {}
        self.character_data = {}
        
        # 初始化所有数据
        self._init_weapons()
        self._init_armors()
        self._init_items()
        self._init_cities()
        self._init_character_data()
    
    def _init_weapons(self):
        """初始化武器数据"""
        weapons_data = [
            ("木剑", 5, 50, "最基础的武器"),
            ("铁剑", 12, 200, "普通的铁制剑"),
            ("钢剑", 20, 500, "锋利的钢制剑"),
            ("青龙偃月刀", 35, 2000, "关羽的专用武器"),
            ("丈八蛇矛", 32, 1800, "张飞的专用武器"),
            ("双股剑", 25, 1200, "刘备的专用武器"),
            ("方天画戟", 45, 3000, "吕布的无双武器"),
            ("倚天剑", 40, 2500, "传说中的神剑"),
            ("青釭剑", 38, 2200, "曹操的宝剑"),
            ("七星剑", 30, 1500, "王允的宝剑"),
        ]
        
        for name, attack, price, desc in weapons_data:
            self.weapons[name] = WeaponData(name, attack, price, desc)
    
    def _init_armors(self):
        """初始化防具数据"""
        armors_data = [
            ("布衣", 2, 30, "最基础的防具"),
            ("皮甲", 8, 150, "轻便的皮制护甲"),
            ("铁甲", 15, 400, "坚固的铁制护甲"),
            ("钢甲", 25, 800, "精良的钢制护甲"),
            ("黄金甲", 35, 2000, "华丽的黄金护甲"),
            ("龙鳞甲", 40, 3000, "传说中的龙鳞护甲"),
            ("凤凰羽衣", 30, 2500, "轻盈的凤凰羽衣"),
            ("玄武甲", 45, 3500, "防御力极强的玄武甲"),
        ]
        
        for name, defense, price, desc in armors_data:
            self.armors[name] = ArmorData(name, defense, price, desc)
    
    def _init_items(self):
        """初始化物品数据"""
        items_data = [
            ("药草", "consumable", "恢复50HP", 20, "常见的治疗药草"),
            ("高级药草", "consumable", "恢复150HP", 80, "效果更好的药草"),
            ("万能药", "consumable", "完全恢复HP", 300, "珍贵的万能药"),
            ("魔法药水", "consumable", "恢复50MP", 50, "恢复魔法力的药水"),
            ("高级魔法药水", "consumable", "恢复150MP", 200, "高级魔法药水"),
            ("复活药", "consumable", "复活并恢复1HP", 500, "能够复活倒下同伴的神药"),
            ("经验书", "consumable", "获得100经验值", 100, "记录着修炼心得的书籍"),
            ("力量果实", "consumable", "永久增加5攻击力", 1000, "能够增强力量的神奇果实"),
            ("防御果实", "consumable", "永久增加5防御力", 1000, "能够增强防御的神奇果实"),
            ("智慧果实", "consumable", "永久增加5智力", 1000, "能够增强智慧的神奇果实"),
            ("城门钥匙", "key", "打开城门", 0, "打开特定城门的钥匙"),
            ("宝箱钥匙", "key", "打开宝箱", 0, "打开宝箱的钥匙"),
            ("传国玉玺", "key", "象征皇权的印玺", 0, "代表天下正统的传国玉玺"),
        ]
        
        for name, item_type, effect, price, desc in items_data:
            self.items[name] = ItemData(name, item_type, effect, price, desc)
    
    def _init_cities(self):
        """初始化城市数据"""
        cities_data = [
            {
                "name": "洛阳",
                "type": "city",
                "population": 100000,
                "prosperity": 90,
                "ruler": "汉献帝",
                "description": "东汉的都城，天下的中心",
                "characters": ["董卓", "李儒", "华雄"],
                "items": ["高级药草", "钢剑", "铁甲", "魔法药水"]
            },
            {
                "name": "长安",
                "type": "city", 
                "population": 80000,
                "prosperity": 85,
                "ruler": "董卓",
                "description": "西部的重要城市",
                "characters": ["张辽", "高顺"],
                "items": ["药草", "铁剑", "皮甲", "经验书"]
            },
            {
                "name": "新野",
                "type": "village",
                "population": 5000,
                "prosperity": 40,
                "ruler": "刘备",
                "description": "刘备的根据地",
                "characters": ["徐庶", "伊籍"],
                "items": ["药草", "木剑", "布衣"]
            },
            {
                "name": "成都",
                "type": "castle",
                "population": 60000,
                "prosperity": 75,
                "ruler": "刘璋",
                "description": "益州的首府",
                "characters": ["法正", "孟达", "严颜"],
                "items": ["高级药草", "钢剑", "钢甲", "高级魔法药水"]
            },
            {
                "name": "徐州",
                "type": "village",
                "population": 8000,
                "prosperity": 50,
                "ruler": "陶谦",
                "description": "东部的重要据点",
                "characters": ["糜竺", "糜芳"],
                "items": ["药草", "铁剑", "皮甲"]
            },
        ]
        
        for city_info in cities_data:
            city_data = CityData(
                name=city_info["name"],
                city_type=city_info["type"],
                population=city_info["population"],
                prosperity=city_info["prosperity"],
                ruler=city_info["ruler"],
                description=city_info["description"],
                available_characters=city_info["characters"],
                available_items=city_info["items"]
            )
            self.cities[city_info["name"]] = city_data
    
    def _init_character_data(self):
        """初始化角色数据"""
        # 这里存储角色的背景信息、特殊技能等
        self.character_data = {
            "刘备": {
                "title": "仁德之主",
                "biography": "汉室宗亲，以仁德著称",
                "special_skills": ["仁德", "激励"],
                "relationships": {"关羽": 100, "张飞": 100, "诸葛亮": 90}
            },
            "关羽": {
                "title": "武圣",
                "biography": "忠义无双的万人敌",
                "special_skills": ["青龙偃月", "义薄云天"],
                "relationships": {"刘备": 100, "张飞": 95, "诸葛亮": 80}
            },
            "张飞": {
                "title": "猛张飞",
                "biography": "勇猛无敌的虎将",
                "special_skills": ["咆哮", "猛攻"],
                "relationships": {"刘备": 100, "关羽": 95, "诸葛亮": 70}
            },
            "诸葛亮": {
                "title": "卧龙",
                "biography": "智谋无双的军师",
                "special_skills": ["八卦阵", "火攻", "空城计"],
                "relationships": {"刘备": 90, "关羽": 80, "张飞": 70}
            },
            "赵云": {
                "title": "常胜将军",
                "biography": "忠勇双全的完美武将",
                "special_skills": ["龙胆", "救主"],
                "relationships": {"刘备": 95, "关羽": 85, "张飞": 85}
            },
            "曹操": {
                "title": "魏武帝",
                "biography": "乱世之奸雄",
                "special_skills": ["奸雄", "护驾"],
                "relationships": {"司马懿": 70, "典韦": 90, "许褚": 85}
            },
            "典韦": {
                "title": "古之恶来",
                "biography": "曹操的贴身护卫",
                "special_skills": ["强袭", "护主"],
                "relationships": {"曹操": 100, "许褚": 80}
            },
            "许褚": {
                "title": "虎痴",
                "biography": "力大无穷的猛将",
                "special_skills": ["裸衣", "虎啸"],
                "relationships": {"曹操": 95, "典韦": 80}
            },
            "司马懿": {
                "title": "冢虎",
                "biography": "深藏不露的谋士",
                "special_skills": ["反间", "忍耐"],
                "relationships": {"曹操": 60}
            },
            "孙权": {
                "title": "江东之主",
                "biography": "继承父兄基业的君主",
                "special_skills": ["制衡", "救援"],
                "relationships": {"周瑜": 90, "鲁肃": 85}
            },
            "周瑜": {
                "title": "美周郎",
                "biography": "文武双全的都督",
                "special_skills": ["反间", "火攻"],
                "relationships": {"孙权": 90, "小乔": 100}
            },
            "吕布": {
                "title": "飞将",
                "biography": "天下无双的武将",
                "special_skills": ["无双", "神速"],
                "relationships": {"貂蝉": 80}
            },
        }
    
    def get_weapon(self, name: str) -> WeaponData:
        """获取武器数据"""
        return self.weapons.get(name)
    
    def get_armor(self, name: str) -> ArmorData:
        """获取防具数据"""
        return self.armors.get(name)
    
    def get_item(self, name: str) -> ItemData:
        """获取物品数据"""
        return self.items.get(name)
    
    def get_city(self, name: str) -> CityData:
        """获取城市数据"""
        return self.cities.get(name)
    
    def get_character_info(self, name: str) -> Dict[str, Any]:
        """获取角色信息"""
        return self.character_data.get(name, {})
    
    def get_all_weapons(self) -> List[WeaponData]:
        """获取所有武器"""
        return list(self.weapons.values())
    
    def get_all_armors(self) -> List[ArmorData]:
        """获取所有防具"""
        return list(self.armors.values())
    
    def get_all_items(self) -> List[ItemData]:
        """获取所有物品"""
        return list(self.items.values())
    
    def get_all_cities(self) -> List[CityData]:
        """获取所有城市"""
        return list(self.cities.values())
