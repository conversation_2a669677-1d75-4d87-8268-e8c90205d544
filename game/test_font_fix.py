#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体修复效果
"""

import pygame
import sys
import os

def test_font_fix():
    """测试字体修复效果"""
    pygame.init()
    
    # 创建窗口
    screen = pygame.display.set_mode((600, 400))
    pygame.display.set_caption("字体修复测试")
    
    # 直接加载宋体
    simsun_path = "C:/Windows/Fonts/simsun.ttc"
    
    try:
        if os.path.exists(simsun_path):
            font = pygame.font.Font(simsun_path, 24)
            print(f"✓ 成功加载宋体字体: {simsun_path}")
        else:
            font = pygame.font.SysFont("SimSun", 24)
            print("✓ 成功加载系统宋体字体")
    except Exception as e:
        print(f"✗ 字体加载失败: {e}")
        font = pygame.font.Font(None, 24)
    
    # 测试文本
    test_texts = [
        "吞食天地2",
        "开始游戏",
        "读取存档",
        "游戏设置", 
        "退出游戏",
        "刘备 关羽 张飞",
        "诸葛亮 赵云 曹操"
    ]
    
    clock = pygame.time.Clock()
    running = True
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
        
        # 清空屏幕
        screen.fill((0, 0, 0))
        
        # 显示标题
        title_surface = font.render("字体修复测试 - 宋体", True, (255, 255, 0))
        title_rect = title_surface.get_rect(center=(300, 50))
        screen.blit(title_surface, title_rect)
        
        # 显示测试文本
        y_offset = 100
        for text in test_texts:
            try:
                text_surface = font.render(text, True, (255, 255, 255))
                text_rect = text_surface.get_rect(center=(300, y_offset))
                screen.blit(text_surface, text_rect)
                
                # 显示宽度信息
                width_text = f"宽度: {text_surface.get_width()}"
                width_surface = pygame.font.Font(None, 16).render(width_text, True, (128, 128, 128))
                screen.blit(width_surface, (450, y_offset - 8))
                
                y_offset += 35
            except Exception as e:
                print(f"渲染失败: {text} - {e}")
        
        # 显示说明
        instruction_surface = pygame.font.Font(None, 18).render("Press ESC to exit", True, (128, 128, 128))
        instruction_rect = instruction_surface.get_rect(center=(300, 350))
        screen.blit(instruction_surface, instruction_rect)
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("字体测试完成")

if __name__ == "__main__":
    print("开始测试字体修复效果...")
    test_font_fix()
