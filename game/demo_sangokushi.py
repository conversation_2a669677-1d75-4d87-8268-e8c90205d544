#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
吞食天地2游戏演示脚本
展示游戏的主要功能和特色
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """打印标题"""
    print("\n" + "="*50)
    print(f"  {title}")
    print("="*50)

def print_separator():
    """打印分隔符"""
    print("-" * 50)

def demo_character_system():
    """演示角色系统"""
    print_header("角色系统演示")
    
    from character import Character, CharacterClass, CharacterManager
    from game_data import GameData
    
    # 创建游戏数据和角色管理器
    game_data = GameData()
    char_manager = CharacterManager(game_data)
    
    print("🏛️ 三国武将招募中...")
    time.sleep(1)
    
    # 显示默认队伍
    print("\n📋 当前队伍:")
    for i, char in enumerate(char_manager.party, 1):
        status = char.get_status_info()
        print(f"  {i}. {status['name']} ({status['class']}) - Lv.{status['level']}")
        print(f"     HP: {status['hp']}, 攻击: {status['attack']}, 防御: {status['defense']}")
    
    print_separator()
    
    # 演示角色升级
    print("⚡ 刘备获得战斗经验...")
    liu_bei = char_manager.party[0]
    old_level = liu_bei.level
    old_attack = liu_bei.attack
    
    # 给予大量经验值触发升级
    liu_bei.gain_exp(200)
    
    if liu_bei.level > old_level:
        print(f"🎉 {liu_bei.name} 升级了! {old_level} → {liu_bei.level}")
        print(f"   攻击力提升: {old_attack} → {liu_bei.attack}")
    
    print_separator()
    
    # 显示可招募角色
    print("🎯 可招募的传奇武将:")
    legendary_chars = ["诸葛亮", "赵云", "曹操", "吕布"]
    for char_name in legendary_chars:
        char_info = game_data.get_character_info(char_name)
        print(f"  • {char_name} - {char_info.get('title', '未知')}")
        print(f"    {char_info.get('biography', '暂无介绍')}")

def demo_battle_system():
    """演示战斗系统"""
    print_header("战斗系统演示")
    
    from battle import BattleSystem, Enemy
    from character import CharacterManager
    from game_data import GameData
    
    # 创建战斗系统
    game_data = GameData()
    char_manager = CharacterManager(game_data)
    battle_system = BattleSystem(char_manager)
    
    print("⚔️ 遭遇敌人!")
    time.sleep(1)
    
    # 创建敌人
    enemies = [
        Enemy("山贼头目", 5, "elite"),
        Enemy("山贼", 3, "soldier"),
        Enemy("山贼", 4, "soldier")
    ]
    
    print("\n👹 敌方阵容:")
    for i, enemy in enumerate(enemies, 1):
        print(f"  {i}. {enemy.name} (Lv.{enemy.level}) - HP: {enemy.current_hp}")
    
    print("\n👥 我方阵容:")
    for i, char in enumerate(char_manager.party, 1):
        print(f"  {i}. {char.name} (Lv.{char.level}) - HP: {char.current_hp}/{char.max_hp}")
    
    print_separator()
    
    # 模拟战斗
    print("🎲 战斗开始!")
    battle_data = {"enemies": enemies}
    battle_system.start_battle(battle_data)
    
    # 模拟几回合战斗
    print("\n📜 战斗记录:")
    for turn in range(3):
        print(f"  回合 {turn + 1}:")
        
        # 模拟玩家攻击
        attacker = char_manager.party[turn % len(char_manager.party)]
        target = enemies[0] if enemies[0].is_alive() else enemies[1]
        
        damage = attacker.get_attack_power()
        target.take_damage(damage)
        
        print(f"    {attacker.name} 攻击 {target.name}, 造成 {damage} 点伤害")
        
        if not target.is_alive():
            print(f"    {target.name} 被击败!")
            break
        
        time.sleep(0.5)
    
    print("\n🏆 战斗胜利! 获得经验值和金币奖励")

def demo_map_system():
    """演示地图系统"""
    print_header("地图系统演示")
    
    from map_system import WorldMap, TerrainType
    from game_data import GameData
    
    # 创建地图
    game_data = GameData()
    world_map = WorldMap(game_data)
    
    print("🗺️ 三国世界地图")
    print(f"   地图大小: {world_map.width} x {world_map.height}")
    print(f"   当前位置: ({world_map.player_x}, {world_map.player_y})")
    
    # 显示当前位置信息
    current_tile = world_map.get_current_tile()
    if current_tile:
        print(f"   当前地形: {current_tile.terrain.value}")
        if current_tile.name:
            print(f"   地点名称: {current_tile.name}")
    
    print_separator()
    
    # 显示地图图例
    print("🎨 地形图例:")
    terrain_info = {
        TerrainType.GRASS: ("草地", ".", "适合行军的平原"),
        TerrainType.FOREST: ("森林", "T", "容易遭遇野兽"),
        TerrainType.MOUNTAIN: ("山地", "^", "地势险要，多有盗贼"),
        TerrainType.WATER: ("水域", "~", "无法通行"),
        TerrainType.CITY: ("城市", "C", "繁华的大城市"),
        TerrainType.CASTLE: ("城堡", "H", "军事要塞"),
        TerrainType.VILLAGE: ("村庄", "V", "小型聚落"),
    }
    
    for terrain, (name, char, desc) in terrain_info.items():
        print(f"  {char} - {name}: {desc}")
    
    print_separator()
    
    # 显示重要城市
    print("🏰 重要城市:")
    cities = game_data.get_all_cities()
    for city in cities:
        print(f"  • {city.name} ({city.city_type})")
        print(f"    人口: {city.population:,}, 繁荣度: {city.prosperity}")
        print(f"    统治者: {city.ruler}")
    
    print_separator()
    
    # 模拟移动和遭遇
    print("🚶 模拟移动...")
    directions = [(1, 0), (0, 1), (-1, 0), (0, -1)]
    
    for i, (dx, dy) in enumerate(directions):
        old_pos = (world_map.player_x, world_map.player_y)
        success = world_map.move_player(dx, dy)
        
        if success:
            new_pos = (world_map.player_x, world_map.player_y)
            tile = world_map.get_current_tile()
            print(f"  移动 {i+1}: {old_pos} → {new_pos} ({tile.terrain.value})")
            
            # 检查是否有随机遭遇
            if world_map.pending_battle:
                print(f"    ⚠️ 遭遇敌人! ({len(world_map.pending_battle['enemies'])} 个敌人)")
                world_map.pending_battle = None  # 清除遭遇
        else:
            print(f"  移动 {i+1}: 无法移动到 {(old_pos[0]+dx, old_pos[1]+dy)}")
        
        time.sleep(0.3)

def demo_game_data():
    """演示游戏数据"""
    print_header("游戏数据演示")
    
    from game_data import GameData
    
    game_data = GameData()
    
    # 显示武器数据
    print("⚔️ 传奇武器:")
    legendary_weapons = ["青龙偃月刀", "方天画戟", "倚天剑", "七星剑"]
    for weapon_name in legendary_weapons:
        weapon = game_data.get_weapon(weapon_name)
        if weapon:
            print(f"  • {weapon.name}: 攻击+{weapon.attack_bonus}, 价格 {weapon.price} 金")
            print(f"    {weapon.description}")
    
    print_separator()
    
    # 显示防具数据
    print("🛡️ 精良防具:")
    good_armors = ["钢甲", "黄金甲", "龙鳞甲", "玄武甲"]
    for armor_name in good_armors:
        armor = game_data.get_armor(armor_name)
        if armor:
            print(f"  • {armor.name}: 防御+{armor.defense_bonus}, 价格 {armor.price} 金")
            print(f"    {armor.description}")
    
    print_separator()
    
    # 显示特殊道具
    print("💎 珍贵道具:")
    special_items = ["万能药", "力量果实", "传国玉玺", "复活药"]
    for item_name in special_items:
        item = game_data.get_item(item_name)
        if item:
            print(f"  • {item.name}: {item.effect}")
            print(f"    {item.description}")
            if item.price > 0:
                print(f"    价格: {item.price} 金")

def demo_game_state():
    """演示游戏状态"""
    print_header("游戏状态演示")
    
    from game_state import GameState
    
    game_state = GameState()
    
    print("💾 存档系统功能:")
    print(f"  • 支持 {game_state.max_save_slots} 个存档槽位")
    print(f"  • 当前金币: {game_state.get_gold()}")
    print(f"  • 游戏时间: {game_state.get_play_time_formatted()}")
    print(f"  • 当前章节: {game_state.get_current_chapter()}")
    
    print_separator()
    
    # 模拟游戏进度
    print("📈 模拟游戏进度:")
    
    # 添加一些物品
    items_to_add = [("药草", 10), ("铁剑", 2), ("皮甲", 1)]
    for item_name, quantity in items_to_add:
        game_state.add_item(item_name, quantity)
        print(f"  获得物品: {item_name} x{quantity}")
    
    # 增加金币
    game_state.add_gold(500)
    print(f"  获得金币: 500 (总计: {game_state.get_gold()})")
    
    # 访问城市
    cities_to_visit = ["洛阳", "长安", "新野"]
    for city in cities_to_visit:
        game_state.add_visited_city(city)
        print(f"  访问城市: {city}")
    
    # 解锁角色
    chars_to_unlock = ["诸葛亮", "赵云"]
    for char in chars_to_unlock:
        game_state.unlock_character(char)
        print(f"  解锁角色: {char}")
    
    print_separator()
    
    # 显示当前状态
    print("📊 当前游戏状态:")
    print(f"  背包物品: {game_state.get_inventory()}")
    print(f"  已访问城市: {len(game_state.game_data['visited_cities'])} 个")
    print(f"  已解锁角色: {len(game_state.game_data['unlocked_characters'])} 个")

def main():
    """主演示函数"""
    print("🎮 欢迎来到《吞食天地2》游戏演示!")
    print("   基于FC经典游戏的Python重制版")
    print("   使用Pygame开发的三国题材RPG")
    
    time.sleep(2)
    
    # 运行各个系统的演示
    demo_character_system()
    time.sleep(1)
    
    demo_battle_system()
    time.sleep(1)
    
    demo_map_system()
    time.sleep(1)
    
    demo_game_data()
    time.sleep(1)
    
    demo_game_state()
    
    print_header("演示完成")
    print("🎉 感谢观看《吞食天地2》游戏演示!")
    print("💡 运行 'python sangokushi2.py' 开始游戏")
    print("🧪 运行 'python test_sangokushi.py' 进行系统测试")
    print("📖 查看 README.md 了解详细信息")

if __name__ == "__main__":
    main()
