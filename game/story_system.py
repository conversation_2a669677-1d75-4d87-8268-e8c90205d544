#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
故事情节系统 - FC吞食天地2剧情
"""

from typing import Dict, List, Optional
from enum import Enum

class StoryChapter(Enum):
    PROLOGUE = "序章"
    YELLOW_TURBAN = "黄巾起义"
    DONG_ZHUO = "董卓之乱"
    COALITION = "诸侯讨董"
    THREE_KINGDOMS = "三国鼎立"
    CHIBI = "赤壁之战"
    YILING = "夷陵之战"
    UNIFICATION = "天下统一"

class StoryEvent:
    """故事事件"""
    
    def __init__(self, event_id: str, title: str, description: str, 
                 location: str = "", characters: List[str] = None,
                 prerequisites: List[str] = None, rewards: Dict = None):
        self.event_id = event_id
        self.title = title
        self.description = description
        self.location = location
        self.characters = characters or []
        self.prerequisites = prerequisites or []
        self.rewards = rewards or {}
        self.completed = False

class StorySystem:
    """故事系统"""
    
    def __init__(self):
        self.current_chapter = StoryChapter.PROLOGUE
        self.completed_events = set()
        self.available_events = set()
        self.story_events = {}
        
        # 初始化故事事件
        self._init_story_events()
        
        # 设置初始可用事件
        self.available_events.add("prologue_start")
    
    def _init_story_events(self):
        """初始化故事事件"""
        
        # 序章
        self.story_events["prologue_start"] = StoryEvent(
            "prologue_start",
            "桃园结义",
            "东汉末年，天下大乱。刘备、关羽、张飞三人在桃园结为异姓兄弟，立誓共同匡扶汉室。",
            "涿郡",
            ["刘备", "关羽", "张飞"],
            [],
            {"characters": ["刘备", "关羽", "张飞"], "gold": 1000}
        )
        
        self.story_events["meet_zhuge"] = StoryEvent(
            "meet_zhuge",
            "三顾茅庐",
            "刘备三次拜访诸葛亮，终于请得卧龙先生出山相助。",
            "隆中",
            ["刘备", "诸葛亮"],
            ["prologue_start"],
            {"characters": ["诸葛亮"], "items": ["羽扇"]}
        )
        
        # 黄巾起义
        self.story_events["yellow_turban_start"] = StoryEvent(
            "yellow_turban_start",
            "黄巾起义",
            "张角率领黄巾军起义，天下震动。刘备兄弟奉命讨伐黄巾贼。",
            "冀州",
            ["张角", "张宝", "张梁"],
            ["prologue_start"],
            {"exp": 500, "gold": 2000}
        )
        
        self.story_events["defeat_zhang_jiao"] = StoryEvent(
            "defeat_zhang_jiao",
            "击败张角",
            "经过激战，刘备军成功击败黄巾军首领张角，平定了黄巾之乱。",
            "广宗",
            ["张角"],
            ["yellow_turban_start"],
            {"exp": 1000, "gold": 3000, "items": ["太平要术"]}
        )
        
        # 董卓之乱
        self.story_events["dong_zhuo_chaos"] = StoryEvent(
            "dong_zhuo_chaos",
            "董卓进京",
            "董卓挟天子以令诸侯，在洛阳为所欲为，天下诸侯愤慨不已。",
            "洛阳",
            ["董卓", "吕布"],
            ["defeat_zhang_jiao"],
            {"story_progress": "董卓篇"}
        )
        
        self.story_events["coalition_formed"] = StoryEvent(
            "coalition_formed",
            "诸侯联盟",
            "袁绍、曹操等诸侯组成联军，共同讨伐董卓。刘备也加入了联军。",
            "陈留",
            ["袁绍", "曹操", "孙坚"],
            ["dong_zhuo_chaos"],
            {"allies": ["袁绍", "曹操"]}
        )
        
        self.story_events["hulao_pass"] = StoryEvent(
            "hulao_pass",
            "虎牢关之战",
            "联军在虎牢关与董卓军激战，吕布无人能敌，直到刘关张三兄弟联手。",
            "虎牢关",
            ["吕布", "华雄"],
            ["coalition_formed"],
            {"exp": 2000, "fame": 1000}
        )
        
        # 三国鼎立
        self.story_events["three_kingdoms"] = StoryEvent(
            "three_kingdoms",
            "三国鼎立",
            "曹操统一北方，孙权据守江东，刘备占据益州，三国鼎立的局面正式形成。",
            "成都",
            ["曹操", "孙权"],
            ["hulao_pass"],
            {"territory": "益州"}
        )
        
        # 赤壁之战
        self.story_events["chibi_battle"] = StoryEvent(
            "chibi_battle",
            "赤壁之战",
            "曹操率大军南下，孙刘联军在赤壁以火攻大败曹军，奠定三分天下之势。",
            "赤壁",
            ["曹操", "周瑜", "诸葛亮"],
            ["three_kingdoms"],
            {"exp": 5000, "fame": 3000, "alliance": "孙吴"}
        )
        
        # 夷陵之战
        self.story_events["yiling_battle"] = StoryEvent(
            "yiling_battle",
            "夷陵之战",
            "刘备为报关羽之仇，率大军伐吴，却在夷陵被陆逊火烧连营，大败而归。",
            "夷陵",
            ["陆逊", "关羽"],
            ["chibi_battle"],
            {"exp": 3000, "story_progress": "后期"}
        )
        
        # 天下统一
        self.story_events["unification"] = StoryEvent(
            "unification",
            "天下统一",
            "经过多年征战，终于统一了天下，结束了乱世，百姓得以安居乐业。",
            "洛阳",
            [],
            ["yiling_battle"],
            {"ending": "统一天下", "exp": 10000}
        )
    
    def get_available_events(self) -> List[StoryEvent]:
        """获取可用的故事事件"""
        available = []
        
        for event_id in self.available_events:
            if event_id not in self.completed_events:
                event = self.story_events.get(event_id)
                if event and self._check_prerequisites(event):
                    available.append(event)
        
        return available
    
    def _check_prerequisites(self, event: StoryEvent) -> bool:
        """检查事件前置条件"""
        for prereq in event.prerequisites:
            if prereq not in self.completed_events:
                return False
        return True
    
    def complete_event(self, event_id: str) -> Dict:
        """完成故事事件"""
        if event_id not in self.story_events:
            return {}
        
        event = self.story_events[event_id]
        if event_id in self.completed_events:
            return {}
        
        # 标记为已完成
        self.completed_events.add(event_id)
        self.available_events.discard(event_id)
        
        # 解锁后续事件
        self._unlock_next_events(event_id)
        
        # 更新章节
        self._update_chapter(event_id)
        
        return event.rewards
    
    def _unlock_next_events(self, completed_event_id: str):
        """解锁后续事件"""
        for event_id, event in self.story_events.items():
            if completed_event_id in event.prerequisites:
                if self._check_prerequisites(event):
                    self.available_events.add(event_id)
    
    def _update_chapter(self, event_id: str):
        """更新当前章节"""
        chapter_events = {
            "prologue_start": StoryChapter.PROLOGUE,
            "yellow_turban_start": StoryChapter.YELLOW_TURBAN,
            "dong_zhuo_chaos": StoryChapter.DONG_ZHUO,
            "coalition_formed": StoryChapter.COALITION,
            "three_kingdoms": StoryChapter.THREE_KINGDOMS,
            "chibi_battle": StoryChapter.CHIBI,
            "yiling_battle": StoryChapter.YILING,
            "unification": StoryChapter.UNIFICATION,
        }
        
        if event_id in chapter_events:
            self.current_chapter = chapter_events[event_id]
    
    def get_current_chapter_info(self) -> Dict:
        """获取当前章节信息"""
        chapter_descriptions = {
            StoryChapter.PROLOGUE: "东汉末年，英雄辈出的时代即将开始...",
            StoryChapter.YELLOW_TURBAN: "黄巾起义席卷天下，乱世拉开序幕。",
            StoryChapter.DONG_ZHUO: "董卓专权，祸乱朝纲，天下诸侯共愤。",
            StoryChapter.COALITION: "十八路诸侯讨董，英雄豪杰齐聚。",
            StoryChapter.THREE_KINGDOMS: "魏蜀吴三国鼎立，天下三分。",
            StoryChapter.CHIBI: "赤壁烈火，改变天下格局的一战。",
            StoryChapter.YILING: "蜀汉衰落，三国局势再次变化。",
            StoryChapter.UNIFICATION: "乱世终结，天下重归一统。",
        }
        
        return {
            "chapter": self.current_chapter.value,
            "description": chapter_descriptions.get(self.current_chapter, ""),
            "progress": len(self.completed_events),
            "total_events": len(self.story_events)
        }
    
    def get_story_progress(self) -> float:
        """获取故事进度百分比"""
        return len(self.completed_events) / len(self.story_events) * 100
    
    def get_event_by_location(self, location: str) -> Optional[StoryEvent]:
        """根据位置获取可用事件"""
        for event in self.get_available_events():
            if event.location == location:
                return event
        return None
    
    def is_story_complete(self) -> bool:
        """检查故事是否完成"""
        return "unification" in self.completed_events
    
    def get_character_story_events(self, character_name: str) -> List[StoryEvent]:
        """获取特定角色相关的故事事件"""
        character_events = []
        
        for event in self.story_events.values():
            if character_name in event.characters:
                character_events.append(event)
        
        return character_events
