import pygame
import random
import sys
from typing import List, Tuple, Optional

# 初始化Pygame
pygame.init()

# 游戏常量
GRID_WIDTH = 10
GRID_HEIGHT = 20
CELL_SIZE = 30
GRID_X_OFFSET = 50
GRID_Y_OFFSET = 50

# 窗口尺寸
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 700

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
CYAN = (0, 255, 255)
BLUE = (0, 0, 255)
ORANGE = (255, 165, 0)
YELLOW = (255, 255, 0)
GREEN = (0, 255, 0)
PURPLE = (128, 0, 128)
RED = (255, 0, 0)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)

# 方块形状定义
TETROMINOES = {
    'I': [
        ['.....',
         '..#..',
         '..#..',
         '..#..',
         '..#..'],
        ['.....',
         '.....',
         '####.',
         '.....',
         '.....']
    ],
    'O': [
        ['.....',
         '.....',
         '.##..',
         '.##..',
         '.....']
    ],
    'T': [
        ['.....',
         '.....',
         '.#...',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.##..',
         '.#...'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '.#...'],
        ['.....',
         '.....',
         '.#...',
         '##...',
         '.#...']
    ],
    'S': [
        ['.....',
         '.....',
         '.##..',
         '##...',
         '.....'],
        ['.....',
         '.#...',
         '.##..',
         '..#..',
         '.....']
    ],
    'Z': [
        ['.....',
         '.....',
         '##...',
         '.##..',
         '.....'],
        ['.....',
         '..#..',
         '.##..',
         '.#...',
         '.....']
    ],
    'J': [
        ['.....',
         '.#...',
         '.#...',
         '##...',
         '.....'],
        ['.....',
         '.....',
         '#....',
         '###..',
         '.....'],
        ['.....',
         '.##..',
         '.#...',
         '.#...',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '..#..',
         '.....']
    ],
    'L': [
        ['.....',
         '..#..',
         '..#..',
         '.##..',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '#....',
         '.....'],
        ['.....',
         '##...',
         '.#...',
         '.#...',
         '.....'],
        ['.....',
         '.....',
         '..#..',
         '###..',
         '.....']
    ]
}

# 方块颜色
TETROMINO_COLORS = {
    'I': CYAN,
    'O': YELLOW,
    'T': PURPLE,
    'S': GREEN,
    'Z': RED,
    'J': BLUE,
    'L': ORANGE
}

class Tetromino:
    def __init__(self, shape: str):
        self.shape = shape
        self.color = TETROMINO_COLORS[shape]
        self.x = GRID_WIDTH // 2 - 2
        self.y = 0
        self.rotation = 0
        
    def get_rotated_shape(self) -> List[str]:
        """获取当前旋转状态的形状"""
        return TETROMINOES[self.shape][self.rotation]
    
    def get_cells(self) -> List[Tuple[int, int]]:
        """获取方块占用的所有格子坐标"""
        cells = []
        shape = self.get_rotated_shape()
        for i, row in enumerate(shape):
            for j, cell in enumerate(row):
                if cell == '#':
                    cells.append((self.x + j, self.y + i))
        return cells

class TetrisGame:
    def __init__(self):
        self.grid = [[BLACK for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = None
        self.next_piece = None
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_time = 0
        self.fall_speed = 500  # 毫秒
        self.game_over = False
        self.paused = False
        
        # 创建字体
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # 生成第一个和下一个方块
        self.spawn_piece()
        self.next_piece = self.create_random_piece()
        
    def create_random_piece(self) -> Tetromino:
        """创建随机方块"""
        shape = random.choice(list(TETROMINOES.keys()))
        return Tetromino(shape)
    
    def spawn_piece(self):
        """生成新方块"""
        if self.next_piece:
            self.current_piece = self.next_piece
            self.next_piece = self.create_random_piece()
        else:
            self.current_piece = self.create_random_piece()
            
        # 检查游戏是否结束
        if self.check_collision(self.current_piece):
            self.game_over = True
    
    def check_collision(self, piece: Tetromino, dx: int = 0, dy: int = 0, rotation: int = None) -> bool:
        """检查碰撞"""
        if rotation is not None:
            old_rotation = piece.rotation
            piece.rotation = rotation
        
        cells = piece.get_cells()
        
        if rotation is not None:
            piece.rotation = old_rotation
        
        for x, y in cells:
            new_x, new_y = x + dx, y + dy
            
            # 检查边界
            if new_x < 0 or new_x >= GRID_WIDTH or new_y >= GRID_HEIGHT:
                return True
            
            # 检查与已放置方块的碰撞
            if new_y >= 0 and self.grid[new_y][new_x] != BLACK:
                return True
        
        return False
    
    def move_piece(self, dx: int, dy: int) -> bool:
        """移动方块"""
        if not self.current_piece or self.game_over or self.paused:
            return False
            
        if not self.check_collision(self.current_piece, dx, dy):
            self.current_piece.x += dx
            self.current_piece.y += dy
            return True
        return False
    
    def rotate_piece(self) -> bool:
        """旋转方块"""
        if not self.current_piece or self.game_over or self.paused:
            return False
            
        new_rotation = (self.current_piece.rotation + 1) % len(TETROMINOES[self.current_piece.shape])
        
        if not self.check_collision(self.current_piece, rotation=new_rotation):
            self.current_piece.rotation = new_rotation
            return True
        return False
    
    def place_piece(self):
        """放置方块到网格中"""
        if not self.current_piece:
            return
            
        for x, y in self.current_piece.get_cells():
            if 0 <= x < GRID_WIDTH and 0 <= y < GRID_HEIGHT:
                self.grid[y][x] = self.current_piece.color
        
        # 检查并清除完整的行
        self.clear_lines()
        
        # 生成新方块
        self.spawn_piece()
    
    def clear_lines(self):
        """清除完整的行"""
        lines_to_clear = []
        
        for y in range(GRID_HEIGHT):
            if all(cell != BLACK for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        # 清除行并计分
        for y in reversed(lines_to_clear):
            del self.grid[y]
            self.grid.insert(0, [BLACK for _ in range(GRID_WIDTH)])
        
        lines_count = len(lines_to_clear)
        if lines_count > 0:
            self.lines_cleared += lines_count
            # 计分规则：单行100分，双行300分，三行500分，四行800分
            score_multiplier = [0, 100, 300, 500, 800]
            self.score += score_multiplier[min(lines_count, 4)] * self.level
            
            # 升级逻辑：每10行升一级
            new_level = self.lines_cleared // 10 + 1
            if new_level > self.level:
                self.level = new_level
                self.fall_speed = max(50, 500 - (self.level - 1) * 50)
    
    def update(self, dt: int):
        """更新游戏状态"""
        if self.game_over or self.paused:
            return
            
        self.fall_time += dt
        
        if self.fall_time >= self.fall_speed:
            if not self.move_piece(0, 1):
                self.place_piece()
            self.fall_time = 0
    
    def hard_drop(self):
        """硬降（瞬间下降到底部）"""
        if not self.current_piece or self.game_over or self.paused:
            return
            
        while self.move_piece(0, 1):
            self.score += 2  # 硬降奖励分数
        self.place_piece()
    
    def toggle_pause(self):
        """切换暂停状态"""
        if not self.game_over:
            self.paused = not self.paused
    
    def restart(self):
        """重新开始游戏"""
        self.__init__()

def draw_grid(screen, game):
    """绘制游戏网格"""
    # 绘制网格背景
    grid_rect = pygame.Rect(GRID_X_OFFSET, GRID_Y_OFFSET, 
                           GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE)
    pygame.draw.rect(screen, DARK_GRAY, grid_rect)
    
    # 绘制已放置的方块
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            if game.grid[y][x] != BLACK:
                rect = pygame.Rect(GRID_X_OFFSET + x * CELL_SIZE,
                                 GRID_Y_OFFSET + y * CELL_SIZE,
                                 CELL_SIZE, CELL_SIZE)
                pygame.draw.rect(screen, game.grid[y][x], rect)
                pygame.draw.rect(screen, WHITE, rect, 1)
    
    # 绘制当前方块
    if game.current_piece and not game.game_over:
        for x, y in game.current_piece.get_cells():
            if 0 <= x < GRID_WIDTH and y >= 0:
                rect = pygame.Rect(GRID_X_OFFSET + x * CELL_SIZE,
                                 GRID_Y_OFFSET + y * CELL_SIZE,
                                 CELL_SIZE, CELL_SIZE)
                pygame.draw.rect(screen, game.current_piece.color, rect)
                pygame.draw.rect(screen, WHITE, rect, 1)
    
    # 绘制网格线
    for x in range(GRID_WIDTH + 1):
        pygame.draw.line(screen, GRAY,
                        (GRID_X_OFFSET + x * CELL_SIZE, GRID_Y_OFFSET),
                        (GRID_X_OFFSET + x * CELL_SIZE, GRID_Y_OFFSET + GRID_HEIGHT * CELL_SIZE))
    
    for y in range(GRID_HEIGHT + 1):
        pygame.draw.line(screen, GRAY,
                        (GRID_X_OFFSET, GRID_Y_OFFSET + y * CELL_SIZE),
                        (GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE, GRID_Y_OFFSET + y * CELL_SIZE))

def draw_next_piece(screen, game):
    """绘制下一个方块预览"""
    if not game.next_piece:
        return
        
    # 预览区域位置
    preview_x = GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 50
    preview_y = GRID_Y_OFFSET + 50
    
    # 绘制标题
    title = game.font.render("NEXT", True, WHITE)
    screen.blit(title, (preview_x, preview_y - 30))
    
    # 绘制预览方块
    shape = game.next_piece.get_rotated_shape()
    for i, row in enumerate(shape):
        for j, cell in enumerate(row):
            if cell == '#':
                rect = pygame.Rect(preview_x + j * 20,
                                 preview_y + i * 20,
                                 20, 20)
                pygame.draw.rect(screen, game.next_piece.color, rect)
                pygame.draw.rect(screen, WHITE, rect, 1)

def draw_info(screen, game):
    """绘制游戏信息"""
    info_x = GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 50
    info_y = GRID_Y_OFFSET + 200
    
    # 分数
    score_text = game.font.render(f"SCORE", True, WHITE)
    score_value = game.font.render(f"{game.score}", True, WHITE)
    screen.blit(score_text, (info_x, info_y))
    screen.blit(score_value, (info_x, info_y + 30))
    
    # 等级
    level_text = game.font.render(f"LEVEL", True, WHITE)
    level_value = game.font.render(f"{game.level}", True, WHITE)
    screen.blit(level_text, (info_x, info_y + 80))
    screen.blit(level_value, (info_x, info_y + 110))
    
    # 消除行数
    lines_text = game.font.render(f"LINES", True, WHITE)
    lines_value = game.font.render(f"{game.lines_cleared}", True, WHITE)
    screen.blit(lines_text, (info_x, info_y + 160))
    screen.blit(lines_value, (info_x, info_y + 190))

def draw_controls(screen, game):
    """绘制控制说明"""
    controls_x = GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 50
    controls_y = GRID_Y_OFFSET + 400

    controls = [
        "CONTROLS:",
        "A/D or ←/→ - Move",
        "S or ↓ - Soft Drop",
        "W or ↑ - Rotate",
        "SPACE - Hard Drop",
        "P - Pause",
        "R - Restart"
    ]

    for i, text in enumerate(controls):
        color = WHITE if i == 0 else GRAY
        font = game.font if i == 0 else game.small_font
        control_text = font.render(text, True, color)
        screen.blit(control_text, (controls_x, controls_y + i * 25))

def main():
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
    pygame.display.set_caption("俄罗斯方块 - Tetris")
    clock = pygame.time.Clock()
    
    game = TetrisGame()
    
    running = True
    while running:
        dt = clock.tick(60)
        
        # 处理事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                # 原有按键控制
                if event.key == pygame.K_a:  # 左移
                    game.move_piece(-1, 0)
                elif event.key == pygame.K_d:  # 右移
                    game.move_piece(1, 0)
                elif event.key == pygame.K_s:  # 软降
                    game.move_piece(0, 1)
                elif event.key == pygame.K_w:  # 旋转
                    game.rotate_piece()
                elif event.key == pygame.K_SPACE:  # 硬降
                    game.hard_drop()
                elif event.key == pygame.K_p:  # 暂停
                    game.toggle_pause()
                elif event.key == pygame.K_r:  # 重新开始
                    game.restart()

                # 新增方向键控制
                elif event.key == pygame.K_LEFT:  # 左方向键 - 左移
                    game.move_piece(-1, 0)
                elif event.key == pygame.K_RIGHT:  # 右方向键 - 右移
                    game.move_piece(1, 0)
                elif event.key == pygame.K_DOWN:  # 下方向键 - 下移
                    game.move_piece(0, 1)
                elif event.key == pygame.K_UP:  # 上方向键 - 旋转
                    game.rotate_piece()
        
        # 更新游戏
        game.update(dt)
        
        # 绘制
        screen.fill(BLACK)
        draw_grid(screen, game)
        draw_next_piece(screen, game)
        draw_info(screen, game)
        draw_controls(screen, game)
        
        # 绘制游戏状态文本
        if game.game_over:
            game_over_text = game.font.render("GAME OVER", True, RED)
            restart_text = game.small_font.render("Press R to restart", True, WHITE)
            screen.blit(game_over_text, (WINDOW_WIDTH // 2 - 80, WINDOW_HEIGHT // 2))
            screen.blit(restart_text, (WINDOW_WIDTH // 2 - 70, WINDOW_HEIGHT // 2 + 40))
        elif game.paused:
            pause_text = game.font.render("PAUSED", True, YELLOW)
            screen.blit(pause_text, (WINDOW_WIDTH // 2 - 50, WINDOW_HEIGHT // 2))
        
        pygame.display.flip()
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
