# 吞食天地2 - 游戏说明

## 🎮 游戏简介

《吞食天地2》是基于FC经典游戏的Python重制版，使用Pygame开发的三国题材回合制RPG游戏。游戏完整重现了原作的核心玩法，包括角色培养、回合制战斗、世界探索等经典要素。

## ⭐ 游戏特色

### 🏛️ 经典三国题材
- 包含刘备、关羽、张飞、诸葛亮等30+经典武将
- 真实的三国历史背景和人物关系
- 洛阳、长安、新野、成都等历史名城

### ⚔️ 策略战斗系统
- 回合制战斗，考验策略和智慧
- 攻击、技能、道具、防御、逃跑等多种行动
- 不同类型敌人：普通兵、精英、BOSS

### 👥 深度角色系统
- 4种职业：武将、军师、大将、弓兵
- 7项属性：HP、MP、攻击、防御、智力、敏捷、运气
- 完整的升级和装备系统

### 🗺️ 开放世界探索
- 20x15的大世界地图
- 7种地形：草地、森林、山地、水域、城市、城堡、村庄
- 随机遭遇和探索要素

## 🎯 游戏目标

1. **队伍建设**：招募和培养强大的武将队伍
2. **装备收集**：获得传说级武器和防具
3. **世界统一**：征服三国世界，成就霸业
4. **角色成长**：将武将培养到最高等级

## 🎮 操作指南

### 主菜单
- **↑↓ / W S**：选择菜单选项
- **回车 / 空格**：确认选择
- **ESC**：退出游戏

### 世界地图
- **WASD / 方向键**：移动角色
- **回车 / 空格**：进入城市/确认
- **ESC**：返回主菜单

### 战斗界面
- **↑↓ / W S**：选择行动或目标
- **回车 / 空格**：确认行动
- **ESC**：返回地图（战斗结束后）

## 📊 游戏系统详解

### 角色职业特色

| 职业 | 特点 | 属性加成 | 适合玩法 |
|------|------|----------|----------|
| 武将 | 高攻击力 | 攻击+10, 防御+5, HP+20 | 前排输出 |
| 军师 | 高智力 | 智力+15, MP+30, 攻击-5 | 技能支援 |
| 大将 | 平衡发展 | 全属性+5, HP+10, MP+10 | 队长角色 |
| 弓兵 | 高敏捷 | 敏捷+10, 攻击+8, 防御-3 | 速度优势 |

### 地形效果

| 地形 | 遭遇率 | 常见敌人 | 特殊效果 |
|------|--------|----------|----------|
| 草地 | 10% | 流寇、山贼 | 移动便利 |
| 森林 | 15% | 盗贼、野兽 | 容易迷路 |
| 山地 | 20% | 强盗、猛虎 | 地势险要 |
| 城市 | 0% | 无 | 商店、客栈 |

### 装备等级

| 等级 | 武器示例 | 攻击力 | 价格 |
|------|----------|--------|------|
| 初级 | 木剑 | +5 | 50金 |
| 中级 | 铁剑 | +12 | 200金 |
| 高级 | 钢剑 | +20 | 500金 |
| 传说 | 青龙偃月刀 | +35 | 2000金 |

## 🏰 重要城市介绍

### 洛阳 - 东汉都城
- **统治者**：汉献帝
- **人口**：100,000
- **特色**：最繁华的城市，高级装备和道具
- **可招募**：董卓、李儒、华雄

### 长安 - 西部重镇
- **统治者**：董卓
- **人口**：80,000
- **特色**：军事要地，武器装备丰富
- **可招募**：张辽、高顺

### 新野 - 刘备根据地
- **统治者**：刘备
- **人口**：5,000
- **特色**：起始城市，基础装备
- **可招募**：徐庶、伊籍

## 💎 珍贵道具

### 恢复道具
- **药草**：恢复50HP，20金
- **高级药草**：恢复150HP，80金
- **万能药**：完全恢复HP，300金
- **复活药**：复活倒下同伴，500金

### 永久提升道具
- **力量果实**：永久+5攻击力，1000金
- **防御果实**：永久+5防御力，1000金
- **智慧果实**：永久+5智力，1000金

### 特殊道具
- **传国玉玺**：象征皇权的印玺
- **城门钥匙**：打开特定城门
- **宝箱钥匙**：打开宝箱

## 🎯 游戏攻略提示

### 新手指南
1. **熟悉操作**：先在安全区域练习移动和战斗
2. **平衡发展**：不要只培养一个角色
3. **装备重要**：及时更新武器和防具
4. **善用道具**：战斗中合理使用恢复道具

### 进阶策略
1. **职业搭配**：武将+军师+大将的经典组合
2. **地形利用**：避开高危险地区，选择合适路线
3. **资源管理**：合理分配金币和经验值
4. **角色招募**：优先招募传说武将

### 战斗技巧
1. **目标选择**：优先攻击血量低的敌人
2. **防御时机**：血量不足时选择防御恢复
3. **逃跑判断**：遇到强敌时果断逃跑
4. **技能运用**：合理使用MP和特殊技能

## 🔧 技术信息

### 系统要求
- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.8+
- **依赖库**：pygame 2.0+
- **内存**：512MB RAM
- **存储**：100MB 可用空间

### 文件说明
- `sangokushi2.py` - 主游戏程序
- `saves/` - 存档目录（自动创建）
- `assets/` - 游戏资源目录
- `test_sangokushi.py` - 系统测试程序
- `demo_sangokushi.py` - 功能演示程序

### 存档系统
- 支持10个存档槽位
- JSON格式存储
- 包含完整游戏状态
- 支持快速存取

## 🐛 常见问题

### Q: 游戏无法启动？
A: 确保已安装Python 3.8+和pygame库

### Q: 没有声音？
A: 音频系统可选，不影响游戏运行

### Q: 存档丢失？
A: 检查saves目录是否存在，避免删除存档文件

### Q: 游戏卡顿？
A: 关闭其他程序，确保有足够内存

## 📞 联系方式

如有问题或建议，欢迎反馈！

---

**祝您游戏愉快，早日统一三国！** 🏆
