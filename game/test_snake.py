import unittest
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入游戏模块
from 贪吃蛇 import SnakeGame, Direction, GameState

class TestSnakeGame(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        # 创建游戏实例但不初始化pygame显示
        import pygame
        pygame.init()
        self.game = SnakeGame()
    
    def test_initial_state(self):
        """测试初始状态"""
        # 检查蛇的初始长度
        self.assertEqual(len(self.game.snake), 3)
        
        # 检查初始方向
        self.assertEqual(self.game.direction, Direction.RIGHT)
        
        # 检查初始分数
        self.assertEqual(self.game.score, 0)
        
        # 检查游戏状态
        self.assertEqual(self.game.game_state, GameState.PLAYING)
    
    def test_snake_movement(self):
        """测试蛇的移动"""
        initial_head = self.game.snake[0]
        initial_length = len(self.game.snake)
        
        # 模拟一次移动
        self.game.update_game()
        
        # 检查头部位置是否改变
        new_head = self.game.snake[0]
        self.assertNotEqual(initial_head, new_head)
        
        # 检查长度是否保持不变（没有吃到食物）
        if new_head != self.game.food:
            self.assertEqual(len(self.game.snake), initial_length)
    
    def test_direction_change(self):
        """测试方向改变"""
        # 改变方向
        self.game.next_direction = Direction.UP
        self.game.update_game()
        
        # 检查方向是否改变
        self.assertEqual(self.game.direction, Direction.UP)
    
    def test_food_generation(self):
        """测试食物生成"""
        food = self.game.generate_food()
        
        # 检查食物位置是否在有效范围内
        self.assertGreaterEqual(food[0], 0)
        self.assertLess(food[0], 25)  # GRID_WIDTH
        self.assertGreaterEqual(food[1], 0)
        self.assertLess(food[1], 20)  # GRID_HEIGHT
        
        # 检查食物是否不在蛇身上
        self.assertNotIn(food, self.game.snake)
    
    def test_collision_detection(self):
        """测试碰撞检测"""
        # 测试边界碰撞
        # 将蛇移动到边界
        self.game.snake = [(24, 10)]  # 右边界
        self.game.direction = Direction.RIGHT
        self.game.next_direction = Direction.RIGHT
        
        self.game.update_game()
        
        # 检查游戏是否结束
        self.assertEqual(self.game.game_state, GameState.GAME_OVER)
    
    def test_reset_game(self):
        """测试游戏重置"""
        # 修改游戏状态
        self.game.score = 100
        self.game.game_state = GameState.GAME_OVER
        
        # 重置游戏
        self.game.reset_game()
        
        # 检查是否重置成功
        self.assertEqual(self.game.score, 0)
        self.assertEqual(self.game.game_state, GameState.PLAYING)
        self.assertEqual(len(self.game.snake), 3)

def run_tests():
    """运行测试"""
    print("开始测试贪吃蛇游戏...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSnakeGame)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！游戏功能正常。")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_tests()
