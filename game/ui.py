#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI系统 - 游戏界面、菜单、对话框
"""

import pygame
import os
from typing import List, Dict, Tuple, Optional
from enum import Enum

class UIManager:
    """UI管理器"""
    
    def __init__(self, screen: pygame.Surface, resource_manager):
        self.screen = screen
        self.resource_manager = resource_manager
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()

        # 字体初始化
        pygame.font.init()

        # 强制加载中文字体
        self.font_large = self._load_chinese_font_force(36)
        self.font_medium = self._load_chinese_font_force(24)
        self.font_small = self._load_chinese_font_force(18)
        
        # 颜色定义
        self.colors = {
            'white': (255, 255, 255),
            'black': (0, 0, 0),
            'red': (255, 0, 0),
            'green': (0, 255, 0),
            'blue': (0, 0, 255),
            'yellow': (255, 255, 0),
            'gray': (128, 128, 128),
            'dark_gray': (64, 64, 64),
            'light_gray': (192, 192, 192),
            'orange': (255, 165, 0),
            'purple': (128, 0, 128),
        }
        
        # 主菜单状态
        self.menu_options = ["开始游戏", "故事模式", "读取存档", "游戏设置", "退出游戏"]
        self.menu_selected = 0
        self.should_start = False
        self.story_mode = False
        
        # 地图UI状态
        self.show_minimap = True
        self.show_status = True

    def _load_chinese_font(self, size):
        """加载中文字体"""
        # 常见的中文字体列表
        chinese_fonts = [
            "SimHei",           # 黑体
            "Microsoft YaHei",  # 微软雅黑
            "SimSun",          # 宋体
            "KaiTi",           # 楷体
            "FangSong",        # 仿宋
            "NSimSun",         # 新宋体
            "Microsoft JhengHei", # 微软正黑体
        ]

        # 尝试加载系统中文字体
        for font_name in chinese_fonts:
            try:
                font = pygame.font.SysFont(font_name, size)
                # 测试字体是否支持中文
                test_surface = font.render("测试", True, (255, 255, 255))
                if test_surface.get_width() > 0:
                    print(f"成功加载中文字体: {font_name}")
                    return font
            except:
                continue

        # 如果系统字体都不可用，尝试加载字体文件
        font_files = [
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/msyh.ttf",        # 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/kaiti.ttf",       # 楷体
        ]

        for font_file in font_files:
            try:
                if os.path.exists(font_file):
                    font = pygame.font.Font(font_file, size)
                    print(f"成功加载字体文件: {font_file}")
                    return font
            except:
                continue

        print(f"警告: 无法加载中文字体，将使用默认字体")
        return None

    def _load_chinese_font_force(self, size):
        """强制加载中文字体 - 使用测试确认的宋体"""
        # 直接使用宋体字体文件（测试确认可用）
        simsun_path = "C:/Windows/Fonts/simsun.ttc"

        try:
            if os.path.exists(simsun_path):
                font = pygame.font.Font(simsun_path, size)
                # 测试中文渲染
                test_surface = font.render("测试", True, (255, 255, 255))
                if test_surface.get_width() > 0:
                    print(f"成功加载宋体字体: {simsun_path} (大小: {size})")
                    return font
        except Exception as e:
            print(f"加载宋体字体文件失败: {e}")

        # 备用方案：尝试系统宋体
        try:
            font = pygame.font.SysFont("SimSun", size)
            test_surface = font.render("测试", True, (255, 255, 255))
            if test_surface.get_width() > 0:
                print(f"成功加载系统宋体字体 (大小: {size})")
                return font
        except Exception as e:
            print(f"加载系统宋体失败: {e}")

        # 最后的备用方案
        print(f"警告: 无法加载宋体字体，使用默认字体 (大小: {size})")
        return pygame.font.Font(None, size)

    def _render_text(self, text, font, color):
        """渲染文本，自动处理中文编码"""
        try:
            # 确保文本是字符串类型
            if not isinstance(text, str):
                text = str(text)

            # 强制使用UTF-8编码
            if isinstance(text, str):
                text = text.encode('utf-8').decode('utf-8')

            # 尝试渲染文本
            surface = font.render(text, True, color)

            # 检查渲染是否成功（宽度大于0）
            if surface.get_width() > 0:
                return surface
            else:
                # 如果渲染失败，尝试使用系统默认字体
                return self._render_with_fallback_font(text, color)

        except Exception as e:
            print(f"文本渲染失败: {text}, 错误: {e}")
            return self._render_with_fallback_font(text, color)

    def _render_with_fallback_font(self, text, color):
        """使用备用字体渲染文本"""
        # 尝试使用不同的字体
        fallback_fonts = [
            pygame.font.SysFont("SimHei", 24),
            pygame.font.SysFont("Microsoft YaHei", 24),
            pygame.font.SysFont("SimSun", 24),
            pygame.font.Font(None, 24)
        ]

        for font in fallback_fonts:
            try:
                surface = font.render(text, True, color)
                if surface.get_width() > 0:
                    return surface
            except:
                continue

        # 最后的备用方案：创建一个简单的矩形
        surface = pygame.Surface((len(text) * 12, 20))
        surface.fill(color)
        return surface
    
    def render_main_menu(self):
        """渲染主菜单"""
        # 背景
        self.screen.fill(self.colors['black'])

        # 游戏标题
        title_text = self._render_text("吞食天地2", self.font_large, self.colors['yellow'])
        title_rect = title_text.get_rect(center=(self.screen_width // 2, 150))
        self.screen.blit(title_text, title_rect)

        subtitle_text = self._render_text("Sangokushi 2", self.font_medium, self.colors['white'])
        subtitle_rect = subtitle_text.get_rect(center=(self.screen_width // 2, 180))
        self.screen.blit(subtitle_text, subtitle_rect)
        
        # 菜单选项
        menu_start_y = 250
        for i, option in enumerate(self.menu_options):
            color = self.colors['yellow'] if i == self.menu_selected else self.colors['white']
            prefix = "► " if i == self.menu_selected else "  "

            option_text = self._render_text(f"{prefix}{option}", self.font_medium, color)
            option_rect = option_text.get_rect(center=(self.screen_width // 2, menu_start_y + i * 40))
            self.screen.blit(option_text, option_rect)
        
        # 控制说明
        controls = [
            "控制说明:",
            "↑↓ / WS - 选择",
            "回车 / 空格 - 确认",
            "ESC - 返回/退出"
        ]

        control_start_y = self.screen_height - 120
        for i, control in enumerate(controls):
            color = self.colors['yellow'] if i == 0 else self.colors['light_gray']
            font = self.font_small if i > 0 else self.font_medium

            control_text = self._render_text(control, font, color)
            control_rect = control_text.get_rect(center=(self.screen_width // 2, control_start_y + i * 20))
            self.screen.blit(control_text, control_rect)
    
    def render_world_map_ui(self):
        """渲染世界地图UI"""
        # 这里会被具体的地图渲染替代
        pass
    
    def render_battle_ui(self):
        """渲染战斗UI"""
        # 这里会被具体的战斗界面渲染替代
        pass
    
    def render_fc_map(self, fc_world_map):
        """渲染FC风格世界地图"""
        # 清空屏幕
        self.screen.fill(self.colors['black'])

        # 计算地图渲染参数
        tile_size = fc_world_map.tile_size
        map_start_x = 50
        map_start_y = 50

        # 渲染可见的地图格子
        visible_tiles = fc_world_map.get_visible_tiles()

        for map_x, map_y, terrain, city_name in visible_tiles:
            # 计算屏幕坐标
            screen_x = map_start_x + (map_x - fc_world_map.camera_x) * tile_size
            screen_y = map_start_y + (map_y - fc_world_map.camera_y) * tile_size

            # 获取地形颜色
            color = fc_world_map.get_terrain_color(terrain)

            # 绘制地形
            pygame.draw.rect(self.screen, color,
                           (screen_x, screen_y, tile_size, tile_size))

            # 绘制边框
            pygame.draw.rect(self.screen, (100, 100, 100),
                           (screen_x, screen_y, tile_size, tile_size), 1)

            # 绘制地形字符
            char = fc_world_map.get_terrain_char(terrain)
            char_text = self._render_text(char, self.font_small, self.colors['white'])
            char_rect = char_text.get_rect(center=(screen_x + tile_size // 2,
                                                  screen_y + tile_size // 2))
            self.screen.blit(char_text, char_rect)

            # 如果是城市，显示名称
            if city_name:
                name_text = self._render_text(city_name, self.font_small, self.colors['yellow'])
                name_rect = name_text.get_rect(center=(screen_x + tile_size // 2,
                                                      screen_y + tile_size + 12))
                self.screen.blit(name_text, name_rect)

        # 渲染玩家 (FC风格小人)
        player_screen_x = map_start_x + (fc_world_map.player_x - fc_world_map.camera_x) * tile_size
        player_screen_y = map_start_y + (fc_world_map.player_y - fc_world_map.camera_y) * tile_size

        # FC风格玩家图标
        player_rect = pygame.Rect(player_screen_x + 4, player_screen_y + 4, tile_size - 8, tile_size - 8)
        pygame.draw.ellipse(self.screen, self.colors['red'], player_rect)
        pygame.draw.ellipse(self.screen, self.colors['white'], player_rect, 2)

        # 渲染UI面板
        self._render_fc_map_info_panel(fc_world_map)

    def render_map(self, world_map):
        """渲染世界地图 (兼容旧版本)"""
        if hasattr(world_map, 'get_visible_tiles') and hasattr(world_map, 'tile_size'):
            # FC风格地图
            self.render_fc_map(world_map)
        else:
            # 原版地图渲染逻辑
            self._render_legacy_map(world_map)
    
    def _render_map_info_panel(self, world_map):
        """渲染地图信息面板"""
        panel_x = self.screen_width - 200
        panel_y = 20
        panel_width = 180
        panel_height = 150
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, self.colors['dark_gray'], 
                        (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(self.screen, self.colors['white'], 
                        (panel_x, panel_y, panel_width, panel_height), 2)
        
        # 当前位置信息
        current_tile = world_map.get_current_tile()
        if current_tile:
            info_lines = [
                f"位置: ({world_map.player_x}, {world_map.player_y})",
                f"地形: {current_tile.terrain.value}",
            ]
            
            if current_tile.name:
                info_lines.append(f"地点: {current_tile.name}")
            
            for i, line in enumerate(info_lines):
                text = self._render_text(line, self.font_small, self.colors['white'])
                self.screen.blit(text, (panel_x + 10, panel_y + 20 + i * 20))

    def _render_fc_map_info_panel(self, fc_world_map):
        """渲染FC地图信息面板"""
        panel_x = self.screen_width - 220
        panel_y = 20
        panel_width = 200
        panel_height = 200

        # 绘制面板背景
        pygame.draw.rect(self.screen, self.colors['dark_gray'],
                        (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(self.screen, self.colors['white'],
                        (panel_x, panel_y, panel_width, panel_height), 2)

        # 标题
        title_text = self._render_text("地图信息", self.font_medium, self.colors['yellow'])
        self.screen.blit(title_text, (panel_x + 10, panel_y + 10))

        # 当前位置信息
        location = fc_world_map.get_current_location()
        info_lines = [
            f"位置: ({fc_world_map.player_x}, {fc_world_map.player_y})",
            f"地点: {location}",
            f"遭遇率: {fc_world_map.get_encounter_rate():.1%}",
        ]

        # 检查是否可以进入城市
        if fc_world_map.can_enter_city():
            info_lines.append("按空格进入城市")

        for i, line in enumerate(info_lines):
            text = self._render_text(line, self.font_small, self.colors['white'])
            self.screen.blit(text, (panel_x + 10, panel_y + 40 + i * 20))

        # 小地图
        minimap_y = panel_y + 120
        minimap_size = 80
        minimap_scale = 2

        pygame.draw.rect(self.screen, self.colors['black'],
                        (panel_x + 10, minimap_y, minimap_size, minimap_size))
        pygame.draw.rect(self.screen, self.colors['white'],
                        (panel_x + 10, minimap_y, minimap_size, minimap_size), 1)

        # 绘制小地图
        for dx in range(-minimap_scale, minimap_scale + 1):
            for dy in range(-minimap_scale, minimap_scale + 1):
                map_x = fc_world_map.player_x + dx
                map_y = fc_world_map.player_y + dy

                if 0 <= map_x < fc_world_map.width and 0 <= map_y < fc_world_map.height:
                    terrain = fc_world_map.map_data.get((map_x, map_y))
                    if terrain:
                        color = fc_world_map.get_terrain_color(terrain)

                        mini_x = panel_x + 10 + (dx + minimap_scale) * (minimap_size // (minimap_scale * 2 + 1))
                        mini_y = minimap_y + (dy + minimap_scale) * (minimap_size // (minimap_scale * 2 + 1))
                        mini_size = minimap_size // (minimap_scale * 2 + 1)

                        pygame.draw.rect(self.screen, color, (mini_x, mini_y, mini_size, mini_size))

        # 玩家在小地图上的位置
        player_mini_x = panel_x + 10 + minimap_scale * (minimap_size // (minimap_scale * 2 + 1))
        player_mini_y = minimap_y + minimap_scale * (minimap_size // (minimap_scale * 2 + 1))
        mini_size = minimap_size // (minimap_scale * 2 + 1)

        pygame.draw.rect(self.screen, self.colors['red'],
                        (player_mini_x, player_mini_y, mini_size, mini_size))

    def _render_party_info_with_portraits(self, character_manager, portrait_manager):
        """渲染带头像的队伍信息"""
        party_panel_x = self.screen_width - 300
        party_panel_y = 50

        title_text = self._render_text("队伍", self.font_medium, self.colors['blue'])
        self.screen.blit(title_text, (party_panel_x, party_panel_y))

        for i, character in enumerate(character_manager.party):
            y_offset = party_panel_y + 30 + i * 100

            # 渲染角色头像
            if portrait_manager:
                portrait = portrait_manager.get_portrait(character.name)
                portrait_rect = pygame.Rect(party_panel_x, y_offset, 64, 64)

                # 缩放头像到合适大小
                scaled_portrait = pygame.transform.scale(portrait, (48, 48))
                self.screen.blit(scaled_portrait, (party_panel_x, y_offset))

                # 头像边框
                border_color = self.colors['white'] if character.is_alive() else self.colors['gray']
                pygame.draw.rect(self.screen, border_color, (party_panel_x, y_offset, 48, 48), 2)

            # 角色信息
            info_x = party_panel_x + 60

            # 角色名称和等级
            name_color = self.colors['white'] if character.is_alive() else self.colors['gray']
            name_text = self._render_text(f"{character.name} Lv.{character.level}", self.font_small, name_color)
            self.screen.blit(name_text, (info_x, y_offset))

            if character.is_alive():
                # HP条
                hp_ratio = character.current_hp / character.max_hp
                hp_bar_width = 120
                hp_bar_height = 8

                pygame.draw.rect(self.screen, self.colors['dark_gray'],
                               (info_x, y_offset + 20, hp_bar_width, hp_bar_height))

                hp_fill_width = int(hp_bar_width * hp_ratio)
                hp_color = self.colors['green'] if hp_ratio > 0.5 else self.colors['yellow'] if hp_ratio > 0.2 else self.colors['red']
                pygame.draw.rect(self.screen, hp_color,
                               (info_x, y_offset + 20, hp_fill_width, hp_bar_height))

                # MP条
                mp_ratio = character.current_mp / character.max_mp
                pygame.draw.rect(self.screen, self.colors['dark_gray'],
                               (info_x, y_offset + 35, hp_bar_width, hp_bar_height))

                mp_fill_width = int(hp_bar_width * mp_ratio)
                pygame.draw.rect(self.screen, self.colors['blue'],
                               (info_x, y_offset + 35, mp_fill_width, hp_bar_height))

                # 数值显示
                hp_text = self._render_text(f"HP: {character.current_hp}/{character.max_hp}", self.font_small, self.colors['white'])
                self.screen.blit(hp_text, (info_x, y_offset + 50))

                mp_text = self._render_text(f"MP: {character.current_mp}/{character.max_mp}", self.font_small, self.colors['white'])
                self.screen.blit(mp_text, (info_x, y_offset + 65))
            else:
                # 死亡状态
                dead_text = self._render_text("战斗不能", self.font_small, self.colors['red'])
                self.screen.blit(dead_text, (info_x, y_offset + 20))
    
    def render_battle_screen(self, battle_system, character_manager, portrait_manager=None):
        """渲染战斗界面"""
        # 清空屏幕
        self.screen.fill(self.colors['black'])

        # 渲染敌人信息
        self._render_enemy_info(battle_system)

        # 渲染玩家队伍信息 (带头像)
        self._render_party_info_with_portraits(character_manager, portrait_manager)

        # 渲染行动菜单
        self._render_action_menu(battle_system)

        # 渲染战斗日志
        self._render_battle_log(battle_system)

        # 渲染当前角色指示
        self._render_current_character_indicator(battle_system, character_manager)
    
    def _render_enemy_info(self, battle_system):
        """渲染敌人信息"""
        enemy_panel_x = 50
        enemy_panel_y = 50
        
        alive_enemies = [e for e in battle_system.enemies if e.is_alive()]
        
        title_text = self._render_text("敌人", self.font_medium, self.colors['red'])
        self.screen.blit(title_text, (enemy_panel_x, enemy_panel_y))
        
        for i, enemy in enumerate(alive_enemies):
            y_offset = enemy_panel_y + 30 + i * 60
            
            # 敌人名称
            name_color = self.colors['yellow'] if i == battle_system.target_menu_index and battle_system.show_target_menu else self.colors['white']
            name_text = self._render_text(enemy.name, self.font_small, name_color)
            self.screen.blit(name_text, (enemy_panel_x, y_offset))
            
            # HP条
            hp_ratio = enemy.current_hp / enemy.max_hp
            hp_bar_width = 100
            hp_bar_height = 10
            
            # HP条背景
            pygame.draw.rect(self.screen, self.colors['dark_gray'], 
                           (enemy_panel_x, y_offset + 20, hp_bar_width, hp_bar_height))
            
            # HP条前景
            hp_fill_width = int(hp_bar_width * hp_ratio)
            hp_color = self.colors['green'] if hp_ratio > 0.5 else self.colors['yellow'] if hp_ratio > 0.2 else self.colors['red']
            pygame.draw.rect(self.screen, hp_color, 
                           (enemy_panel_x, y_offset + 20, hp_fill_width, hp_bar_height))
            
            # HP数值
            hp_text = self._render_text(f"{enemy.current_hp}/{enemy.max_hp}", self.font_small, self.colors['white'])
            self.screen.blit(hp_text, (enemy_panel_x + hp_bar_width + 10, y_offset + 15))
    
    def _render_party_info(self, character_manager):
        """渲染队伍信息"""
        party_panel_x = self.screen_width - 250
        party_panel_y = 50
        
        title_text = self.font_medium.render("队伍", True, self.colors['blue'])
        self.screen.blit(title_text, (party_panel_x, party_panel_y))
        
        for i, character in enumerate(character_manager.party):
            y_offset = party_panel_y + 30 + i * 80
            
            # 角色名称
            name_color = self.colors['white'] if character.is_alive() else self.colors['gray']
            name_text = self.font_small.render(f"{character.name} Lv.{character.level}", True, name_color)
            self.screen.blit(name_text, (party_panel_x, y_offset))
            
            if character.is_alive():
                # HP条
                hp_ratio = character.current_hp / character.max_hp
                hp_bar_width = 120
                hp_bar_height = 8
                
                pygame.draw.rect(self.screen, self.colors['dark_gray'], 
                               (party_panel_x, y_offset + 20, hp_bar_width, hp_bar_height))
                
                hp_fill_width = int(hp_bar_width * hp_ratio)
                hp_color = self.colors['green'] if hp_ratio > 0.5 else self.colors['yellow'] if hp_ratio > 0.2 else self.colors['red']
                pygame.draw.rect(self.screen, hp_color, 
                               (party_panel_x, y_offset + 20, hp_fill_width, hp_bar_height))
                
                # MP条
                mp_ratio = character.current_mp / character.max_mp
                pygame.draw.rect(self.screen, self.colors['dark_gray'], 
                               (party_panel_x, y_offset + 35, hp_bar_width, hp_bar_height))
                
                mp_fill_width = int(hp_bar_width * mp_ratio)
                pygame.draw.rect(self.screen, self.colors['blue'], 
                               (party_panel_x, y_offset + 35, mp_fill_width, hp_bar_height))
                
                # 数值显示
                hp_text = self.font_small.render(f"HP: {character.current_hp}/{character.max_hp}", True, self.colors['white'])
                self.screen.blit(hp_text, (party_panel_x, y_offset + 50))
                
                mp_text = self.font_small.render(f"MP: {character.current_mp}/{character.max_mp}", True, self.colors['white'])
                self.screen.blit(mp_text, (party_panel_x, y_offset + 65))
            else:
                # 死亡状态
                dead_text = self.font_small.render("战斗不能", True, self.colors['red'])
                self.screen.blit(dead_text, (party_panel_x, y_offset + 20))
    
    def _render_action_menu(self, battle_system):
        """渲染行动菜单"""
        if battle_system.show_target_menu:
            return  # 显示目标选择时不显示行动菜单
        
        menu_x = 50
        menu_y = self.screen_height - 150
        
        title_text = self.font_medium.render("行动选择", True, self.colors['yellow'])
        self.screen.blit(title_text, (menu_x, menu_y))
        
        from battle import BattleAction
        actions = list(BattleAction)
        
        for i, action in enumerate(actions):
            y_offset = menu_y + 30 + i * 25
            color = self.colors['yellow'] if i == battle_system.action_menu_index else self.colors['white']
            prefix = "► " if i == battle_system.action_menu_index else "  "
            
            action_text = self.font_small.render(f"{prefix}{action.value}", True, color)
            self.screen.blit(action_text, (menu_x, y_offset))
    
    def _render_battle_log(self, battle_system):
        """渲染战斗日志"""
        log_x = 300
        log_y = self.screen_height - 150
        
        title_text = self.font_medium.render("战斗记录", True, self.colors['white'])
        self.screen.blit(title_text, (log_x, log_y))
        
        # 显示最近的几条日志
        recent_logs = battle_system.battle_log[-5:]
        for i, log_entry in enumerate(recent_logs):
            y_offset = log_y + 25 + i * 20
            log_text = self.font_small.render(log_entry, True, self.colors['light_gray'])
            self.screen.blit(log_text, (log_x, y_offset))
    
    def _render_current_character_indicator(self, battle_system, character_manager):
        """渲染当前角色指示"""
        if battle_system.state.value != "player_turn":
            return
        
        current_char = battle_system._get_current_character()
        if not current_char:
            return
        
        indicator_text = self.font_medium.render(f"当前行动: {current_char.name}", True, self.colors['yellow'])
        indicator_rect = indicator_text.get_rect(center=(self.screen_width // 2, 30))
        self.screen.blit(indicator_text, indicator_rect)
    
    def menu_move_up(self):
        """菜单向上移动"""
        self.menu_selected = (self.menu_selected - 1) % len(self.menu_options)
    
    def menu_move_down(self):
        """菜单向下移动"""
        self.menu_selected = (self.menu_selected + 1) % len(self.menu_options)
    
    def menu_move_left(self):
        """菜单向左移动"""
        pass  # 主菜单不需要左右移动
    
    def menu_move_right(self):
        """菜单向右移动"""
        pass  # 主菜单不需要左右移动
    
    def handle_menu_confirm(self):
        """处理菜单确认"""
        if self.menu_selected == 0:  # 开始游戏
            self.should_start = True
        elif self.menu_selected == 3:  # 退出游戏
            import sys
            sys.exit()
    
    def should_start_game(self) -> bool:
        """是否应该开始游戏"""
        return self.should_start
    
    def reset_menu_state(self):
        """重置菜单状态"""
        self.should_start = False
