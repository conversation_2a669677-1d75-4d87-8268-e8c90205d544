#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FC吞食天地2 ROM分析工具
"""

import os
import struct

def analyze_nes_rom(rom_path):
    """分析NES ROM文件"""
    if not os.path.exists(rom_path):
        print(f"ROM文件不存在: {rom_path}")
        return
    
    with open(rom_path, 'rb') as f:
        # 读取NES文件头 (16字节)
        header = f.read(16)
        
        if header[:4] != b'NES\x1a':
            print("这不是有效的NES ROM文件")
            return
        
        # 解析文件头
        prg_size = header[4] * 16384  # PRG-ROM大小
        chr_size = header[5] * 8192   # CHR-ROM大小
        flags6 = header[6]
        flags7 = header[7]
        
        print("=== NES ROM信息 ===")
        print(f"PRG-ROM大小: {prg_size} 字节 ({header[4]} * 16KB)")
        print(f"CHR-ROM大小: {chr_size} 字节 ({header[5]} * 8KB)")
        print(f"Mapper: {(flags7 & 0xF0) | (flags6 >> 4)}")
        print(f"镜像模式: {'垂直' if flags6 & 1 else '水平'}")
        
        # 跳过trainer（如果存在）
        if flags6 & 4:
            f.read(512)
        
        # 读取PRG-ROM数据
        prg_data = f.read(prg_size)
        print(f"\nPRG-ROM数据读取: {len(prg_data)} 字节")
        
        # 读取CHR-ROM数据（图像数据）
        if chr_size > 0:
            chr_data = f.read(chr_size)
            print(f"CHR-ROM数据读取: {len(chr_data)} 字节")
            
            # 分析图像数据
            analyze_chr_data(chr_data)
        
        # 分析PRG数据中的可能文本
        analyze_text_data(prg_data)
        
        # 查找地图数据模式
        analyze_map_data(prg_data)

def analyze_chr_data(chr_data):
    """分析CHR-ROM图像数据"""
    print("\n=== 图像数据分析 ===")
    
    # CHR-ROM包含8x8像素的瓦片
    tile_count = len(chr_data) // 16  # 每个瓦片16字节
    print(f"瓦片数量: {tile_count}")
    
    # 分析前几个瓦片的数据模式
    print("前10个瓦片数据预览:")
    for i in range(min(10, tile_count)):
        tile_offset = i * 16
        tile_data = chr_data[tile_offset:tile_offset + 16]
        
        # 显示瓦片的十六进制数据
        hex_data = ' '.join(f'{b:02X}' for b in tile_data[:8])
        print(f"瓦片 {i:2d}: {hex_data}...")

def analyze_text_data(prg_data):
    """分析PRG数据中的文本"""
    print("\n=== 文本数据分析 ===")
    
    # 查找可能的中文文本（通常使用特定编码）
    # FC游戏中文本可能使用自定义字符映射
    
    # 查找重复的字节模式（可能是文本）
    text_patterns = []
    
    # 简单的文本检测：查找连续的可打印ASCII范围
    i = 0
    while i < len(prg_data) - 10:
        if 0x20 <= prg_data[i] <= 0x7E:  # 可打印ASCII
            text_start = i
            while i < len(prg_data) and 0x20 <= prg_data[i] <= 0x7E:
                i += 1
            
            if i - text_start >= 5:  # 至少5个字符
                text = prg_data[text_start:i].decode('ascii', errors='ignore')
                text_patterns.append((text_start, text))
        else:
            i += 1
    
    print(f"发现 {len(text_patterns)} 个可能的文本段:")
    for offset, text in text_patterns[:10]:  # 显示前10个
        print(f"  偏移 0x{offset:04X}: {text}")

def analyze_map_data(prg_data):
    """分析地图数据"""
    print("\n=== 地图数据分析 ===")
    
    # 查找重复的数据模式（可能是地图）
    # FC游戏地图通常使用瓦片ID数组
    
    # 查找大小合适的数据块
    possible_maps = []
    
    # 常见的地图大小
    map_sizes = [
        (32, 32),   # 32x32
        (64, 64),   # 64x64
        (32, 24),   # 32x24
        (40, 30),   # 40x30
    ]
    
    for width, height in map_sizes:
        map_size = width * height
        
        # 在ROM中查找这个大小的数据块
        for offset in range(0, len(prg_data) - map_size, 0x100):  # 每256字节检查一次
            map_data = prg_data[offset:offset + map_size]
            
            # 检查数据的变化程度（地图数据通常有一定的变化但不会太随机）
            unique_values = len(set(map_data))
            if 4 <= unique_values <= 64:  # 合理的瓦片种类数
                possible_maps.append((offset, width, height, unique_values))
    
    print(f"发现 {len(possible_maps)} 个可能的地图数据:")
    for offset, width, height, unique_vals in possible_maps[:5]:  # 显示前5个
        print(f"  偏移 0x{offset:04X}: {width}x{height}, {unique_vals}种瓦片")

def extract_data_samples(rom_path, output_dir="rom_analysis"):
    """提取ROM数据样本"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    with open(rom_path, 'rb') as f:
        # 跳过文件头
        header = f.read(16)
        if header[6] & 4:  # 跳过trainer
            f.read(512)
        
        # 提取PRG-ROM前4KB作为样本
        prg_sample = f.read(4096)
        with open(f"{output_dir}/prg_sample.bin", 'wb') as out:
            out.write(prg_sample)
        
        # 提取CHR-ROM前2KB作为样本
        f.seek(16 + (512 if header[6] & 4 else 0) + header[4] * 16384)
        chr_sample = f.read(2048)
        with open(f"{output_dir}/chr_sample.bin", 'wb') as out:
            out.write(chr_sample)
        
        print(f"数据样本已保存到 {output_dir}/ 目录")

def main():
    """主函数"""
    print("FC吞食天地2 ROM分析工具")
    print("=" * 40)
    
    # ROM文件路径
    rom_path = "game/sangokushi2.nes"
    
    if os.path.exists(rom_path):
        print(f"分析ROM文件: {rom_path}")
        analyze_nes_rom(rom_path)
        extract_data_samples(rom_path)
    else:
        print(f"请将吞食天地2的ROM文件重命名为 '{rom_path}' 并放在当前目录")
        print("然后重新运行此脚本")
        
        print("\n支持的ROM文件格式:")
        print("- .nes (iNES格式)")
        print("- 文件大小通常为128KB-512KB")

if __name__ == "__main__":
    main()
