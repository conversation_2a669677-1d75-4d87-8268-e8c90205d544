#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
吞食天地2游戏测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_character_system():
    """测试角色系统"""
    print("=== 测试角色系统 ===")
    
    from character import Character, CharacterClass, CharacterManager
    from game_data import GameData
    
    # 创建角色
    game_data = GameData()
    char_manager = CharacterManager(game_data)
    
    # 测试角色创建
    liu_bei = Character("刘备", CharacterClass.GENERAL, 5)
    print(f"创建角色: {liu_bei.name}, 等级: {liu_bei.level}")
    print(f"HP: {liu_bei.current_hp}/{liu_bei.max_hp}")
    print(f"攻击力: {liu_bei.attack}, 防御力: {liu_bei.defense}")
    
    # 测试升级
    print("\n测试升级系统:")
    old_level = liu_bei.level
    liu_bei.gain_exp(150)
    if liu_bei.level > old_level:
        print(f"升级成功! {old_level} -> {liu_bei.level}")
    
    # 测试队伍管理
    print(f"\n当前队伍人数: {len(char_manager.party)}")
    for char in char_manager.party:
        print(f"- {char.name} (Lv.{char.level})")
    
    print("角色系统测试完成!\n")

def test_battle_system():
    """测试战斗系统"""
    print("=== 测试战斗系统 ===")
    
    from battle import BattleSystem, Enemy
    from character import CharacterManager
    from game_data import GameData
    
    # 创建战斗系统
    game_data = GameData()
    char_manager = CharacterManager(game_data)
    battle_system = BattleSystem(char_manager)
    
    # 创建敌人
    enemies = [
        Enemy("山贼", 3, "soldier"),
        Enemy("强盗", 4, "soldier")
    ]
    
    # 开始战斗
    battle_data = {"enemies": enemies}
    battle_system.start_battle(battle_data)
    
    print(f"战斗开始! 敌人数量: {len(enemies)}")
    for enemy in enemies:
        print(f"- {enemy.name} (Lv.{enemy.level}) HP: {enemy.current_hp}")
    
    print("战斗系统测试完成!\n")

def test_map_system():
    """测试地图系统"""
    print("=== 测试地图系统 ===")
    
    from map_system import WorldMap, TerrainType
    from game_data import GameData
    
    # 创建地图
    game_data = GameData()
    world_map = WorldMap(game_data)
    
    print(f"地图大小: {world_map.width} x {world_map.height}")
    print(f"玩家位置: ({world_map.player_x}, {world_map.player_y})")
    
    # 测试移动
    old_x, old_y = world_map.player_x, world_map.player_y
    success = world_map.move_player(1, 0)
    if success:
        print(f"移动成功: ({old_x}, {old_y}) -> ({world_map.player_x}, {world_map.player_y})")
    
    # 测试当前地形
    current_tile = world_map.get_current_tile()
    if current_tile:
        print(f"当前地形: {current_tile.terrain.value}")
        if current_tile.name:
            print(f"地点名称: {current_tile.name}")
    
    print("地图系统测试完成!\n")

def test_game_data():
    """测试游戏数据"""
    print("=== 测试游戏数据 ===")
    
    from game_data import GameData
    
    game_data = GameData()
    
    # 测试武器数据
    weapons = game_data.get_all_weapons()
    print(f"武器数量: {len(weapons)}")
    for weapon in weapons[:3]:  # 显示前3个
        print(f"- {weapon.name}: 攻击+{weapon.attack_bonus}, 价格{weapon.price}")
    
    # 测试城市数据
    cities = game_data.get_all_cities()
    print(f"\n城市数量: {len(cities)}")
    for city in cities:
        print(f"- {city.name} ({city.city_type}): 人口{city.population}")
    
    print("游戏数据测试完成!\n")

def test_game_state():
    """测试游戏状态"""
    print("=== 测试游戏状态 ===")
    
    from game_state import GameState
    
    game_state = GameState()
    
    # 测试基础功能
    print(f"初始金币: {game_state.get_gold()}")
    game_state.add_gold(500)
    print(f"增加500金币后: {game_state.get_gold()}")
    
    # 测试物品系统
    game_state.add_item("药草", 5)
    game_state.add_item("铁剑", 1)
    inventory = game_state.get_inventory()
    print(f"背包物品: {inventory}")
    
    # 测试位置更新
    game_state.update_player_position(15, 8)
    position = game_state.get_player_position()
    print(f"玩家位置: {position}")
    
    print("游戏状态测试完成!\n")

def test_resources():
    """测试资源管理"""
    print("=== 测试资源管理 ===")
    
    import pygame
    pygame.init()
    
    from resources import ResourceManager
    
    resource_manager = ResourceManager()
    
    # 测试图像资源
    player_image = resource_manager.get_image("player")
    if player_image:
        print(f"玩家图像: {player_image.get_size()}")
    
    grass_image = resource_manager.get_image("grass")
    if grass_image:
        print(f"草地图像: {grass_image.get_size()}")
    
    # 测试字体资源
    font = resource_manager.get_font("chinese")
    if font:
        print(f"中文字体加载成功")
    
    # 测试文本创建
    text_surface = resource_manager.create_text_surface("测试文本")
    if text_surface:
        print(f"文本表面: {text_surface.get_size()}")
    
    pygame.quit()
    print("资源管理测试完成!\n")

def main():
    """主测试函数"""
    print("开始测试吞食天地2游戏系统...\n")
    
    try:
        test_game_data()
        test_character_system()
        test_battle_system()
        test_map_system()
        test_game_state()
        test_resources()
        
        print("=== 所有测试完成 ===")
        print("游戏系统运行正常!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
