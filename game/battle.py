#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战斗系统 - 回合制战斗逻辑
"""

from typing import List, Dict, Optional, Tuple
from enum import Enum
import random
import pygame

from character import Character, CharacterManager

class BattleAction(Enum):
    ATTACK = "攻击"
    SKILL = "技能"
    ITEM = "道具"
    DEFEND = "防御"
    ESCAPE = "逃跑"

class BattleState(Enum):
    PLAYER_TURN = "player_turn"
    ENEMY_TURN = "enemy_turn"
    VICTORY = "victory"
    DEFEAT = "defeat"
    ESCAPED = "escaped"

class Enemy:
    """敌人类"""
    
    def __init__(self, name: str, level: int, enemy_type: str = "soldier"):
        self.name = name
        self.level = level
        self.enemy_type = enemy_type
        
        # 基础属性
        self.max_hp = 50 + level * 15
        self.current_hp = self.max_hp
        self.attack = 15 + level * 3
        self.defense = 10 + level * 2
        self.agility = 8 + level * 2
        
        # 经验值和金钱奖励
        self.exp_reward = level * 20 + random.randint(5, 15)
        self.gold_reward = level * 10 + random.randint(3, 12)
        
        # 根据敌人类型调整属性
        self._apply_type_bonuses()
    
    def _apply_type_bonuses(self):
        """根据敌人类型应用属性加成"""
        if self.enemy_type == "boss":
            self.max_hp *= 3
            self.current_hp = self.max_hp
            self.attack = int(self.attack * 1.5)
            self.defense = int(self.defense * 1.3)
            self.exp_reward *= 5
            self.gold_reward *= 3
            
        elif self.enemy_type == "elite":
            self.max_hp = int(self.max_hp * 1.5)
            self.current_hp = self.max_hp
            self.attack = int(self.attack * 1.2)
            self.defense = int(self.defense * 1.1)
            self.exp_reward *= 2
            self.gold_reward *= 2
    
    def take_damage(self, damage: int) -> bool:
        """受到伤害，返回是否死亡"""
        actual_damage = max(1, damage - self.defense // 2)
        self.current_hp = max(0, self.current_hp - actual_damage)
        return self.current_hp <= 0
    
    def get_attack_power(self) -> int:
        """获取攻击力"""
        variation = random.randint(-3, 3)
        return max(1, self.attack + variation)
    
    def is_alive(self) -> bool:
        """是否存活"""
        return self.current_hp > 0

class BattleSystem:
    """战斗系统"""
    
    def __init__(self, character_manager: CharacterManager):
        self.character_manager = character_manager
        self.state = BattleState.PLAYER_TURN
        self.enemies = []
        self.current_character_index = 0
        self.selected_action = BattleAction.ATTACK
        self.selected_target = 0
        self.battle_log = []
        self.turn_count = 0
        self.battle_finished = False
        self.battle_result = None
        
        # UI相关
        self.action_menu_index = 0
        self.target_menu_index = 0
        self.show_target_menu = False
    
    def start_battle(self, battle_data: Dict):
        """开始战斗"""
        self.enemies = battle_data.get('enemies', [])
        self.state = BattleState.PLAYER_TURN
        self.current_character_index = 0
        self.battle_log = []
        self.turn_count = 0
        self.battle_finished = False
        self.battle_result = None
        self.show_target_menu = False
        
        # 重置选择状态
        self.action_menu_index = 0
        self.target_menu_index = 0
        
        self.add_log(f"战斗开始! 遭遇 {len(self.enemies)} 个敌人")
    
    def update(self):
        """更新战斗逻辑"""
        if self.battle_finished:
            return
            
        if self.state == BattleState.PLAYER_TURN:
            self._update_player_turn()
        elif self.state == BattleState.ENEMY_TURN:
            self._update_enemy_turn()
        
        # 检查战斗结束条件
        self._check_battle_end()
    
    def _update_player_turn(self):
        """更新玩家回合"""
        # 玩家回合由输入处理，这里只检查是否需要切换到下一个角色
        pass
    
    def _update_enemy_turn(self):
        """更新敌人回合"""
        alive_enemies = [e for e in self.enemies if e.is_alive()]
        alive_party = self.character_manager.get_alive_party_members()
        
        if not alive_enemies or not alive_party:
            return
        
        # 每个存活的敌人行动
        for enemy in alive_enemies:
            if not alive_party:  # 如果玩家队伍全灭，停止敌人行动
                break
                
            # 随机选择目标
            target = random.choice(alive_party)
            damage = enemy.get_attack_power()
            
            is_dead = target.take_damage(damage)
            self.add_log(f"{enemy.name} 攻击 {target.name}, 造成 {damage} 点伤害")
            
            if is_dead:
                self.add_log(f"{target.name} 倒下了!")
                alive_party = self.character_manager.get_alive_party_members()
        
        # 敌人回合结束，切换到玩家回合
        self.state = BattleState.PLAYER_TURN
        self.current_character_index = 0
        self.turn_count += 1
    
    def _check_battle_end(self):
        """检查战斗结束条件"""
        alive_enemies = [e for e in self.enemies if e.is_alive()]
        alive_party = self.character_manager.get_alive_party_members()
        
        if not alive_party:
            self.state = BattleState.DEFEAT
            self.battle_finished = True
            self.battle_result = {"result": "defeat"}
            self.add_log("战斗失败...")
            
        elif not alive_enemies:
            self.state = BattleState.VICTORY
            self.battle_finished = True
            
            # 计算奖励
            total_exp = sum(e.exp_reward for e in self.enemies)
            total_gold = sum(e.gold_reward for e in self.enemies)
            
            # 分配经验值
            for character in self.character_manager.party:
                if character.is_alive():
                    leveled_up = character.gain_exp(total_exp // len(self.character_manager.party))
                    if leveled_up:
                        self.add_log(f"{character.name} 升级了!")
            
            self.battle_result = {
                "result": "victory",
                "exp": total_exp,
                "gold": total_gold
            }
            self.add_log(f"战斗胜利! 获得 {total_exp} 经验值, {total_gold} 金币")
    
    def handle_confirm(self):
        """处理确认操作"""
        if self.battle_finished:
            return
            
        if self.state != BattleState.PLAYER_TURN:
            return
        
        current_character = self._get_current_character()
        if not current_character or not current_character.is_alive():
            self._next_character()
            return
        
        if not self.show_target_menu:
            # 选择行动
            self.selected_action = list(BattleAction)[self.action_menu_index]
            
            if self.selected_action in [BattleAction.ATTACK, BattleAction.SKILL]:
                # 需要选择目标
                self.show_target_menu = True
                self.target_menu_index = 0
            else:
                # 直接执行行动
                self._execute_action()
        else:
            # 选择目标并执行行动
            self._execute_action()
    
    def _execute_action(self):
        """执行行动"""
        current_character = self._get_current_character()
        if not current_character:
            return
        
        if self.selected_action == BattleAction.ATTACK:
            self._execute_attack(current_character)
        elif self.selected_action == BattleAction.DEFEND:
            self._execute_defend(current_character)
        elif self.selected_action == BattleAction.ESCAPE:
            self._execute_escape()
        
        # 重置UI状态
        self.show_target_menu = False
        self.action_menu_index = 0
        
        # 切换到下一个角色
        self._next_character()
    
    def _execute_attack(self, character: Character):
        """执行攻击"""
        alive_enemies = [e for e in self.enemies if e.is_alive()]
        if not alive_enemies:
            return
        
        if self.target_menu_index < len(alive_enemies):
            target = alive_enemies[self.target_menu_index]
            damage = character.get_attack_power()
            
            is_dead = target.take_damage(damage)
            self.add_log(f"{character.name} 攻击 {target.name}, 造成 {damage} 点伤害")
            
            if is_dead:
                self.add_log(f"{target.name} 被击败了!")
    
    def _execute_defend(self, character: Character):
        """执行防御"""
        # 防御可以恢复少量HP
        heal_amount = character.max_hp // 10
        character.heal(heal_amount)
        self.add_log(f"{character.name} 进入防御状态, 恢复 {heal_amount} HP")
    
    def _execute_escape(self):
        """执行逃跑"""
        escape_chance = 0.7  # 70%逃跑成功率
        if random.random() < escape_chance:
            self.state = BattleState.ESCAPED
            self.battle_finished = True
            self.battle_result = {"result": "escaped"}
            self.add_log("成功逃脱了!")
        else:
            self.add_log("逃跑失败!")
    
    def _get_current_character(self) -> Optional[Character]:
        """获取当前行动的角色"""
        alive_party = self.character_manager.get_alive_party_members()
        if self.current_character_index < len(alive_party):
            return alive_party[self.current_character_index]
        return None
    
    def _next_character(self):
        """切换到下一个角色"""
        alive_party = self.character_manager.get_alive_party_members()
        self.current_character_index += 1
        
        if self.current_character_index >= len(alive_party):
            # 所有角色行动完毕，切换到敌人回合
            self.state = BattleState.ENEMY_TURN
            self.current_character_index = 0
    
    def move_action_selection(self, direction: int):
        """移动行动选择"""
        if self.show_target_menu:
            alive_enemies = [e for e in self.enemies if e.is_alive()]
            self.target_menu_index = (self.target_menu_index + direction) % len(alive_enemies)
        else:
            actions_count = len(BattleAction)
            self.action_menu_index = (self.action_menu_index + direction) % actions_count
    
    def add_log(self, message: str):
        """添加战斗日志"""
        self.battle_log.append(message)
        if len(self.battle_log) > 10:  # 限制日志长度
            self.battle_log.pop(0)
        print(f"[战斗] {message}")
    
    def is_battle_finished(self) -> bool:
        """战斗是否结束"""
        return self.battle_finished
    
    def get_battle_result(self) -> Optional[Dict]:
        """获取战斗结果"""
        return self.battle_result
    
    def render(self, screen: pygame.Surface):
        """渲染战斗画面"""
        # 这里只是基础的文本渲染，UI系统会处理具体的渲染
        pass
