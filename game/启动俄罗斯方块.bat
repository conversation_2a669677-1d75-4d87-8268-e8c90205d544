@echo off
chcp 65001 >nul
title 俄罗斯方块游戏

echo ========================================
echo           俄罗斯方块游戏
echo ========================================
echo.
echo 正在检查Python环境...

python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.6+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.
echo 正在检查Pygame库...

python -c "import pygame" >nul 2>&1
if errorlevel 1 (
    echo 警告：未找到Pygame库，正在尝试安装...
    pip install pygame
    if errorlevel 1 (
        echo 错误：Pygame安装失败
        pause
        exit /b 1
    )
)

echo Pygame库检查通过
echo.
echo 游戏控制说明：
echo A/D 或 ←/→ - 左右移动
echo S 或 ↓ - 软降
echo W 或 ↑ - 旋转
echo SPACE - 硬降
echo P - 暂停/继续
echo R - 重新开始
echo.
echo 正在启动游戏...
echo.

python 俄罗斯方块.py

if errorlevel 1 (
    echo.
    echo 游戏运行出错，请检查错误信息
    pause
)
