#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角色系统 - 武将属性、技能、升级系统
"""

from typing import Dict, List, Optional
from enum import Enum
import random

class CharacterClass(Enum):
    """武将职业"""
    WARRIOR = "武将"      # 武力型
    STRATEGIST = "军师"   # 智力型
    GENERAL = "大将"      # 平衡型
    ARCHER = "弓兵"       # 远程型

class Character:
    """角色类"""
    
    def __init__(self, name: str, char_class: CharacterClass, level: int = 1):
        self.name = name
        self.char_class = char_class
        self.level = level
        
        # 基础属性
        self.max_hp = 100
        self.current_hp = self.max_hp
        self.max_mp = 50
        self.current_mp = self.max_mp
        
        # 战斗属性
        self.attack = 20
        self.defense = 15
        self.intelligence = 10
        self.agility = 12
        self.luck = 8
        
        # 经验值
        self.exp = 0
        self.exp_to_next_level = 100
        
        # 装备
        self.weapon = None
        self.armor = None
        self.accessory = None
        
        # 技能
        self.skills = []
        
        # 根据职业调整属性
        self._apply_class_bonuses()
        
        # 根据等级调整属性
        self._apply_level_bonuses()
    
    def _apply_class_bonuses(self):
        """根据职业应用属性加成"""
        if self.char_class == CharacterClass.WARRIOR:
            self.attack += 10
            self.defense += 5
            self.max_hp += 20
            
        elif self.char_class == CharacterClass.STRATEGIST:
            self.intelligence += 15
            self.max_mp += 30
            self.attack -= 5
            
        elif self.char_class == CharacterClass.GENERAL:
            self.attack += 5
            self.defense += 5
            self.intelligence += 5
            self.max_hp += 10
            self.max_mp += 10
            
        elif self.char_class == CharacterClass.ARCHER:
            self.agility += 10
            self.attack += 8
            self.defense -= 3
        
        # 更新当前HP/MP
        self.current_hp = self.max_hp
        self.current_mp = self.max_mp
    
    def _apply_level_bonuses(self):
        """根据等级应用属性加成"""
        if self.level > 1:
            level_bonus = self.level - 1
            self.max_hp += level_bonus * 15
            self.max_mp += level_bonus * 8
            self.attack += level_bonus * 3
            self.defense += level_bonus * 2
            self.intelligence += level_bonus * 2
            self.agility += level_bonus * 2
            self.luck += level_bonus * 1
            
            # 更新当前HP/MP
            self.current_hp = self.max_hp
            self.current_mp = self.max_mp
    
    def gain_exp(self, amount: int) -> bool:
        """获得经验值，返回是否升级"""
        self.exp += amount
        
        if self.exp >= self.exp_to_next_level:
            return self.level_up()
        
        return False
    
    def level_up(self) -> bool:
        """升级"""
        if self.level >= 99:  # 最大等级限制
            return False
            
        self.level += 1
        self.exp -= self.exp_to_next_level
        self.exp_to_next_level = int(self.exp_to_next_level * 1.2)
        
        # 升级属性提升
        hp_gain = random.randint(10, 20)
        mp_gain = random.randint(5, 12)
        attack_gain = random.randint(2, 5)
        defense_gain = random.randint(1, 4)
        int_gain = random.randint(1, 4)
        agi_gain = random.randint(1, 4)
        luck_gain = random.randint(0, 2)
        
        self.max_hp += hp_gain
        self.max_mp += mp_gain
        self.attack += attack_gain
        self.defense += defense_gain
        self.intelligence += int_gain
        self.agility += agi_gain
        self.luck += luck_gain
        
        # 恢复HP/MP
        self.current_hp = self.max_hp
        self.current_mp = self.max_mp
        
        print(f"{self.name} 升级到 {self.level} 级!")
        print(f"HP +{hp_gain}, MP +{mp_gain}, 攻击 +{attack_gain}, 防御 +{defense_gain}")
        
        return True
    
    def take_damage(self, damage: int) -> bool:
        """受到伤害，返回是否死亡"""
        actual_damage = max(1, damage - self.defense // 2)
        self.current_hp = max(0, self.current_hp - actual_damage)
        
        return self.current_hp <= 0
    
    def heal(self, amount: int):
        """治疗"""
        self.current_hp = min(self.max_hp, self.current_hp + amount)
    
    def use_mp(self, amount: int) -> bool:
        """使用MP，返回是否成功"""
        if self.current_mp >= amount:
            self.current_mp -= amount
            return True
        return False
    
    def restore_mp(self, amount: int):
        """恢复MP"""
        self.current_mp = min(self.max_mp, self.current_mp + amount)
    
    def is_alive(self) -> bool:
        """是否存活"""
        return self.current_hp > 0
    
    def get_attack_power(self) -> int:
        """获取攻击力"""
        base_attack = self.attack
        
        # 武器加成
        if self.weapon:
            base_attack += self.weapon.attack_bonus
            
        # 随机波动
        variation = random.randint(-5, 5)
        
        return max(1, base_attack + variation)
    
    def get_defense_power(self) -> int:
        """获取防御力"""
        base_defense = self.defense
        
        # 防具加成
        if self.armor:
            base_defense += self.armor.defense_bonus
            
        return base_defense
    
    def get_status_info(self) -> Dict:
        """获取状态信息"""
        return {
            'name': self.name,
            'class': self.char_class.value,
            'level': self.level,
            'hp': f"{self.current_hp}/{self.max_hp}",
            'mp': f"{self.current_mp}/{self.max_mp}",
            'attack': self.attack,
            'defense': self.defense,
            'intelligence': self.intelligence,
            'agility': self.agility,
            'luck': self.luck,
            'exp': self.exp,
            'exp_to_next': self.exp_to_next_level
        }

class CharacterManager:
    """角色管理器"""
    
    def __init__(self, game_data):
        self.game_data = game_data
        self.party = []  # 当前队伍
        self.all_characters = {}  # 所有可用角色
        
        # 初始化预设角色
        self._init_preset_characters()
    
    def _init_preset_characters(self):
        """初始化预设角色"""
        # 三国经典武将
        preset_chars = [
            ("刘备", CharacterClass.GENERAL, 5),
            ("关羽", CharacterClass.WARRIOR, 6),
            ("张飞", CharacterClass.WARRIOR, 5),
            ("诸葛亮", CharacterClass.STRATEGIST, 8),
            ("赵云", CharacterClass.GENERAL, 7),
            ("曹操", CharacterClass.GENERAL, 10),
            ("典韦", CharacterClass.WARRIOR, 8),
            ("许褚", CharacterClass.WARRIOR, 7),
            ("司马懿", CharacterClass.STRATEGIST, 9),
            ("孙权", CharacterClass.GENERAL, 6),
            ("周瑜", CharacterClass.STRATEGIST, 7),
            ("吕布", CharacterClass.WARRIOR, 12),
        ]
        
        for name, char_class, level in preset_chars:
            character = Character(name, char_class, level)
            self.all_characters[name] = character
        
        # 默认队伍：刘备、关羽、张飞
        self.party = [
            self.all_characters["刘备"],
            self.all_characters["关羽"],
            self.all_characters["张飞"]
        ]
    
    def add_to_party(self, character_name: str) -> bool:
        """添加角色到队伍"""
        if len(self.party) >= 6:  # 最大队伍人数
            return False
            
        if character_name in self.all_characters:
            character = self.all_characters[character_name]
            if character not in self.party:
                self.party.append(character)
                return True
        
        return False
    
    def remove_from_party(self, character_name: str) -> bool:
        """从队伍中移除角色"""
        for i, character in enumerate(self.party):
            if character.name == character_name:
                if len(self.party) > 1:  # 至少保留一个角色
                    self.party.pop(i)
                    return True
                break
        
        return False
    
    def get_party_leader(self) -> Optional[Character]:
        """获取队伍队长"""
        return self.party[0] if self.party else None
    
    def get_alive_party_members(self) -> List[Character]:
        """获取存活的队伍成员"""
        return [char for char in self.party if char.is_alive()]
    
    def is_party_defeated(self) -> bool:
        """队伍是否全灭"""
        return len(self.get_alive_party_members()) == 0
    
    def heal_party(self, amount: int):
        """治疗队伍"""
        for character in self.party:
            character.heal(amount)
    
    def restore_party_mp(self, amount: int):
        """恢复队伍MP"""
        for character in self.party:
            character.restore_mp(amount)
    
    def get_party_info(self) -> List[Dict]:
        """获取队伍信息"""
        return [char.get_status_info() for char in self.party]
