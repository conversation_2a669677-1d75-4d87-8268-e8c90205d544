import pygame
import random
import sys
from typing import List, Tu<PERSON>, Optional
from enum import Enum

# 初始化Pygame
pygame.init()

# 游戏常量
GRID_WIDTH = 25
GRID_HEIGHT = 20
CELL_SIZE = 25
WINDOW_WIDTH = GRID_WIDTH * CELL_SIZE + 200  # 额外空间用于显示信息
WINDOW_HEIGHT = GRID_HEIGHT * CELL_SIZE + 100

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 128, 0)
LIGHT_GREEN = (144, 238, 144)

# 游戏设置
FPS = 10
INITIAL_SNAKE_LENGTH = 3

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class GameState(Enum):
    PLAYING = 1
    PAUSED = 2
    GAME_OVER = 3

class SnakeGame:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("贪吃蛇游戏 - WASD/方向键控制, 空格暂停, R重新开始")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)

        # 游戏状态
        self.reset_game()

    def reset_game(self):
        """重置游戏状态"""
        # 蛇的初始位置（屏幕中央）
        start_x = GRID_WIDTH // 2
        start_y = GRID_HEIGHT // 2
        self.snake = [(start_x, start_y)]
        for i in range(1, INITIAL_SNAKE_LENGTH):
            self.snake.append((start_x - i, start_y))

        self.direction = Direction.RIGHT
        self.next_direction = Direction.RIGHT
        self.food = self.generate_food()
        self.score = 0
        self.game_state = GameState.PLAYING

    def generate_food(self) -> Tuple[int, int]:
        """生成食物位置"""
        while True:
            food_x = random.randint(0, GRID_WIDTH - 1)
            food_y = random.randint(0, GRID_HEIGHT - 1)
            if (food_x, food_y) not in self.snake:
                return (food_x, food_y)

    def handle_input(self):
        """处理用户输入"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    if self.game_state == GameState.PLAYING:
                        self.game_state = GameState.PAUSED
                    elif self.game_state == GameState.PAUSED:
                        self.game_state = GameState.PLAYING
                elif event.key == pygame.K_r:
                    self.reset_game()
                elif self.game_state == GameState.PLAYING:
                    # WASD控制
                    if event.key == pygame.K_w or event.key == pygame.K_UP:
                        if self.direction != Direction.DOWN:
                            self.next_direction = Direction.UP
                    elif event.key == pygame.K_s or event.key == pygame.K_DOWN:
                        if self.direction != Direction.UP:
                            self.next_direction = Direction.DOWN
                    elif event.key == pygame.K_a or event.key == pygame.K_LEFT:
                        if self.direction != Direction.RIGHT:
                            self.next_direction = Direction.LEFT
                    elif event.key == pygame.K_d or event.key == pygame.K_RIGHT:
                        if self.direction != Direction.LEFT:
                            self.next_direction = Direction.RIGHT
        return True

    def update_game(self):
        """更新游戏状态"""
        if self.game_state != GameState.PLAYING:
            return

        # 更新方向
        self.direction = self.next_direction

        # 计算新的头部位置
        head_x, head_y = self.snake[0]
        dx, dy = self.direction.value
        new_head = (head_x + dx, head_y + dy)

        # 检查边界碰撞
        if (new_head[0] < 0 or new_head[0] >= GRID_WIDTH or
            new_head[1] < 0 or new_head[1] >= GRID_HEIGHT):
            self.game_state = GameState.GAME_OVER
            return

        # 检查自身碰撞
        if new_head in self.snake:
            self.game_state = GameState.GAME_OVER
            return

        # 添加新头部
        self.snake.insert(0, new_head)

        # 检查是否吃到食物
        if new_head == self.food:
            self.score += 10
            self.food = self.generate_food()
        else:
            # 移除尾部
            self.snake.pop()

    def draw_grid(self):
        """绘制网格"""
        for x in range(0, GRID_WIDTH * CELL_SIZE + 1, CELL_SIZE):
            pygame.draw.line(self.screen, GRAY, (x, 0), (x, GRID_HEIGHT * CELL_SIZE))
        for y in range(0, GRID_HEIGHT * CELL_SIZE + 1, CELL_SIZE):
            pygame.draw.line(self.screen, GRAY, (0, y), (GRID_WIDTH * CELL_SIZE, y))

    def draw_snake(self):
        """绘制蛇"""
        for i, (x, y) in enumerate(self.snake):
            rect = pygame.Rect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE)
            if i == 0:  # 头部
                pygame.draw.rect(self.screen, DARK_GREEN, rect)
                pygame.draw.rect(self.screen, GREEN, rect, 2)
            else:  # 身体
                pygame.draw.rect(self.screen, LIGHT_GREEN, rect)
                pygame.draw.rect(self.screen, GREEN, rect, 1)

    def draw_food(self):
        """绘制食物"""
        x, y = self.food
        rect = pygame.Rect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE)
        pygame.draw.rect(self.screen, RED, rect)
        pygame.draw.rect(self.screen, YELLOW, rect, 2)

    def draw_ui(self):
        """绘制用户界面"""
        # 分数
        score_text = self.font.render(f"分数: {self.score}", True, WHITE)
        self.screen.blit(score_text, (GRID_WIDTH * CELL_SIZE + 10, 20))

        # 长度
        length_text = self.font.render(f"长度: {len(self.snake)}", True, WHITE)
        self.screen.blit(length_text, (GRID_WIDTH * CELL_SIZE + 10, 60))

        # 控制说明
        controls = [
            "控制:",
            "WASD / 方向键",
            "空格: 暂停",
            "R: 重新开始"
        ]
        for i, text in enumerate(controls):
            control_text = self.small_font.render(text, True, WHITE)
            self.screen.blit(control_text, (GRID_WIDTH * CELL_SIZE + 10, 120 + i * 25))

        # 游戏状态
        if self.game_state == GameState.PAUSED:
            pause_text = self.font.render("游戏暂停", True, YELLOW)
            text_rect = pause_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2))
            self.screen.blit(pause_text, text_rect)
        elif self.game_state == GameState.GAME_OVER:
            game_over_text = self.font.render("游戏结束!", True, RED)
            restart_text = self.small_font.render("按R重新开始", True, WHITE)

            game_over_rect = game_over_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 20))
            restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 20))

            self.screen.blit(game_over_text, game_over_rect)
            self.screen.blit(restart_text, restart_rect)

    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(BLACK)

        # 绘制游戏区域边框
        game_area = pygame.Rect(0, 0, GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE)
        pygame.draw.rect(self.screen, WHITE, game_area, 2)

        # 绘制网格
        self.draw_grid()

        # 绘制游戏元素
        self.draw_snake()
        self.draw_food()

        # 绘制UI
        self.draw_ui()

        pygame.display.flip()

    def run(self):
        """运行游戏主循环"""
        running = True
        while running:
            running = self.handle_input()
            self.update_game()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()

def main():
    """主函数"""
    try:
        game = SnakeGame()
        game.run()
    except Exception as e:
        print(f"游戏运行出错: {e}")
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    main()