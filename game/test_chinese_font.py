#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体显示测试
"""

import pygame
import sys
import os

def test_chinese_font():
    """测试中文字体显示"""
    pygame.init()
    
    # 创建窗口
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("中文字体测试")
    
    # 尝试加载中文字体
    chinese_fonts = [
        "SimHei",           # 黑体
        "Microsoft YaHei",  # 微软雅黑
        "SimSun",          # 宋体
        "KaiTi",           # 楷体
    ]
    
    loaded_fonts = []
    
    for font_name in chinese_fonts:
        try:
            font = pygame.font.SysFont(font_name, 24)
            # 测试字体是否支持中文
            test_surface = font.render("测试", True, (255, 255, 255))
            if test_surface.get_width() > 0:
                loaded_fonts.append((font_name, font))
                print(f"✓ 成功加载字体: {font_name}")
            else:
                print(f"✗ 字体不支持中文: {font_name}")
        except Exception as e:
            print(f"✗ 加载字体失败: {font_name} - {e}")
    
    if not loaded_fonts:
        print("警告: 没有找到可用的中文字体")
        return
    
    # 测试文本
    test_texts = [
        "吞食天地2",
        "三国RPG游戏",
        "刘备 关羽 张飞",
        "诸葛亮 赵云 曹操",
        "开始游戏",
        "读取存档",
        "游戏设置",
        "退出游戏"
    ]
    
    clock = pygame.time.Clock()
    running = True
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
        
        # 清空屏幕
        screen.fill((0, 0, 0))
        
        # 显示标题
        if loaded_fonts:
            title_font = loaded_fonts[0][1]
            title_surface = title_font.render("中文字体显示测试", True, (255, 255, 0))
            title_rect = title_surface.get_rect(center=(400, 50))
            screen.blit(title_surface, title_rect)
        
        # 显示测试文本
        y_offset = 100
        for i, text in enumerate(test_texts):
            if loaded_fonts:
                font_name, font = loaded_fonts[i % len(loaded_fonts)]
                
                # 显示字体名称
                font_name_surface = pygame.font.Font(None, 18).render(f"[{font_name}]", True, (128, 128, 128))
                screen.blit(font_name_surface, (50, y_offset))
                
                # 显示中文文本
                text_surface = font.render(text, True, (255, 255, 255))
                screen.blit(text_surface, (200, y_offset))
                
                y_offset += 40
        
        # 显示说明
        instruction_font = pygame.font.Font(None, 20)
        instruction_surface = instruction_font.render("Press ESC to exit", True, (128, 128, 128))
        instruction_rect = instruction_surface.get_rect(center=(400, 550))
        screen.blit(instruction_surface, instruction_rect)
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("中文字体测试完成")

if __name__ == "__main__":
    test_chinese_font()
