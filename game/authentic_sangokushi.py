#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于原版ROM数据的吞食天地2
"""

import pygame
import sys
import os
import json
from typing import Dict, List, Tuple
from enum import Enum

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class AuthenticTileType(Enum):
    """基于ROM数据的瓦片类型"""
    TILE_0 = 0    # 最常见的瓦片（可能是平原）
    TILE_1 = 1    # 第二常见（可能是山地）
    TILE_2 = 2    # 第三常见（可能是森林）
    TILE_3 = 3    # 道路或河流
    TILE_4 = 4    # 特殊地形
    TILE_5 = 5    # 城市或建筑
    TILE_6 = 6    # 水域
    TILE_7 = 7    # 其他地形

class AuthenticWorldMap:
    """基于ROM数据的真实世界地图"""
    
    def __init__(self):
        self.width = 32
        self.height = 32
        self.tile_size = 20
        
        # 玩家位置
        self.player_x = 16
        self.player_y = 16
        
        # 摄像机
        self.camera_x = 0
        self.camera_y = 0
        self.view_width = 25
        self.view_height = 20
        
        # 加载ROM提取的地图数据
        self.map_data = self._load_rom_map_data()
        
        # 更新摄像机
        self._update_camera()
    
    def _load_rom_map_data(self) -> Dict[Tuple[int, int], int]:
        """加载ROM提取的地图数据"""
        map_data = {}
        
        # 从ROM分析结果加载真实地图数据
        try:
            with open('rom_analysis/extracted_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 使用ROM中的真实地图数据
            if data.get('world_map_info'):
                print(f"加载ROM地图数据: {data['world_map_info']['width']}x{data['world_map_info']['height']}")
                
                # 这里我们需要重新读取原始地图数据
                # 因为JSON中没有保存完整的2D数组
                self._load_raw_map_data(map_data)
            else:
                # 如果没有数据，创建默认地图
                self._create_default_map(map_data)
                
        except Exception as e:
            print(f"加载ROM地图数据失败: {e}")
            self._create_default_map(map_data)
        
        return map_data
    
    def _load_raw_map_data(self, map_data: Dict):
        """从ROM文件直接加载地图数据"""
        try:
            # 直接从ROM文件读取地图数据
            with open('game/sangokushi2.nes', 'rb') as f:
                # 跳过文件头
                header = f.read(16)
                if header[6] & 4:
                    f.read(512)
                
                # 读取PRG数据
                prg_size = header[4] * 16384
                prg_data = f.read(prg_size)
                
                # 从偏移0xC100读取32x32地图数据
                map_offset = 0xC100
                if map_offset < len(prg_data):
                    for y in range(32):
                        for x in range(32):
                            data_index = map_offset + y * 32 + x
                            if data_index < len(prg_data):
                                tile_id = prg_data[data_index]
                                map_data[(x, y)] = tile_id
                    
                    print("成功加载ROM原始地图数据")
                    return
        except Exception as e:
            print(f"直接读取ROM地图失败: {e}")
        
        # 如果失败，使用默认地图
        self._create_default_map(map_data)
    
    def _create_default_map(self, map_data: Dict):
        """创建默认地图"""
        for y in range(self.height):
            for x in range(self.width):
                # 创建一个简单的测试地图
                if x == 0 or x == self.width-1 or y == 0 or y == self.height-1:
                    map_data[(x, y)] = 6  # 边界用水域
                elif (x + y) % 8 == 0:
                    map_data[(x, y)] = 5  # 城市
                elif (x * y) % 7 == 0:
                    map_data[(x, y)] = 2  # 森林
                elif (x + y) % 5 == 0:
                    map_data[(x, y)] = 1  # 山地
                else:
                    map_data[(x, y)] = 0  # 平原
    
    def get_tile_color(self, tile_id: int) -> Tuple[int, int, int]:
        """根据瓦片ID获取颜色"""
        # 基于FC游戏的经典配色
        colors = {
            0: (144, 238, 144),   # 浅绿色 - 平原
            1: (139, 69, 19),     # 棕色 - 山地
            2: (34, 139, 34),     # 深绿色 - 森林
            3: (160, 82, 45),     # 棕色 - 道路
            4: (255, 165, 0),     # 橙色 - 特殊地形
            5: (255, 215, 0),     # 金色 - 城市
            6: (0, 191, 255),     # 蓝色 - 水域
            7: (128, 128, 128),   # 灰色 - 其他
        }
        
        # 对于更多的瓦片ID，使用渐变色
        if tile_id in colors:
            return colors[tile_id]
        else:
            # 为其他瓦片ID生成颜色
            r = (tile_id * 37) % 256
            g = (tile_id * 73) % 256
            b = (tile_id * 109) % 256
            return (r, g, b)
    
    def get_tile_char(self, tile_id: int) -> str:
        """根据瓦片ID获取字符"""
        chars = {
            0: '.',   # 平原
            1: '^',   # 山地
            2: 'T',   # 森林
            3: '=',   # 道路
            4: '*',   # 特殊
            5: 'C',   # 城市
            6: '~',   # 水域
            7: '#',   # 其他
        }
        
        if tile_id in chars:
            return chars[tile_id]
        else:
            return str(tile_id % 10)
    
    def move_player(self, dx: int, dy: int) -> bool:
        """移动玩家"""
        new_x = self.player_x + dx
        new_y = self.player_y + dy
        
        # 检查边界
        if not (0 <= new_x < self.width and 0 <= new_y < self.height):
            return False
        
        # 检查地形是否可通行
        tile_id = self.map_data.get((new_x, new_y), 0)
        if tile_id == 6:  # 水域不可通行
            return False
        
        # 移动玩家
        self.player_x = new_x
        self.player_y = new_y
        
        # 更新摄像机
        self._update_camera()
        
        return True
    
    def _update_camera(self):
        """更新摄像机位置"""
        self.camera_x = max(0, min(self.width - self.view_width, 
                                  self.player_x - self.view_width // 2))
        self.camera_y = max(0, min(self.height - self.view_height, 
                                  self.player_y - self.view_height // 2))
    
    def get_visible_tiles(self) -> List[Tuple[int, int, int]]:
        """获取可见的地图格子"""
        visible_tiles = []
        
        for y in range(self.camera_y, min(self.camera_y + self.view_height, self.height)):
            for x in range(self.camera_x, min(self.camera_x + self.view_width, self.width)):
                tile_id = self.map_data.get((x, y), 0)
                visible_tiles.append((x, y, tile_id))
        
        return visible_tiles

class AuthenticSangokushiGame:
    """基于ROM数据的吞食天地2游戏"""
    
    def __init__(self):
        # 设置中文编码
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 初始化pygame
        pygame.init()
        
        # 游戏配置 - 使用FC风格的分辨率比例
        self.SCREEN_WIDTH = 640
        self.SCREEN_HEIGHT = 480
        self.FPS = 60
        self.TITLE = "吞食天地2 - 基于原版ROM数据"
        
        # 创建游戏窗口
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption(self.TITLE)
        self.clock = pygame.time.Clock()
        
        # 游戏状态
        self.running = True
        
        # 初始化字体
        self._init_fonts()
        
        # 初始化游戏系统
        self.world_map = AuthenticWorldMap()
        
        # 颜色定义
        self.colors = {
            'black': (0, 0, 0),
            'white': (255, 255, 255),
            'red': (255, 0, 0),
            'green': (0, 255, 0),
            'blue': (0, 0, 255),
            'yellow': (255, 255, 0),
            'gray': (128, 128, 128),
        }
        
        print("基于ROM数据的吞食天地2初始化完成")
    
    def _init_fonts(self):
        """初始化字体"""
        try:
            # 使用宋体字体
            simsun_path = "C:/Windows/Fonts/simsun.ttc"
            if os.path.exists(simsun_path):
                self.font_large = pygame.font.Font(simsun_path, 32)
                self.font_medium = pygame.font.Font(simsun_path, 20)
                self.font_small = pygame.font.Font(simsun_path, 16)
                print("成功加载宋体字体")
            else:
                self.font_large = pygame.font.Font(None, 32)
                self.font_medium = pygame.font.Font(None, 20)
                self.font_small = pygame.font.Font(None, 16)
        except Exception as e:
            print(f"字体加载失败: {e}")
            self.font_large = pygame.font.Font(None, 32)
            self.font_medium = pygame.font.Font(None, 20)
            self.font_small = pygame.font.Font(None, 16)
    
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
                
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key in [pygame.K_w, pygame.K_UP]:
                    self.world_map.move_player(0, -1)
                elif event.key in [pygame.K_s, pygame.K_DOWN]:
                    self.world_map.move_player(0, 1)
                elif event.key in [pygame.K_a, pygame.K_LEFT]:
                    self.world_map.move_player(-1, 0)
                elif event.key in [pygame.K_d, pygame.K_RIGHT]:
                    self.world_map.move_player(1, 0)
    
    def render(self):
        """渲染游戏画面"""
        # 清空屏幕
        self.screen.fill(self.colors['black'])
        
        # 渲染地图
        self._render_map()
        
        # 渲染UI
        self._render_ui()
        
        # 更新显示
        pygame.display.flip()
    
    def _render_map(self):
        """渲染地图"""
        tile_size = self.world_map.tile_size
        map_start_x = 50
        map_start_y = 50
        
        # 渲染可见的地图格子
        visible_tiles = self.world_map.get_visible_tiles()
        
        for map_x, map_y, tile_id in visible_tiles:
            # 计算屏幕坐标
            screen_x = map_start_x + (map_x - self.world_map.camera_x) * tile_size
            screen_y = map_start_y + (map_y - self.world_map.camera_y) * tile_size
            
            # 获取瓦片颜色
            color = self.world_map.get_tile_color(tile_id)
            
            # 绘制瓦片
            pygame.draw.rect(self.screen, color, 
                           (screen_x, screen_y, tile_size, tile_size))
            
            # 绘制边框
            pygame.draw.rect(self.screen, self.colors['gray'], 
                           (screen_x, screen_y, tile_size, tile_size), 1)
            
            # 绘制瓦片字符
            char = self.world_map.get_tile_char(tile_id)
            char_text = self.font_small.render(char, True, self.colors['white'])
            char_rect = char_text.get_rect(center=(screen_x + tile_size // 2, 
                                                  screen_y + tile_size // 2))
            self.screen.blit(char_text, char_rect)
        
        # 渲染玩家
        player_screen_x = map_start_x + (self.world_map.player_x - self.world_map.camera_x) * tile_size
        player_screen_y = map_start_y + (self.world_map.player_y - self.world_map.camera_y) * tile_size
        
        # FC风格玩家图标
        player_rect = pygame.Rect(player_screen_x + 2, player_screen_y + 2, tile_size - 4, tile_size - 4)
        pygame.draw.ellipse(self.screen, self.colors['red'], player_rect)
        pygame.draw.ellipse(self.screen, self.colors['white'], player_rect, 2)
    
    def _render_ui(self):
        """渲染UI"""
        # 标题
        title_text = self.font_large.render("吞食天地2 - 基于原版ROM", True, self.colors['yellow'])
        self.screen.blit(title_text, (10, 10))
        
        # 位置信息
        pos_text = self.font_medium.render(f"位置: ({self.world_map.player_x}, {self.world_map.player_y})", 
                                          True, self.colors['white'])
        self.screen.blit(pos_text, (10, 50))
        
        # 当前瓦片信息
        current_tile = self.world_map.map_data.get((self.world_map.player_x, self.world_map.player_y), 0)
        tile_text = self.font_medium.render(f"地形: 瓦片ID {current_tile}", True, self.colors['white'])
        self.screen.blit(tile_text, (10, 75))
        
        # 控制说明
        controls = [
            "控制说明:",
            "WASD/方向键 - 移动",
            "ESC - 退出"
        ]
        
        for i, control in enumerate(controls):
            color = self.colors['yellow'] if i == 0 else self.colors['white']
            control_text = self.font_small.render(control, True, color)
            self.screen.blit(control_text, (10, self.SCREEN_HEIGHT - 80 + i * 20))
    
    def run(self):
        """主游戏循环"""
        print(f"启动{self.TITLE}")
        print("使用原版ROM数据的真实地图!")
        
        while self.running:
            # 处理事件
            self.handle_events()
            
            # 渲染画面
            self.render()
            
            # 控制帧率
            self.clock.tick(self.FPS)
        
        # 退出游戏
        self.quit()
    
    def quit(self):
        """退出游戏"""
        print("感谢游玩基于ROM数据的吞食天地2!")
        pygame.quit()
        sys.exit()

def main():
    """主函数"""
    try:
        game = AuthenticSangokushiGame()
        game.run()
    except Exception as e:
        print(f"游戏运行错误: {e}")
        import traceback
        traceback.print_exc()
        pygame.quit()
        sys.exit(1)

if __name__ == "__main__":
    main()
