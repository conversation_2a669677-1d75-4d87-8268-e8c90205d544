#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
俄罗斯方块游戏启动器
运行此文件来启动俄罗斯方块游戏
"""

import os
import sys

def main():
    try:
        # 导入并运行俄罗斯方块游戏
        from tetris import main as tetris_main
        print("正在启动俄罗斯方块游戏...")
        print("控制说明：")
        print("A/D 或 ←/→ - 左右移动")
        print("S 或 ↓ - 软降")
        print("W 或 ↑ - 旋转")
        print("SPACE - 硬降")
        print("P - 暂停/继续")
        print("R - 重新开始")
        print("\n游戏即将开始...")

        tetris_main()

    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保pygame已安装: pip install pygame")
    except Exception as e:
        print(f"游戏运行错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()