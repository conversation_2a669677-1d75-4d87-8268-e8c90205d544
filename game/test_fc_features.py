#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FC风格新功能
"""

import pygame
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_portraits():
    """测试角色头像"""
    print("=== 测试角色头像系统 ===")
    
    try:
        from character_portraits import PortraitManager
        
        portrait_manager = PortraitManager()
        
        # 测试主要角色头像
        characters = ["刘备", "关羽", "张飞", "诸葛亮", "赵云", "曹操", "吕布"]
        
        for char_name in characters:
            portrait = portrait_manager.get_portrait(char_name)
            if portrait:
                print(f"✓ {char_name} 头像: {portrait.get_size()}")
            else:
                print(f"✗ {char_name} 头像加载失败")
        
        print(f"头像系统测试完成，共 {len(characters)} 个角色")
        return True
        
    except Exception as e:
        print(f"✗ 头像系统测试失败: {e}")
        return False

def test_fc_map():
    """测试FC风格地图"""
    print("\n=== 测试FC风格地图系统 ===")
    
    try:
        from fc_world_map import FCWorldMap, FCTerrainType
        
        fc_map = FCWorldMap()
        
        print(f"✓ 地图大小: {fc_map.width} x {fc_map.height}")
        print(f"✓ 玩家位置: ({fc_map.player_x}, {fc_map.player_y})")
        print(f"✓ 重要城市数量: {len(fc_map.important_cities)}")
        
        # 测试地形类型
        terrain_count = {}
        for terrain in fc_map.map_data.values():
            terrain_count[terrain] = terrain_count.get(terrain, 0) + 1
        
        print("地形分布:")
        for terrain, count in terrain_count.items():
            print(f"  {terrain.value}: {count} 格")
        
        # 测试移动
        old_x, old_y = fc_map.player_x, fc_map.player_y
        success = fc_map.move_player(1, 0)
        if success:
            print(f"✓ 移动测试: ({old_x}, {old_y}) -> ({fc_map.player_x}, {fc_map.player_y})")
        
        # 测试当前位置
        location = fc_map.get_current_location()
        print(f"✓ 当前位置: {location}")
        
        # 测试遭遇率
        encounter_rate = fc_map.get_encounter_rate()
        print(f"✓ 遭遇率: {encounter_rate:.1%}")
        
        return True
        
    except Exception as e:
        print(f"✗ FC地图系统测试失败: {e}")
        return False

def test_story_system():
    """测试故事系统"""
    print("\n=== 测试故事系统 ===")
    
    try:
        from story_system import StorySystem, StoryChapter
        
        story = StorySystem()
        
        print(f"✓ 当前章节: {story.current_chapter.value}")
        print(f"✓ 故事进度: {story.get_story_progress():.1f}%")
        
        # 测试可用事件
        available_events = story.get_available_events()
        print(f"✓ 可用事件数量: {len(available_events)}")
        
        for event in available_events:
            print(f"  - {event.title}: {event.description[:30]}...")
        
        # 测试完成事件
        if available_events:
            first_event = available_events[0]
            rewards = story.complete_event(first_event.event_id)
            print(f"✓ 完成事件: {first_event.title}")
            print(f"  奖励: {rewards}")
            
            # 检查新的可用事件
            new_available = story.get_available_events()
            print(f"✓ 新可用事件: {len(new_available)}")
        
        # 测试章节信息
        chapter_info = story.get_current_chapter_info()
        print(f"✓ 章节信息: {chapter_info}")
        
        return True
        
    except Exception as e:
        print(f"✗ 故事系统测试失败: {e}")
        return False

def test_visual_display():
    """测试可视化显示"""
    print("\n=== 测试可视化显示 ===")
    
    try:
        pygame.init()
        
        # 创建测试窗口
        screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("FC功能测试")
        
        from character_portraits import PortraitManager
        from fc_world_map import FCWorldMap
        
        portrait_manager = PortraitManager()
        fc_map = FCWorldMap()
        
        clock = pygame.time.Clock()
        running = True
        test_mode = 0  # 0: 头像, 1: 地图
        
        print("✓ 可视化测试窗口已打开")
        print("  按空格切换测试模式，ESC退出")
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        test_mode = (test_mode + 1) % 2
            
            # 清空屏幕
            screen.fill((0, 0, 0))
            
            if test_mode == 0:
                # 显示角色头像
                characters = ["刘备", "关羽", "张飞", "诸葛亮", "赵云", "曹操"]
                for i, char_name in enumerate(characters):
                    portrait = portrait_manager.get_portrait(char_name)
                    x = 50 + (i % 3) * 200
                    y = 50 + (i // 3) * 150
                    
                    screen.blit(portrait, (x, y))
                    
                    # 显示角色名称
                    font = pygame.font.Font(None, 24)
                    name_text = font.render(char_name, True, (255, 255, 255))
                    screen.blit(name_text, (x, y + 70))
                
                # 显示说明
                font = pygame.font.Font(None, 32)
                title_text = font.render("角色头像测试 (按空格切换)", True, (255, 255, 0))
                screen.blit(title_text, (50, 10))
                
            else:
                # 显示地图
                visible_tiles = fc_map.get_visible_tiles()
                tile_size = 20
                
                for map_x, map_y, terrain, city_name in visible_tiles:
                    screen_x = 50 + (map_x - fc_map.camera_x) * tile_size
                    screen_y = 50 + (map_y - fc_map.camera_y) * tile_size
                    
                    color = fc_map.get_terrain_color(terrain)
                    pygame.draw.rect(screen, color, (screen_x, screen_y, tile_size, tile_size))
                    
                    if city_name:
                        pygame.draw.circle(screen, (255, 255, 0), 
                                         (screen_x + tile_size//2, screen_y + tile_size//2), 3)
                
                # 显示玩家
                player_x = 50 + (fc_map.player_x - fc_map.camera_x) * tile_size
                player_y = 50 + (fc_map.player_y - fc_map.camera_y) * tile_size
                pygame.draw.circle(screen, (255, 0, 0), 
                                 (player_x + tile_size//2, player_y + tile_size//2), 5)
                
                # 显示说明
                font = pygame.font.Font(None, 32)
                title_text = font.render("FC地图测试 (按空格切换)", True, (255, 255, 0))
                screen.blit(title_text, (50, 10))
            
            pygame.display.flip()
            clock.tick(60)
        
        pygame.quit()
        print("✓ 可视化测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试FC吞食天地2新功能...\n")
    
    tests = [
        test_portraits,
        test_fc_map,
        test_story_system,
        test_visual_display,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有FC功能测试通过!")
        print("💡 运行 'python 三国.py' 体验完整游戏")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()
