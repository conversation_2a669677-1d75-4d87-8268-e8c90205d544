# 吞食天地2 - 三国RPG游戏使用说明

## 🎮 游戏简介

《吞食天地2》是基于FC经典游戏的Python重制版，现已完全集成到`三国.py`文件中。游戏采用回合制RPG玩法，包含角色培养、战斗系统、世界探索等经典要素。

## ✅ 问题解决状态

### 中文字体显示问题 - 已解决 ✅
- **问题**：界面文字显示乱码
- **解决方案**：
  - 自动检测并加载系统中文字体（黑体、微软雅黑、宋体等）
  - 实现了智能字体回退机制
  - 所有UI文本都使用专门的中文渲染函数

### 主程序整合 - 已完成 ✅
- **问题**：需要将主程序更新到"三国.py"
- **解决方案**：
  - 完整的游戏代码已集成到`三国.py`文件
  - 保持了所有原有功能
  - 优化了中文显示和用户体验

## 🚀 启动游戏

### 方法1：直接运行Python文件
```bash
python 三国.py
```

### 方法2：使用批处理文件（推荐）
双击运行：`启动三国.bat`

### 方法3：命令行启动
```bash
cd game
python 三国.py
```

## 🎯 游戏特色

### 完美的中文支持
- ✅ 自动加载系统中文字体
- ✅ 支持黑体、微软雅黑、宋体等多种字体
- ✅ 所有界面文字正常显示
- ✅ 角色名称、城市名称、菜单选项完全中文化

### 经典三国题材
- 🏛️ 刘备、关羽、张飞、诸葛亮等经典武将
- 🏰 洛阳、长安、新野、成都等历史名城
- ⚔️ 回合制战斗系统
- 📈 角色升级和装备系统

### 完整的游戏系统
- 🗺️ 20x15大世界地图探索
- 👥 6人队伍管理系统
- 💾 10槽位存档系统
- 🎒 完整的物品和装备系统

## 🎮 操作指南

### 主菜单
- **↑↓ / W S**：选择菜单选项
- **回车 / 空格**：确认选择
- **ESC**：退出游戏

### 世界地图
- **WASD / 方向键**：移动角色
- **回车 / 空格**：进入城市/确认
- **ESC**：返回主菜单

### 战斗界面
- **↑↓ / W S**：选择行动或目标
- **回车 / 空格**：确认行动
- **ESC**：返回地图（战斗结束后）

## 🔧 系统要求

- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.8+
- **依赖库**：pygame 2.0+
- **中文字体**：系统自带（黑体/微软雅黑/宋体）

## 📁 文件结构

```
game/
├── 三国.py                    # 主游戏程序（新）
├── 启动三国.bat               # 游戏启动器（新）
├── test_三国.py              # 游戏测试程序（新）
├── character.py              # 角色系统
├── battle.py                # 战斗系统
├── map_system.py            # 地图系统
├── ui.py                    # UI界面系统（已优化中文显示）
├── game_data.py             # 游戏数据
├── game_state.py            # 游戏状态管理
├── resources.py             # 资源管理（已优化字体加载）
├── saves/                   # 存档目录
└── 三国游戏使用说明.md        # 本说明文件
```

## 🧪 测试验证

运行测试程序验证系统：
```bash
python test_三国.py
```

测试内容包括：
- ✅ 模块导入测试
- ✅ 中文字体显示测试
- ✅ 游戏数据测试
- ✅ 角色系统测试
- ✅ 战斗系统测试

## 🎯 游戏内容

### 默认队伍
- **刘备**：仁德之主，平衡型大将
- **关羽**：武圣，强力武将
- **张飞**：猛张飞，勇猛武将

### 重要城市
- **洛阳**：东汉都城，最繁华的城市
- **长安**：西部重镇，军事要地
- **新野**：刘备根据地，起始城市
- **成都**：益州首府，富庶之地
- **徐州**：东部要地，战略位置

### 装备系统
- **武器**：从木剑到传说武器青龙偃月刀
- **防具**：从布衣到传说护甲玄武甲
- **道具**：药草、魔法药水、特殊道具

## 🐛 故障排除

### 如果游戏无法启动
1. 确保已安装Python 3.8+
2. 安装pygame：`pip install pygame`
3. 检查文件完整性

### 如果中文显示异常
1. 运行测试：`python test_三国.py`
2. 检查系统是否有中文字体
3. 确保系统编码设置正确

### 如果游戏运行缓慢
1. 关闭其他程序释放内存
2. 检查系统资源使用情况
3. 降低游戏窗口大小

## 📞 技术支持

如遇到问题，请：
1. 首先运行测试程序检查系统状态
2. 查看控制台输出的错误信息
3. 确认所有依赖库已正确安装

## 🎉 开始游戏

一切准备就绪！现在您可以：

1. **双击 `启动三国.bat`** 开始游戏
2. 或者运行 `python 三国.py`
3. 享受经典的三国RPG体验！

---

**祝您游戏愉快，早日统一三国！** ⚔️🏆
