#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
俄罗斯方块游戏测试脚本
测试游戏的核心功能是否正常工作
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    try:
        import pygame
        print("✓ Pygame导入成功")
        
        from tetris import TetrisGame, Tetromino, TETROMINOES
        print("✓ 游戏模块导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_tetromino_creation():
    """测试方块创建"""
    try:
        from tetris import Tetromino, TETROMINOES
        
        # 测试所有方块类型
        for shape in TETROMINOES.keys():
            piece = Tetromino(shape)
            cells = piece.get_cells()
            print(f"✓ {shape}型方块创建成功，占用{len(cells)}个格子")
        
        return True
    except Exception as e:
        print(f"✗ 方块创建测试失败: {e}")
        return False

def test_game_initialization():
    """测试游戏初始化"""
    try:
        # 设置pygame为无头模式（不显示窗口）
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        
        import pygame
        pygame.init()
        
        from tetris import TetrisGame
        
        game = TetrisGame()
        
        # 检查初始状态
        assert game.score == 0, "初始分数应为0"
        assert game.level == 1, "初始等级应为1"
        assert game.lines_cleared == 0, "初始消行数应为0"
        assert not game.game_over, "游戏不应结束"
        assert not game.paused, "游戏不应暂停"
        assert game.current_piece is not None, "应有当前方块"
        assert game.next_piece is not None, "应有下一个方块"
        
        print("✓ 游戏初始化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 游戏初始化测试失败: {e}")
        return False

def test_piece_movement():
    """测试方块移动"""
    try:
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        
        import pygame
        pygame.init()
        
        from tetris import TetrisGame
        
        game = TetrisGame()
        
        # 记录初始位置
        initial_x = game.current_piece.x
        initial_y = game.current_piece.y
        
        # 测试左移
        game.move_piece(-1, 0)
        assert game.current_piece.x == initial_x - 1, "左移失败"
        
        # 测试右移
        game.move_piece(1, 0)
        assert game.current_piece.x == initial_x, "右移失败"
        
        # 测试下移
        game.move_piece(0, 1)
        assert game.current_piece.y == initial_y + 1, "下移失败"
        
        print("✓ 方块移动测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 方块移动测试失败: {e}")
        return False

def test_piece_rotation():
    """测试方块旋转"""
    try:
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        
        import pygame
        pygame.init()
        
        from tetris import TetrisGame, Tetromino
        
        game = TetrisGame()
        
        # 创建一个T型方块进行旋转测试
        game.current_piece = Tetromino('T')
        initial_rotation = game.current_piece.rotation
        
        # 测试旋转
        game.rotate_piece()
        assert game.current_piece.rotation != initial_rotation, "旋转失败"
        
        print("✓ 方块旋转测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 方块旋转测试失败: {e}")
        return False

def test_line_clearing():
    """测试消行功能"""
    try:
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        
        import pygame
        pygame.init()
        
        from tetris import TetrisGame, RED
        
        game = TetrisGame()
        
        # 手动填满最底行
        for x in range(10):
            game.grid[19][x] = RED
        
        initial_score = game.score
        initial_lines = game.lines_cleared
        
        # 触发消行
        game.clear_lines()
        
        # 检查结果
        assert game.lines_cleared == initial_lines + 1, "消行数未增加"
        assert game.score > initial_score, "分数未增加"
        assert all(cell == (0, 0, 0) for cell in game.grid[19]), "底行未清空"
        
        print("✓ 消行功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 消行功能测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始俄罗斯方块游戏测试...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_tetromino_creation,
        test_game_initialization,
        test_piece_movement,
        test_piece_rotation,
        test_line_clearing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！游戏核心功能正常。")
        print("\n游戏控制说明:")
        print("A/D 或 ←/→ - 左右移动")
        print("S 或 ↓ - 软降")
        print("W 或 ↑ - 旋转")
        print("SPACE - 硬降")
        print("P - 暂停/继续")
        print("R - 重新开始")
        print("\n可以运行以下命令启动游戏:")
        print("python tetris.py")
        print("或")
        print("python 俄罗斯方块.py")
    else:
        print("❌ 部分测试失败，请检查代码。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
