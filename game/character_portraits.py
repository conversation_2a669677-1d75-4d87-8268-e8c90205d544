#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角色头像系统 - FC吞食天地2风格
"""

import pygame
from typing import Dict, Tuple

class PortraitManager:
    """角色头像管理器"""
    
    def __init__(self):
        self.portraits = {}
        self.portrait_size = (64, 64)  # FC风格头像大小
        self._create_pixel_portraits()
    
    def _create_pixel_portraits(self):
        """创建像素风格头像"""
        # 刘备 - 仁德之主
        self.portraits["刘备"] = self._create_liu_bei_portrait()
        
        # 关羽 - 红脸长须
        self.portraits["关羽"] = self._create_guan_yu_portrait()
        
        # 张飞 - 黑脸虬髯
        self.portraits["张飞"] = self._create_zhang_fei_portrait()
        
        # 诸葛亮 - 羽扇纶巾
        self.portraits["诸葛亮"] = self._create_zhuge_liang_portrait()
        
        # 赵云 - 英俊武将
        self.portraits["赵云"] = self._create_zhao_yun_portrait()
        
        # 曹操 - 奸雄面相
        self.portraits["曹操"] = self._create_cao_cao_portrait()
        
        # 孙权 - 江东之主
        self.portraits["孙权"] = self._create_sun_quan_portrait()
        
        # 吕布 - 威猛无双
        self.portraits["吕布"] = self._create_lu_bu_portrait()
    
    def _create_liu_bei_portrait(self):
        """创建刘备头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))  # 肤色背景
        
        # 脸部轮廓
        pygame.draw.ellipse(surface, (255, 220, 177), (16, 12, 32, 40))
        
        # 眼睛
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        pygame.draw.circle(surface, (255, 255, 255), (25, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (41, 23), 1)
        
        # 鼻子
        pygame.draw.circle(surface, (200, 180, 160), (32, 30), 1)
        
        # 嘴巴
        pygame.draw.arc(surface, (150, 100, 100), (28, 34, 8, 4), 0, 3.14, 2)
        
        # 胡须
        pygame.draw.line(surface, (100, 80, 60), (28, 42), (28, 50), 2)
        pygame.draw.line(surface, (100, 80, 60), (32, 42), (32, 52), 2)
        pygame.draw.line(surface, (100, 80, 60), (36, 42), (36, 50), 2)
        
        # 头发
        pygame.draw.ellipse(surface, (50, 40, 30), (14, 8, 36, 20))
        
        # 帽子/头饰
        pygame.draw.rect(surface, (200, 150, 0), (20, 6, 24, 8))
        
        return surface
    
    def _create_guan_yu_portrait(self):
        """创建关羽头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 红脸
        pygame.draw.ellipse(surface, (220, 100, 100), (16, 12, 32, 40))
        
        # 眼睛 - 丹凤眼
        pygame.draw.ellipse(surface, (0, 0, 0), (22, 22, 6, 3))
        pygame.draw.ellipse(surface, (0, 0, 0), (36, 22, 6, 3))
        pygame.draw.circle(surface, (255, 255, 255), (25, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (39, 23), 1)
        
        # 长须
        for i in range(5):
            x = 26 + i * 3
            pygame.draw.line(surface, (80, 60, 40), (x, 45), (x, 58), 2)
        
        # 头发
        pygame.draw.ellipse(surface, (40, 30, 20), (14, 8, 36, 20))
        
        # 头巾
        pygame.draw.polygon(surface, (0, 100, 0), [(20, 6), (44, 6), (40, 16), (24, 16)])
        
        return surface
    
    def _create_zhang_fei_portrait(self):
        """创建张飞头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 黑脸
        pygame.draw.ellipse(surface, (80, 60, 40), (16, 12, 32, 40))
        
        # 怒目
        pygame.draw.circle(surface, (255, 0, 0), (24, 24), 3)
        pygame.draw.circle(surface, (255, 0, 0), (40, 24), 3)
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        
        # 虬髯
        for i in range(7):
            x = 22 + i * 2
            y = 40 + (i % 2) * 2
            pygame.draw.line(surface, (20, 15, 10), (x, y), (x, y + 8), 1)
        
        # 头发
        pygame.draw.ellipse(surface, (20, 15, 10), (14, 8, 36, 20))
        
        return surface
    
    def _create_zhuge_liang_portrait(self):
        """创建诸葛亮头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 文雅面容
        pygame.draw.ellipse(surface, (255, 230, 200), (16, 12, 32, 40))
        
        # 智慧之眼
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        pygame.draw.circle(surface, (255, 255, 255), (25, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (41, 23), 1)
        
        # 山羊胡
        pygame.draw.polygon(surface, (100, 80, 60), [(30, 42), (34, 42), (32, 52)])
        
        # 纶巾
        pygame.draw.ellipse(surface, (255, 255, 255), (18, 6, 28, 16))
        pygame.draw.line(surface, (100, 100, 100), (20, 10), (42, 10), 2)
        
        return surface
    
    def _create_zhao_yun_portrait(self):
        """创建赵云头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 英俊面容
        pygame.draw.ellipse(surface, (255, 225, 190), (16, 12, 32, 40))
        
        # 英气眼神
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        pygame.draw.circle(surface, (255, 255, 255), (25, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (41, 23), 1)
        
        # 短须
        pygame.draw.line(surface, (80, 60, 40), (30, 40), (30, 46), 2)
        pygame.draw.line(surface, (80, 60, 40), (34, 40), (34, 46), 2)
        
        # 头盔
        pygame.draw.ellipse(surface, (150, 150, 150), (16, 6, 32, 18))
        pygame.draw.circle(surface, (200, 200, 0), (32, 10), 3)
        
        return surface
    
    def _create_cao_cao_portrait(self):
        """创建曹操头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 奸雄面相
        pygame.draw.ellipse(surface, (240, 210, 180), (16, 12, 32, 40))
        
        # 狡黠眼神
        pygame.draw.ellipse(surface, (0, 0, 0), (22, 22, 6, 4))
        pygame.draw.ellipse(surface, (0, 0, 0), (36, 22, 6, 4))
        pygame.draw.circle(surface, (255, 255, 255), (24, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (38, 23), 1)
        
        # 胡须
        for i in range(4):
            x = 28 + i * 2
            pygame.draw.line(surface, (60, 40, 20), (x, 42), (x, 50), 2)
        
        # 冠冕
        pygame.draw.rect(surface, (100, 50, 0), (18, 6, 28, 12))
        pygame.draw.rect(surface, (200, 150, 0), (20, 8, 24, 8))
        
        return surface
    
    def _create_sun_quan_portrait(self):
        """创建孙权头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 年轻面容
        pygame.draw.ellipse(surface, (255, 220, 185), (16, 12, 32, 40))
        
        # 眼睛
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        pygame.draw.circle(surface, (255, 255, 255), (25, 23), 1)
        pygame.draw.circle(surface, (255, 255, 255), (41, 23), 1)
        
        # 短须
        pygame.draw.line(surface, (80, 60, 40), (32, 40), (32, 48), 2)
        
        # 紫色头巾
        pygame.draw.ellipse(surface, (100, 50, 150), (18, 6, 28, 16))
        
        return surface
    
    def _create_lu_bu_portrait(self):
        """创建吕布头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((240, 220, 180))
        
        # 威猛面容
        pygame.draw.ellipse(surface, (230, 200, 170), (16, 12, 32, 40))
        
        # 凶狠眼神
        pygame.draw.circle(surface, (150, 0, 0), (24, 24), 3)
        pygame.draw.circle(surface, (150, 0, 0), (40, 24), 3)
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        
        # 络腮胡
        pygame.draw.ellipse(surface, (40, 30, 20), (18, 35, 28, 20))
        
        # 头盔
        pygame.draw.ellipse(surface, (100, 100, 100), (16, 6, 32, 18))
        pygame.draw.polygon(surface, (200, 0, 0), [(32, 4), (28, 12), (36, 12)])
        
        return surface
    
    def get_portrait(self, character_name: str) -> pygame.Surface:
        """获取角色头像"""
        return self.portraits.get(character_name, self._create_default_portrait())
    
    def _create_default_portrait(self) -> pygame.Surface:
        """创建默认头像"""
        surface = pygame.Surface(self.portrait_size)
        surface.fill((200, 200, 200))
        
        # 简单的默认头像
        pygame.draw.ellipse(surface, (180, 180, 180), (16, 12, 32, 40))
        pygame.draw.circle(surface, (0, 0, 0), (24, 24), 2)
        pygame.draw.circle(surface, (0, 0, 0), (40, 24), 2)
        
        return surface
