#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FC吞食天地2风格世界地图
"""

import pygame
from typing import Dict, List, Tuple
from enum import Enum

class FCTerrainType(Enum):
    PLAINS = "平原"
    MOUNTAINS = "山地"
    WATER = "水域"
    FOREST = "森林"
    DESERT = "沙漠"
    CITY = "城市"
    CASTLE = "城堡"
    VILLAGE = "村庄"
    BRIDGE = "桥梁"
    PASS = "关隘"

class FCWorldMap:
    """FC风格世界地图"""
    
    def __init__(self):
        self.width = 32
        self.height = 24
        self.tile_size = 24
        self.map_data = {}
        
        # 玩家位置
        self.player_x = 16
        self.player_y = 12
        
        # 摄像机
        self.camera_x = 0
        self.camera_y = 0
        self.view_width = 20
        self.view_height = 15
        
        # 重要城市位置
        self.important_cities = {
            "洛阳": (16, 10),
            "长安": (12, 11),
            "邺城": (18, 8),
            "许昌": (17, 12),
            "新野": (15, 14),
            "襄阳": (15, 15),
            "江陵": (13, 16),
            "建业": (22, 16),
            "成都": (8, 16),
            "汉中": (10, 13),
            "武威": (6, 8),
            "天水": (8, 10),
            "安定": (9, 9),
            "上庸": (13, 14),
            "江夏": (17, 16),
            "柴桑": (20, 17),
            "会稽": (25, 18),
            "吴郡": (24, 17),
            "庐江": (21, 15),
            "合肥": (22, 14),
        }

        # 创建FC风格地图
        self._create_fc_china_map()

    def _create_fc_china_map(self):
        """创建FC风格的中国地图"""
        # 初始化为平原
        for y in range(self.height):
            for x in range(self.width):
                self.map_data[(x, y)] = FCTerrainType.PLAINS
        
        # 添加山脉
        self._add_mountain_ranges()
        
        # 添加水域
        self._add_water_bodies()
        
        # 添加森林
        self._add_forests()
        
        # 添加城市
        self._add_cities()
        
        # 添加关隘和桥梁
        self._add_passes_and_bridges()
    
    def _add_mountain_ranges(self):
        """添加山脉"""
        # 太行山脉
        for y in range(6, 14):
            for x in range(14, 17):
                if (x + y) % 2 == 0:
                    self.map_data[(x, y)] = FCTerrainType.MOUNTAINS
        
        # 秦岭
        for x in range(8, 16):
            y = 12 + (x - 8) // 3
            if y < self.height:
                self.map_data[(x, y)] = FCTerrainType.MOUNTAINS
        
        # 大巴山
        for x in range(10, 14):
            for y in range(15, 17):
                self.map_data[(x, y)] = FCTerrainType.MOUNTAINS
        
        # 祁连山
        for x in range(4, 10):
            for y in range(6, 9):
                if (x + y) % 3 == 0:
                    self.map_data[(x, y)] = FCTerrainType.MOUNTAINS
        
        # 燕山
        for x in range(18, 24):
            for y in range(4, 7):
                if (x - y) % 2 == 0:
                    self.map_data[(x, y)] = FCTerrainType.MOUNTAINS
    
    def _add_water_bodies(self):
        """添加水域"""
        # 黄河
        yellow_river_points = [
            (6, 9), (7, 9), (8, 9), (9, 8), (10, 8), (11, 8),
            (12, 9), (13, 9), (14, 10), (15, 10), (16, 11), (17, 11)
        ]
        for x, y in yellow_river_points:
            self.map_data[(x, y)] = FCTerrainType.WATER
        
        # 长江
        yangtze_river_points = [
            (8, 17), (9, 17), (10, 17), (11, 16), (12, 16), (13, 17),
            (14, 17), (15, 17), (16, 17), (17, 17), (18, 17), (19, 17),
            (20, 16), (21, 16), (22, 17), (23, 17), (24, 18)
        ]
        for x, y in yangtze_river_points:
            self.map_data[(x, y)] = FCTerrainType.WATER
        
        # 渭河
        for x in range(10, 15):
            self.map_data[(x, 11)] = FCTerrainType.WATER
        
        # 汉水
        for x in range(11, 16):
            y = 14 + (x - 11) // 2
            if y < self.height:
                self.map_data[(x, y)] = FCTerrainType.WATER
    
    def _add_forests(self):
        """添加森林"""
        # 南方森林
        forest_areas = [
            (19, 18), (20, 18), (21, 18), (22, 19), (23, 19),
            (18, 19), (19, 19), (20, 19), (21, 19),
            (9, 18), (10, 18), (11, 19), (12, 19)
        ]
        for x, y in forest_areas:
            if y < self.height and self.map_data.get((x, y)) == FCTerrainType.PLAINS:
                self.map_data[(x, y)] = FCTerrainType.FOREST
    
    def _add_cities(self):
        """添加城市"""
        for city_name, (x, y) in self.important_cities.items():
            if city_name in ["洛阳", "长安", "邺城", "建业", "成都"]:
                self.map_data[(x, y)] = FCTerrainType.CITY
            elif city_name in ["汉中", "武威", "天水"]:
                self.map_data[(x, y)] = FCTerrainType.CASTLE
            else:
                self.map_data[(x, y)] = FCTerrainType.VILLAGE
    
    def _add_passes_and_bridges(self):
        """添加关隘和桥梁"""
        # 函谷关
        self.map_data[(13, 11)] = FCTerrainType.PASS
        
        # 剑阁
        self.map_data[(9, 14)] = FCTerrainType.PASS
        
        # 桥梁
        bridge_points = [(14, 10), (16, 17), (20, 16)]
        for x, y in bridge_points:
            if self.map_data.get((x, y)) == FCTerrainType.WATER:
                self.map_data[(x, y)] = FCTerrainType.BRIDGE
    
    def get_terrain_color(self, terrain: FCTerrainType) -> Tuple[int, int, int]:
        """获取地形颜色"""
        colors = {
            FCTerrainType.PLAINS: (144, 238, 144),      # 浅绿色
            FCTerrainType.MOUNTAINS: (139, 69, 19),     # 棕色
            FCTerrainType.WATER: (0, 191, 255),         # 蓝色
            FCTerrainType.FOREST: (34, 139, 34),        # 深绿色
            FCTerrainType.DESERT: (238, 203, 173),      # 沙色
            FCTerrainType.CITY: (255, 215, 0),          # 金色
            FCTerrainType.CASTLE: (128, 0, 128),        # 紫色
            FCTerrainType.VILLAGE: (255, 165, 0),       # 橙色
            FCTerrainType.BRIDGE: (160, 82, 45),        # 棕色
            FCTerrainType.PASS: (105, 105, 105),        # 灰色
        }
        return colors.get(terrain, (128, 128, 128))
    
    def get_terrain_char(self, terrain: FCTerrainType) -> str:
        """获取地形字符"""
        chars = {
            FCTerrainType.PLAINS: ".",
            FCTerrainType.MOUNTAINS: "^",
            FCTerrainType.WATER: "~",
            FCTerrainType.FOREST: "T",
            FCTerrainType.DESERT: ":",
            FCTerrainType.CITY: "C",
            FCTerrainType.CASTLE: "H",
            FCTerrainType.VILLAGE: "V",
            FCTerrainType.BRIDGE: "=",
            FCTerrainType.PASS: "X",
        }
        return chars.get(terrain, "?")
    
    def move_player(self, dx: int, dy: int) -> bool:
        """移动玩家"""
        new_x = self.player_x + dx
        new_y = self.player_y + dy
        
        # 检查边界
        if not (0 <= new_x < self.width and 0 <= new_y < self.height):
            return False
        
        # 检查地形是否可通行
        terrain = self.map_data.get((new_x, new_y))
        if terrain == FCTerrainType.WATER:
            return False
        
        # 移动玩家
        self.player_x = new_x
        self.player_y = new_y
        
        # 更新摄像机
        self._update_camera()
        
        return True
    
    def _update_camera(self):
        """更新摄像机位置"""
        self.camera_x = max(0, min(self.width - self.view_width, 
                                  self.player_x - self.view_width // 2))
        self.camera_y = max(0, min(self.height - self.view_height, 
                                  self.player_y - self.view_height // 2))
    
    def get_visible_tiles(self) -> List[Tuple[int, int, FCTerrainType, str]]:
        """获取可见的地图格子"""
        visible_tiles = []
        
        for y in range(self.camera_y, min(self.camera_y + self.view_height, self.height)):
            for x in range(self.camera_x, min(self.camera_x + self.view_width, self.width)):
                terrain = self.map_data.get((x, y), FCTerrainType.PLAINS)
                
                # 检查是否是重要城市
                city_name = ""
                for name, (cx, cy) in self.important_cities.items():
                    if cx == x and cy == y:
                        city_name = name
                        break
                
                visible_tiles.append((x, y, terrain, city_name))
        
        return visible_tiles
    
    def get_current_location(self) -> str:
        """获取当前位置信息"""
        # 检查是否在城市
        for city_name, (x, y) in self.important_cities.items():
            if x == self.player_x and y == self.player_y:
                return city_name
        
        # 返回地形类型
        terrain = self.map_data.get((self.player_x, self.player_y), FCTerrainType.PLAINS)
        return terrain.value
    
    def can_enter_city(self) -> bool:
        """检查是否可以进入城市"""
        terrain = self.map_data.get((self.player_x, self.player_y))
        return terrain in [FCTerrainType.CITY, FCTerrainType.CASTLE, FCTerrainType.VILLAGE]
    
    def get_encounter_rate(self) -> float:
        """获取遭遇率"""
        terrain = self.map_data.get((self.player_x, self.player_y), FCTerrainType.PLAINS)
        
        rates = {
            FCTerrainType.PLAINS: 0.08,
            FCTerrainType.MOUNTAINS: 0.15,
            FCTerrainType.FOREST: 0.12,
            FCTerrainType.DESERT: 0.10,
            FCTerrainType.CITY: 0.0,
            FCTerrainType.CASTLE: 0.0,
            FCTerrainType.VILLAGE: 0.0,
            FCTerrainType.BRIDGE: 0.05,
            FCTerrainType.PASS: 0.20,
        }
        
        return rates.get(terrain, 0.08)

    def should_enter_battle(self) -> bool:
        """检查是否应该进入战斗"""
        return hasattr(self, 'pending_battle') and self.pending_battle is not None

    def get_battle_data(self) -> dict:
        """获取战斗数据"""
        if hasattr(self, 'pending_battle') and self.pending_battle:
            battle_data = self.pending_battle
            self.pending_battle = None
            return battle_data
        return {"enemies": []}

    def handle_battle_result(self, result: dict):
        """处理战斗结果"""
        if result.get('result') == 'victory':
            print(f"战斗胜利! 获得 {result.get('exp', 0)} 经验值")
        elif result.get('result') == 'defeat':
            print("战斗失败...")
        elif result.get('result') == 'escaped':
            print("成功逃脱")

    def update(self):
        """更新地图逻辑"""
        # 检查随机遭遇
        if not hasattr(self, 'pending_battle'):
            self.pending_battle = None

        if not hasattr(self, 'last_battle_pos'):
            self.last_battle_pos = None

        # 随机遭遇检查
        current_pos = (self.player_x, self.player_y)
        if current_pos != self.last_battle_pos:
            encounter_rate = self.get_encounter_rate()

            import random
            if random.random() < encounter_rate:
                self._trigger_random_battle()

    def _trigger_random_battle(self):
        """触发随机战斗"""
        import random
        from battle import Enemy

        # 根据地形生成敌人
        terrain = self.map_data.get((self.player_x, self.player_y))
        enemy_count = random.randint(1, 3)
        enemies = []

        for _ in range(enemy_count):
            enemy_level = random.randint(1, 5)
            enemy_name = self._get_random_enemy_name(terrain)
            enemy_type = "soldier"

            if random.random() < 0.1:  # 10%概率遇到精英
                enemy_type = "elite"
                enemy_name = f"精英{enemy_name}"

            enemies.append(Enemy(enemy_name, enemy_level, enemy_type))

        self.pending_battle = {
            'enemies': enemies,
            'terrain': terrain
        }

        self.last_battle_pos = (self.player_x, self.player_y)

    def _get_random_enemy_name(self, terrain) -> str:
        """根据地形获取随机敌人名称"""
        enemy_names = {
            FCTerrainType.PLAINS: ["流寇", "山贼", "野兽"],
            FCTerrainType.MOUNTAINS: ["山贼", "强盗", "猛虎"],
            FCTerrainType.FOREST: ["森林盗贼", "野狼", "熊"],
            FCTerrainType.PASS: ["关隘守卫", "盗贼头目"],
        }

        names = enemy_names.get(terrain, ["敌兵", "盗贼"])
        import random
        return random.choice(names)

    def handle_confirm(self):
        """处理确认操作"""
        # 检查是否可以进入城市
        if self.can_enter_city():
            location = self.get_current_location()
            print(f"进入 {location}")
            # 这里可以触发城市界面
            # 暂时只是打印信息
