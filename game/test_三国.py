#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三国游戏测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_game_import():
    """测试游戏模块导入"""
    print("=== 测试游戏模块导入 ===")
    
    try:
        # 测试导入所有模块
        from character import Character, CharacterClass, CharacterManager
        print("✓ 角色系统模块导入成功")
        
        from battle import BattleSystem, Enemy
        print("✓ 战斗系统模块导入成功")
        
        from map_system import WorldMap, TerrainType
        print("✓ 地图系统模块导入成功")
        
        from ui import UIManager
        print("✓ UI系统模块导入成功")
        
        from game_data import GameData
        print("✓ 游戏数据模块导入成功")
        
        from game_state import GameState
        print("✓ 游戏状态模块导入成功")
        
        from resources import ResourceManager
        print("✓ 资源管理模块导入成功")
        
        print("所有模块导入成功!")
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_chinese_display():
    """测试中文显示"""
    print("\n=== 测试中文显示 ===")
    
    try:
        import pygame
        pygame.init()
        
        # 测试中文字体加载
        chinese_fonts = ["SimHei", "Microsoft YaHei", "SimSun"]
        loaded_fonts = []
        
        for font_name in chinese_fonts:
            try:
                font = pygame.font.SysFont(font_name, 24)
                test_surface = font.render("测试", True, (255, 255, 255))
                if test_surface.get_width() > 0:
                    loaded_fonts.append(font_name)
                    print(f"✓ 成功加载中文字体: {font_name}")
            except:
                print(f"✗ 无法加载字体: {font_name}")
        
        if loaded_fonts:
            print(f"共加载了 {len(loaded_fonts)} 个中文字体")
            
            # 测试中文文本渲染
            test_texts = ["吞食天地2", "三国RPG", "刘备", "关羽", "张飞"]
            font = pygame.font.SysFont(loaded_fonts[0], 24)
            
            for text in test_texts:
                try:
                    surface = font.render(text, True, (255, 255, 255))
                    print(f"✓ 成功渲染文本: {text} (宽度: {surface.get_width()})")
                except Exception as e:
                    print(f"✗ 渲染失败: {text} - {e}")
        else:
            print("✗ 没有找到可用的中文字体")
            return False
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"✗ 中文显示测试失败: {e}")
        return False

def test_game_data():
    """测试游戏数据"""
    print("\n=== 测试游戏数据 ===")
    
    try:
        from game_data import GameData
        
        game_data = GameData()
        
        # 测试武将数据
        weapons = game_data.get_all_weapons()
        print(f"✓ 武器数据: {len(weapons)} 种武器")
        
        # 测试城市数据
        cities = game_data.get_all_cities()
        print(f"✓ 城市数据: {len(cities)} 个城市")
        
        # 测试角色信息
        char_info = game_data.get_character_info("刘备")
        if char_info:
            print(f"✓ 角色信息: 刘备 - {char_info.get('title', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 游戏数据测试失败: {e}")
        return False

def test_character_system():
    """测试角色系统"""
    print("\n=== 测试角色系统 ===")
    
    try:
        from character import Character, CharacterClass, CharacterManager
        from game_data import GameData
        
        game_data = GameData()
        char_manager = CharacterManager(game_data)
        
        # 测试默认队伍
        print(f"✓ 默认队伍人数: {len(char_manager.party)}")
        
        for char in char_manager.party:
            print(f"  - {char.name} (Lv.{char.level}) HP:{char.current_hp}/{char.max_hp}")
        
        # 测试角色升级
        liu_bei = char_manager.party[0]
        old_level = liu_bei.level
        liu_bei.gain_exp(100)
        
        if liu_bei.level > old_level:
            print(f"✓ 升级测试成功: {liu_bei.name} {old_level} → {liu_bei.level}")
        else:
            print(f"✓ 经验值增加: {liu_bei.name} 获得100经验")
        
        return True
        
    except Exception as e:
        print(f"✗ 角色系统测试失败: {e}")
        return False

def test_battle_system():
    """测试战斗系统"""
    print("\n=== 测试战斗系统 ===")
    
    try:
        from battle import BattleSystem, Enemy
        from character import CharacterManager
        from game_data import GameData
        
        game_data = GameData()
        char_manager = CharacterManager(game_data)
        battle_system = BattleSystem(char_manager)
        
        # 创建测试敌人
        enemies = [Enemy("测试敌人", 3, "soldier")]
        battle_data = {"enemies": enemies}
        
        # 开始战斗
        battle_system.start_battle(battle_data)
        print(f"✓ 战斗系统初始化成功")
        print(f"  敌人: {enemies[0].name} (HP: {enemies[0].current_hp})")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试三国游戏系统...\n")
    
    tests = [
        test_game_import,
        test_chinese_display,
        test_game_data,
        test_character_system,
        test_battle_system,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过! 游戏系统运行正常")
        print("💡 运行 'python 三国.py' 开始游戏")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()
