#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
吞食天地2 - 主游戏文件
基于FC经典游戏《吞食天地2》的Python重制版
"""

import pygame
import sys
import os
from enum import Enum
from typing import Dict, List, Optional

# 导入游戏模块
from game_state import GameState
from character import Character, <PERSON><PERSON><PERSON><PERSON>
from battle import BattleSystem
from map_system import WorldMap
from ui import UIManager
from game_data import GameData
from resources import ResourceManager

class GameMode(Enum):
    MENU = "menu"
    WORLD_MAP = "world_map"
    BATTLE = "battle"
    CITY = "city"
    DIALOGUE = "dialogue"
    INVENTORY = "inventory"

class SangokushiGame:
    """吞食天地2主游戏类"""
    
    def __init__(self):
        # 初始化pygame
        pygame.init()
        
        # 游戏配置
        self.SCREEN_WIDTH = 800
        self.SCREEN_HEIGHT = 600
        self.FPS = 60
        self.TITLE = "吞食天地2 - Sangokushi 2"
        
        # 创建游戏窗口
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption(self.TITLE)
        self.clock = pygame.time.Clock()
        
        # 游戏状态
        self.running = True
        self.current_mode = GameMode.MENU
        
        # 初始化游戏系统
        self.init_game_systems()
        
        # 输入处理
        self.keys_pressed = set()
        self.last_key_time = {}
        self.key_repeat_delay = 200  # 按键重复延迟(毫秒)
        
    def init_game_systems(self):
        """初始化游戏各个系统"""
        try:
            # 资源管理器
            self.resource_manager = ResourceManager()
            
            # 游戏数据
            self.game_data = GameData()
            
            # 游戏状态管理
            self.game_state = GameState()
            
            # 角色管理
            self.character_manager = CharacterManager(self.game_data)
            
            # 战斗系统
            self.battle_system = BattleSystem(self.character_manager)
            
            # 世界地图
            self.world_map = WorldMap(self.game_data)
            
            # UI管理器
            self.ui_manager = UIManager(self.screen, self.resource_manager)
            
            print("游戏系统初始化完成")
            
        except Exception as e:
            print(f"游戏系统初始化失败: {e}")
            self.running = False
    
    def handle_events(self):
        """处理游戏事件"""
        current_time = pygame.time.get_ticks()
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
                
            elif event.type == pygame.KEYDOWN:
                self.keys_pressed.add(event.key)
                self.last_key_time[event.key] = current_time
                self.handle_keydown(event.key)
                
            elif event.type == pygame.KEYUP:
                self.keys_pressed.discard(event.key)
                if event.key in self.last_key_time:
                    del self.last_key_time[event.key]
        
        # 处理按键重复
        for key in list(self.keys_pressed):
            if current_time - self.last_key_time.get(key, 0) > self.key_repeat_delay:
                self.handle_key_repeat(key)
                self.last_key_time[key] = current_time
    
    def handle_keydown(self, key):
        """处理按键按下事件"""
        if key == pygame.K_ESCAPE:
            if self.current_mode == GameMode.MENU:
                self.running = False
            else:
                self.current_mode = GameMode.MENU
                
        elif key == pygame.K_RETURN or key == pygame.K_SPACE:
            self.handle_confirm()
            
        elif key in [pygame.K_w, pygame.K_UP]:
            self.handle_move_up()
            
        elif key in [pygame.K_s, pygame.K_DOWN]:
            self.handle_move_down()
            
        elif key in [pygame.K_a, pygame.K_LEFT]:
            self.handle_move_left()
            
        elif key in [pygame.K_d, pygame.K_RIGHT]:
            self.handle_move_right()
    
    def handle_key_repeat(self, key):
        """处理按键重复事件"""
        if key in [pygame.K_w, pygame.K_UP, pygame.K_s, pygame.K_DOWN,
                   pygame.K_a, pygame.K_LEFT, pygame.K_d, pygame.K_RIGHT]:
            self.handle_keydown(key)
    
    def handle_confirm(self):
        """处理确认操作"""
        if self.current_mode == GameMode.MENU:
            self.ui_manager.handle_menu_confirm()
            
        elif self.current_mode == GameMode.WORLD_MAP:
            self.world_map.handle_confirm()
            
        elif self.current_mode == GameMode.BATTLE:
            self.battle_system.handle_confirm()
    
    def handle_move_up(self):
        """处理向上移动"""
        if self.current_mode == GameMode.MENU:
            self.ui_manager.menu_move_up()
            
        elif self.current_mode == GameMode.WORLD_MAP:
            self.world_map.move_player(0, -1)
            
    def handle_move_down(self):
        """处理向下移动"""
        if self.current_mode == GameMode.MENU:
            self.ui_manager.menu_move_down()
            
        elif self.current_mode == GameMode.WORLD_MAP:
            self.world_map.move_player(0, 1)
            
    def handle_move_left(self):
        """处理向左移动"""
        if self.current_mode == GameMode.MENU:
            self.ui_manager.menu_move_left()
            
        elif self.current_mode == GameMode.WORLD_MAP:
            self.world_map.move_player(-1, 0)
            
    def handle_move_right(self):
        """处理向右移动"""
        if self.current_mode == GameMode.MENU:
            self.ui_manager.menu_move_right()
            
        elif self.current_mode == GameMode.WORLD_MAP:
            self.world_map.move_player(1, 0)
    
    def update(self):
        """更新游戏逻辑"""
        if self.current_mode == GameMode.WORLD_MAP:
            self.world_map.update()
            
        elif self.current_mode == GameMode.BATTLE:
            self.battle_system.update()
            
        # 检查模式切换
        self.check_mode_transitions()
    
    def check_mode_transitions(self):
        """检查游戏模式切换"""
        # 从主菜单进入游戏
        if self.current_mode == GameMode.MENU and self.ui_manager.should_start_game():
            self.current_mode = GameMode.WORLD_MAP
            self.ui_manager.reset_menu_state()
            
        # 从世界地图进入战斗
        if self.current_mode == GameMode.WORLD_MAP and self.world_map.should_enter_battle():
            self.current_mode = GameMode.BATTLE
            self.battle_system.start_battle(self.world_map.get_battle_data())
            
        # 从战斗返回世界地图
        if self.current_mode == GameMode.BATTLE and self.battle_system.is_battle_finished():
            self.current_mode = GameMode.WORLD_MAP
            self.world_map.handle_battle_result(self.battle_system.get_battle_result())
    
    def render(self):
        """渲染游戏画面"""
        # 清空屏幕
        self.screen.fill((0, 0, 0))

        # 根据当前模式渲染不同内容
        if self.current_mode == GameMode.MENU:
            self.ui_manager.render_main_menu()

        elif self.current_mode == GameMode.WORLD_MAP:
            self.ui_manager.render_map(self.world_map)

        elif self.current_mode == GameMode.BATTLE:
            self.ui_manager.render_battle_screen(self.battle_system, self.character_manager)

        # 更新显示
        pygame.display.flip()
    
    def run(self):
        """主游戏循环"""
        print(f"启动{self.TITLE}")
        print("控制说明:")
        print("- WASD/方向键: 移动/选择")
        print("- 回车/空格: 确认")
        print("- ESC: 返回/退出")
        
        while self.running:
            # 处理事件
            self.handle_events()
            
            # 更新游戏逻辑
            self.update()
            
            # 渲染画面
            self.render()
            
            # 控制帧率
            self.clock.tick(self.FPS)
        
        # 退出游戏
        self.quit()
    
    def quit(self):
        """退出游戏"""
        print("感谢游玩吞食天地2!")
        pygame.quit()
        sys.exit()

def main():
    """主函数"""
    try:
        game = SangokushiGame()
        game.run()
    except Exception as e:
        print(f"游戏运行错误: {e}")
        pygame.quit()
        sys.exit(1)

if __name__ == "__main__":
    main()
