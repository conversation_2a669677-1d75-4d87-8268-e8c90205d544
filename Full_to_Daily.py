import os
import re
import traceback
from tkinter import filedialog, Tk
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement
from docx.oxml.ns import qn


def select_source_files():
    """选择多个原始Word文件对话框"""
    default_dir = os.path.join(os.getcwd(), "越南湿租报告", "日报（卿光宇）")
    if not os.path.exists(default_dir):
        default_dir = os.getcwd()

    root = Tk()
    root.withdraw()
    file_paths = filedialog.askopenfilenames(
        title="选择原始Word文件(可多选)",
        initialdir=default_dir,
        filetypes=[("Word文件", "*.docx"), ("所有文件", "*.*")]
    )
    return file_paths  # 返回文件路径列表


def get_template_path():
    """获取模板文件路径"""
    template_path = os.path.join(os.getcwd(), "Template", "C909越南运行QAR日报(模板).docx")
    if not os.path.exists(template_path):
        raise FileNotFoundError("找不到模板文件，请确认Template文件夹中存在'C909越南运行QAR日报(模板).docx'")
    return template_path


def extract_parameters(doc):
    """从word1第一页提取关键参数"""
    first_page_text = "\n".join([para.text for para in doc.paragraphs[:20]])

    parameters = {
        "month": extract_between(first_page_text, "年", "月"),
        "day": extract_between(first_page_text, "月", "日"),
        "AC_Num": extract_between(first_page_text, "本日", "架C909"),
        "Flight_Num": extract_between(first_page_text, "共执行", "架次越南"),
        "B_652G_Flt": extract_between(first_page_text, "B-652G飞机执行", "架次"),
        "B_656E_Flt": extract_between(first_page_text, "B-656E飞机执行", "架次"),
        "WQAR_Num": extract_between(first_page_text, "共收到", "段航班")
    }

    print("\n提取的参数值：")
    for key, value in parameters.items():
        print(f"{key}: {value}")

    return parameters


def extract_between(text, start, end):
    """从文本中提取两个字符串之间的内容"""
    pattern = re.compile(f"{re.escape(start)}(.*?){re.escape(end)}")
    match = pattern.search(text)
    return match.group(1).strip() if match else "未找到"


def create_wangpeng_version(source_path):
    """创建王总版文件word1"""
    doc = Document(source_path)
    date_part = os.path.basename(source_path).split('_')[-1].split('.')[0]

    for table in doc.tables[1:6]:
        for row in table.rows:
            for cell in row.cells[2:]:
                match = re.search(r'[(（](.*?)[）)]', cell.text)
                if match:
                    cell.text = match.group(1)
                    set_cell_format(cell)

    wp_dir = os.path.join(os.getcwd(), "越南湿租报告", "日报（王芃）")
    os.makedirs(wp_dir, exist_ok=True)
    wp_path = os.path.join(wp_dir, f"C909越南运行QAR日报_王总_{date_part}.docx")
    doc.save(wp_path)
    print(f"\n王总版文件已生成: {wp_path}")
    return wp_path, date_part, doc


def safe_set_font(run, font_name, font_size):
    """安全设置字体属性"""
    if not hasattr(run, 'font'):
        return

    try:
        run.font.name = font_name
        if hasattr(run, '_element'):
            rPr = run._element.get_or_add_rPr()
            rFonts = OxmlElement('w:rFonts')
            rFonts.set(qn('w:ascii'), font_name)
            rFonts.set(qn('w:eastAsia'), font_name)
            rFonts.set(qn('w:hAnsi'), font_name)
            rPr.append(rFonts)
        run.font.size = font_size
    except Exception as e:
        print(f"设置字体时出错: {str(e)}")


def set_cell_format(cell):
    """设置单元格格式"""
    for paragraph in cell.paragraphs:
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        for run in paragraph.runs:
            safe_set_font(run, '仿宋_GB2312', Pt(14))


def find_second_page_tables(doc):
    """查找第二页的两个ROM表格"""
    rom_section_start = None
    for i, para in enumerate(doc.paragraphs):
        if "三、昆岛起降ROM测量分析" in para.text:
            rom_section_start = i
            break

    if rom_section_start is None:
        print("未找到ROM分析章节，尝试使用默认表格")
        return doc.tables[1:3] if len(doc.tables) >= 3 else []

    rom_tables = []
    for table in doc.tables:
        first_cell_text = table.cell(0, 0).text if len(table.rows) > 0 and len(table.columns) > 0 else ""
        if ("起飞ROM测量分析" in first_cell_text or
                "着陆ROM测量分析" in first_cell_text):
            rom_tables.append(table)

        if len(rom_tables) >= 2:
            break

    return rom_tables


def transfer_column_data(src_table, dst_table, src_col, dst_col, start_row=1):
    """通用的列数据转移函数"""
    for row_offset in range(len(src_table.rows)):
        try:
            if (row_offset + start_row) < len(dst_table.rows):
                src_text = src_table.cell(row_offset, src_col).text
                dst_cell = dst_table.cell(row_offset + start_row, dst_col)
                dst_cell.text = src_text
                set_cell_format(dst_cell)
        except IndexError:
            break


def transfer_table_data(word1_path, word2_doc):
    """完整的表格数据处理（包含4个表格处理）"""
    try:
        word1_doc = Document(word1_path)

        # 检查表格数量
        print(f"\nWord1表格数量: {len(word1_doc.tables)}")
        print(f"Word2表格数量: {len(word2_doc.tables)}")

        # ============ 1. 处理第一个表格 ============
        if len(word1_doc.tables) > 0 and len(word2_doc.tables) > 0:
            print("\n[1/4] 处理第一个表格（word2表1第4列开始）...")
            src_table = word1_doc.tables[0]
            dst_table = word2_doc.tables[0]

            src_col = 3  # word1从第4列开始
            dst_col = 3  # word2从第4列开始

            while src_col < len(src_table.columns) and dst_col < len(dst_table.columns):
                try:
                    header_cell = src_table.cell(1, src_col)
                    if header_cell.text.startswith("VVCS"):
                        print(f"  word1表1第{src_col + 1}列 → word2表1第{dst_col + 1}列")
                        transfer_column_data(src_table, dst_table, src_col, dst_col)
                        dst_col += 1
                    src_col += 1
                except IndexError:
                    break

        # ============ 2. 处理第二个表格 ============
        if len(word1_doc.tables) > 1 and len(word2_doc.tables) > 0:
            print("\n[2/4] 处理第二个表格（word2表1第6列开始）...")
            src_table = word1_doc.tables[1]
            dst_table = word2_doc.tables[0]

            src_col = 3  # word1从第4列开始
            dst_col = 5  # word2从第6列开始

            while src_col < len(src_table.columns) and dst_col < len(dst_table.columns):
                try:
                    header_cell = src_table.cell(1, src_col)
                    if header_cell.text.startswith("VVCS"):
                        print(f"  word1表2第{src_col + 1}列 → word2表1第{dst_col + 1}列")
                        transfer_column_data(src_table, dst_table, src_col, dst_col)
                        dst_col += 1
                    src_col += 1
                except IndexError:
                    break

        # ============ 3. 处理第三个表格 ============
        if len(word1_doc.tables) > 2 and len(word2_doc.tables) > 1:
            print("\n[3/4] 处理第三个表格（word2表2第4列开始）...")
            src_table = word1_doc.tables[2]
            dst_table = word2_doc.tables[1]

            src_col = 0  # word1从第1列开始扫描
            dst_col = 3  # word2从第4列开始

            while src_col < len(src_table.columns) and dst_col < len(dst_table.columns):
                try:
                    header_cell = src_table.cell(1, src_col)
                    if header_cell.text.strip().endswith("VVCS"):
                        print(f"  word1表3第{src_col + 1}列 → word2表2第{dst_col + 1}列")
                        transfer_column_data(src_table, dst_table, src_col, dst_col)
                        dst_col += 1
                    src_col += 1
                except IndexError:
                    break

        # ============ 4. 处理第四个表格 ============
        if len(word1_doc.tables) > 3 and len(word2_doc.tables) > 1:
            print("\n[4/4] 处理第四个表格（word2表2第6列开始）...")
            src_table = word1_doc.tables[3]
            dst_table = word2_doc.tables[1]

            src_col = 0  # word1从第1列开始扫描
            dst_col = 5  # word2从第6列开始

            while src_col < len(src_table.columns) and dst_col < len(dst_table.columns):
                try:
                    header_cell = src_table.cell(1, src_col)
                    if header_cell.text.strip().endswith("VVCS"):
                        print(f"  word1表4第{src_col + 1}列 → word2表2第{dst_col + 1}列")
                        transfer_column_data(src_table, dst_table, src_col, dst_col)
                        dst_col += 1
                    src_col += 1
                except IndexError:
                    break

        # ============ 保留原有ROM表格处理 ============
        if len(word1_doc.tables) >= 2:
            print("\n处理ROM表格数据...")
            src_table = word1_doc.tables[1]
            target_tables = find_second_page_tables(word2_doc)

            if len(target_tables) >= 2:
                takeoff_table = None
                landing_table = None
                for table in target_tables:
                    first_cell = table.cell(0, 0).text if len(table.rows) > 0 and len(table.columns) > 0 else ""
                    if "起飞ROM测量分析" in first_cell:
                        takeoff_table = table
                    elif "着陆ROM测量分析" in first_cell:
                        landing_table = table

                if takeoff_table and landing_table:
                    for col in range(3, len(src_table.columns)):
                        try:
                            header_cell = src_table.cell(1, col)
                            if header_cell.text.startswith("VVCS"):
                                aircraft = "B-652G" if "652" in header_cell.text else "B-656E"
                                target_col = 3 if aircraft == "B-652G" else 4

                                for row_idx in range(2, len(src_table.rows)):
                                    param_name = src_table.cell(row_idx, 1).text.strip()
                                    param_value = src_table.cell(row_idx, col).text

                                    # 填充起飞表
                                    for row in takeoff_table.rows:
                                        if len(row.cells) > 1 and row.cells[1].text.strip() == param_name:
                                            if len(row.cells) > target_col:
                                                row.cells[target_col].text = param_value
                                                set_cell_format(row.cells[target_col])

                                    # 填充着陆表
                                    for row in landing_table.rows:
                                        if len(row.cells) > 1 and row.cells[1].text.strip() == param_name:
                                            if len(row.cells) > target_col:
                                                row.cells[target_col].text = param_value
                                                set_cell_format(row.cells[target_col])
                        except IndexError:
                            continue

    except Exception as e:
        print(f"表格处理出错: {str(e)}")
        traceback.print_exc()
        raise


def replace_parameters_in_doc(doc, parameters):
    """替换文档中的模板参数"""
    for para in doc.paragraphs:
        if not para.runs:
            continue

        original_text = para.text
        new_text = original_text

        for param, value in parameters.items():
            pattern = re.compile(r'[(（]' + re.escape(param) + r'[）)]')
            new_text = pattern.sub(value, new_text)

        if new_text != original_text:
            first_run = para.runs[0] if para.runs else None
            para.text = new_text
            if first_run:
                for run in para.runs:
                    safe_set_font(run, first_run.font.name, first_run.font.size)

    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                if not cell.paragraphs:
                    continue

                original_text = cell.text
                new_text = original_text

                for param, value in parameters.items():
                    pattern = re.compile(r'[(（]' + re.escape(param) + r'[）)]')
                    new_text = pattern.sub(value, new_text)

                if new_text != original_text:
                    first_para = cell.paragraphs[0]
                    first_run = first_para.runs[0] if first_para.runs else None
                    cell.text = new_text
                    if first_run:
                        for para in cell.paragraphs:
                            for run in para.runs:
                                safe_set_font(run, first_run.font.name, first_run.font.size)


def create_final_report(template_path, date_part, parameters, word1_path):
    """创建最终报告word2"""
    report_dir = os.path.join(os.getcwd(), "越南湿租报告", "日报")
    os.makedirs(report_dir, exist_ok=True)
    report_path = os.path.join(report_dir, f"C909越南运行QAR日报_{date_part}.docx")

    print(f"\n正在生成最终报告: {report_path}")
    doc = Document(template_path)

    # 参数替换
    replace_parameters_in_doc(doc, parameters)

    # 表格数据处理
    transfer_table_data(word1_path, doc)

    # 保存前验证
    print("\n文档验证:")
    print(f"段落数量: {len(doc.paragraphs)}")
    print(f"表格数量: {len(doc.tables)}")
    for i, table in enumerate(doc.tables):
        print(f"表格{i}: 行数={len(table.rows)}, 列数={len(table.columns)}")

    try:
        doc.save(report_path)
        print(f"\n最终报告生成成功: {report_path}")
    except PermissionError:
        temp_path = f"{report_path}_TEMP_{os.getpid()}.docx"
        doc.save(temp_path)
        print(f"\n警告：原文件被占用，已临时保存为: {temp_path}")
        report_path = temp_path

    return report_path


def main():
    """主函数"""
    try:
        print("=== C909日报生成程序开始运行 ===")

        # 1. 选择源文件word0(多选)
        print("\n[阶段1] 选择源文件(可多选)...")
        source_paths = select_source_files()
        if not source_paths:
            print("操作已取消")
            return

        # 获取模板路径(只需获取一次)
        print("\n[阶段4] 获取模板文件...")
        template_path = get_template_path()

        # 对每个选中的文件进行处理
        for i, source_path in enumerate(source_paths, 1):
            print(f"\n正在处理第 {i}/{len(source_paths)} 个文件: {source_path}")

            try:
                # 2. 生成王总版word1
                print("\n[阶段2] 生成王总版文件...")
                wp_path, date_part, wp_doc = create_wangpeng_version(source_path)

                # 3. 提取参数
                print("\n[阶段3] 提取文档参数...")
                parameters = extract_parameters(wp_doc)

                # 5. 生成最终报告word2
                print("\n[阶段5] 生成最终报告...")
                report_path = create_final_report(template_path, date_part, parameters, wp_path)

                # 输出当前文件处理结果
                print("\n=== 处理结果 ===")
                print(f"王总版文件: {wp_path} \n存在: {os.path.exists(wp_path)}")
                print(f"最终报告: {report_path} \n存在: {os.path.exists(report_path)}")
                print(f"\n文件 {i} 处理完成！")

            except Exception as e:
                print(f"\n!!! 文件 {source_path} 处理失败: {str(e)}")
                traceback.print_exc()
                continue  # 继续处理下一个文件

        print("\n所有文件处理完成！")

    except Exception as e:
        print(f"\n!!! 程序运行失败: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
