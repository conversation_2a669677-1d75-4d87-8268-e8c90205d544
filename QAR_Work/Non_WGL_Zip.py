import os
import re
import shutil
import time
import threading
import queue
import zipfile
from datetime import datetime, timedelta
import logging
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger()

# 常量定义
AIRCRAFT_PATTERN_FULL = r'[Bb]-[A-Za-z0-9]{4}'  # B-XXXX 格式
AIRCRAFT_PATTERN_SHORT = r'6[A-Za-z0-9]{3}'     # 6XXX 格式
# 日期匹配模式（支持多种格式）
DATE_PATTERNS = [
    r'(20\d{2})[-\/\.](\d{1,2})[-\/\.](\d{1,2})',  # yyyy-mm-dd, yyyy/mm/dd, yyyy.mm.dd
    r'(\d{1,2})[-\/\.](\d{1,2})[-\/\.](20\d{2})',  # mm-dd-yyyy, mm/dd/yyyy, mm.dd.yyyy
    r'(20\d{2})(\d{2})(\d{2})',                     # yyyymmdd
    r'(\d{2})(\d{2})(20\d{2})',                     # mmddyyyy
]

def extract_aircraft_info(folder_path):
    """从文件夹路径中提取飞机号信息"""
    path_parts = folder_path.split(os.sep)
    
    for part in reversed(path_parts):  # 从最深层开始查找
        if not part:
            continue
            
        # 首先查找完整的B-XXXX格式
        match = re.search(AIRCRAFT_PATTERN_FULL, part, re.I)
        if match:
            aircraft = match.group(0).upper()
            if not aircraft.startswith('B-'):
                aircraft = 'B-' + aircraft[2:]  # 确保是B-开头
            logger.info(f"    找到完整飞机号: {aircraft}")
            return aircraft
        
        # 然后查找6XXX格式
        match = re.search(AIRCRAFT_PATTERN_SHORT, part)
        if match:
            aircraft = f"B-{match.group(0).upper()}"
            logger.info(f"    找到6XXX格式飞机号，转换为: {aircraft}")
            return aircraft
    
    logger.info("    未找到飞机号")
    return None

def extract_date_info(folder_path):
    """从文件夹路径中提取日期信息"""
    path_parts = folder_path.split(os.sep)
    
    for part in reversed(path_parts):  # 从最深层开始查找
        if not part:
            continue
            
        for pattern in DATE_PATTERNS:
            match = re.search(pattern, part)
            if match:
                groups = match.groups()
                if len(groups) == 3:
                    # 根据模式确定年月日的位置
                    if len(groups[0]) == 4:  # yyyy开头
                        year, month, day = groups
                    elif len(groups[2]) == 4:  # yyyy结尾
                        month, day, year = groups
                    else:
                        continue
                    
                    # 格式化为标准日期格式
                    try:
                        month = month.zfill(2)
                        day = day.zfill(2)
                        date_str = f"{year}-{month}-{day}"
                        logger.info(f"    找到日期: {date_str}")
                        return date_str
                    except:
                        continue
    
    logger.info("    未找到日期")
    return None

def is_binary_line(line):
    """判断是否为二进制行"""
    try:
        # 检查是否包含大量非打印字符
        non_printable_count = sum(1 for c in line if ord(c) < 32 and c not in '\t\n\r')
        return non_printable_count > len(line) * 0.3
    except:
        return True

def extract_records_with_encoding(file_path, encoding):
    """使用指定编码提取记录 - 改进版本处理飞机号缺失的情况（完全参考PC_BAK.py）"""
    # 预编译正则表达式
    date_time_pattern = re.compile(r'DATE:\s*(\d{2})/(\d{2})/(\d{2})\s+TIME:\s*(\d{2}:\d{2}:\d{2})', re.I)
    ac_pattern = re.compile(r'A/C:\s*(B-[A-Z0-9]{4})', re.I)
    ac_empty_pattern = re.compile(r"A/C:\s*['\s]*", re.I)  # 匹配空的飞机号

    complete_records = []  # 有完整飞机号的记录
    time_only_records = []  # 只有时间没有飞机号的记录
    valid_aircraft_codes = set()  # 收集所有有效的飞机号

    line_count = 0
    current_record = {}

    with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
        for line in f:
            line_count += 1

            # 跳过二进制行
            if is_binary_line(line):
                continue

            line = line.strip()
            if not line:
                continue

            # 查找日期时间行
            date_time_match = date_time_pattern.search(line)
            if date_time_match:
                year, month, day, time = date_time_match.groups()
                current_record = {
                    'date': f"{year}/{month}/{day}",
                    'time': time,
                    'line_num': line_count,
                    'raw_line': line
                }
                continue

            # 查找飞机号行
            if current_record and 'ac' not in current_record:
                # 检查是否有完整的飞机号
                ac_match = ac_pattern.search(line)
                if ac_match:
                    aircraft_code = ac_match.group(1).upper()
                    current_record['ac'] = aircraft_code
                    valid_aircraft_codes.add(aircraft_code)

                    # 转换日期时间
                    dt = convert_to_datetime(
                        current_record['date'].split('/')[0],
                        current_record['date'].split('/')[1],
                        current_record['date'].split('/')[2],
                        current_record['time']
                    )

                    if dt:
                        current_record['datetime'] = dt
                        complete_records.append(current_record.copy())

                    current_record = {}
                    continue

                # 检查是否是空的飞机号行
                ac_empty_match = ac_empty_pattern.search(line)
                if ac_empty_match:
                    # 转换日期时间
                    dt = convert_to_datetime(
                        current_record['date'].split('/')[0],
                        current_record['date'].split('/')[1],
                        current_record['date'].split('/')[2],
                        current_record['time']
                    )

                    if dt:
                        current_record['datetime'] = dt
                        current_record['ac'] = None  # 标记为空飞机号
                        time_only_records.append(current_record.copy())

                    current_record = {}

    # 简化逻辑：保持原始记录状态，在最终处理时再组合
    logger.info(f"    完整记录: {len(complete_records)} 条")
    logger.info(f"    缺失飞机号记录: {len(time_only_records)} 条")

    # 合并所有记录，保持原始状态
    all_records = complete_records + time_only_records

    logger.info(f"    总记录: {len(all_records)} 条")

    return all_records

def convert_to_datetime(year, month, day, time_str):
    """将日期时间字符串转换为datetime对象"""
    try:
        year_int = int(year)
        # 修正年份转换逻辑：对于航班数据，69应该是1969年，不是2069年
        if year_int < 50:  # 00-49 认为是20xx年
            full_year = 2000 + year_int
        elif year_int < 100:  # 50-99 认为是19xx年
            full_year = 1900 + year_int
        else:
            full_year = year_int

        month = month.zfill(2)
        day = day.zfill(2)

        dt = datetime.strptime(f"{full_year}-{month}-{day} {time_str}", "%Y-%m-%d %H:%M:%S")
        return dt
    except Exception as e:
        return None

def convert_month_abbr_to_number(month_abbr):
    """将英文月份缩写转换为数字"""
    month_map = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
        'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
        'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
    }
    return month_map.get(month_abbr.upper(), None)

def parse_cc_format(file_path, encoding):
    """解析CC B-格式的记录
    格式示例: "CC B-30CQ MAR21 122040 ZUGY ZPJH 1807"
    对应: CC 飞机号 月份日期 时间 起飞机场 着陆机场 航班号后四位
    
    注意：MAR21表示3月21日，年份使用程序执行时的当前年份
    """
    try:
        # CC格式的正则表达式
        # CC B-30CQ MAR21 122040 ZUGY ZPJH 1807
        cc_pattern = re.compile(r'CC\s+(B-[A-Z0-9]{4})\s+([A-Z]{3})(\d{2})\s+(\d{6})\s+([A-Z]{4})\s+([A-Z]{4})\s+(\d{4})', re.I)
        
        records = []
        line_count = 0
        
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line in f:
                line_count += 1
                
                # 跳过二进制行
                if is_binary_line(line):
                    continue
                
                line = line.strip()
                if not line:
                    continue
                
                # 查找CC格式
                cc_match = cc_pattern.search(line)
                if cc_match:
                    aircraft, month_abbr, day_str, time_str, dep_airport, arr_airport, flight_suffix = cc_match.groups()
                    
                    # 转换月份
                    month_num = convert_month_abbr_to_number(month_abbr)
                    if not month_num:
                        continue
                    
                    # 解析日期：MAR21中的21是日期，不是年份
                    day_int = int(day_str)
                    if day_int < 1 or day_int > 31:
                        continue
                    
                    # 智能年份判断：航班数据不可能来自未来
                    current_date = datetime.now()
                    current_year = current_date.year
                    
                    # 先尝试使用当前年份构建日期
                    try:
                        test_date = datetime(current_year, int(month_num), day_int)
                        # 如果构建的日期比当前日期晚，说明应该是去年的数据
                        if test_date > current_date:
                            current_year = current_year - 1
                    except ValueError:
                        # 如果日期无效（如2月30日），使用当前年份
                        pass
                    
                    # 解析时间 (HHMMSS)
                    if len(time_str) == 6:
                        hour = time_str[:2]
                        minute = time_str[2:4]
                        second = time_str[4:6]
                        formatted_time = f"{hour}:{minute}:{second}"
                    else:
                        continue
                    
                    # 构建日期字符串（与原有格式保持一致：yy/mm/dd）
                    year_suffix = str(current_year)[-2:]  # 取年份后两位
                    date_str = f"{year_suffix}/{month_num}/{day_int:02d}"
                    
                    # 转换为datetime对象
                    try:
                        dt = datetime.strptime(f"{current_year}-{month_num}-{day_int:02d} {formatted_time}", "%Y-%m-%d %H:%M:%S")
                    except Exception as e:
                        continue
                    
                    record = {
                        'date': date_str,
                        'time': formatted_time,
                        'ac': aircraft.upper(),
                        'line_num': line_count,
                        'raw_line': line,
                        'datetime': dt,
                        'parse_method': 'cc_format',
                        'dep_airport': dep_airport,
                        'arr_airport': arr_airport,
                        'flight_suffix': flight_suffix,
                        'month_abbr': month_abbr,
                        'day_str': day_str
                    }
                    
                    records.append(record)
        
        return records
        
    except Exception as e:
        return []

def parse_mixed_format_file(file_path):
    """解析混合格式文件（文本+二进制）- 完全参考PC_BAK.py的实现"""
    try:
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)
        logger.info(f"  文件大小: {file_size_mb:.2f} MB")

        # 尝试多种编码方式
        encodings_to_try = ['utf-8', 'latin1', 'cp1252', 'utf-8-sig', 'ascii']

        best_records = []
        best_encoding = None

        for encoding in encodings_to_try:
            try:
                records = extract_records_with_encoding(file_path, encoding)
                if records and len(records) > len(best_records):
                    best_records = records
                    best_encoding = encoding
                    # 如果找到足够多的记录，就使用这个编码
                    if len(records) > 50:
                        break
            except Exception as e:
                continue

        # 检查是否需要启动第二种识别方式
        has_valid_aircraft = False
        has_valid_date = False

        if best_records:
            # 检查是否有有效的飞机号和日期信息
            for record in best_records:
                if record.get('ac') and record.get('ac') != 'None':
                    has_valid_aircraft = True
                if record.get('date'):
                    has_valid_date = True
                if has_valid_aircraft and has_valid_date:
                    break

        # 如果现有方法没有找到完整信息，启动第二种识别方式
        cc_records = []
        if not best_records or not (has_valid_aircraft and has_valid_date):
            logger.info(f"  现有方法未找到完整信息，启动CC B-关键字搜索...")
            try:
                cc_records = parse_cc_format(file_path, best_encoding or 'latin1')
                if cc_records:
                    logger.info(f"  CC格式解析找到 {len(cc_records)} 条记录")
                else:
                    logger.info(f"  CC格式解析未找到记录")
            except Exception as e:
                logger.warning(f"  CC格式解析失败: {str(e)}")

        # 合并两种方式的记录
        all_records = best_records + cc_records

        if not all_records:
            logger.warning(f"  两种解析方式都未找到任何有效记录")
            return None, 0

        logger.info(f"  总共找到 {len(all_records)} 条记录（原方式: {len(best_records)}, CC方式: {len(cc_records)}）")

        # 按时间排序
        sorted_records = sorted(all_records, key=lambda x: x['datetime'])

        # 找到绝对最新的记录
        latest_record = sorted_records[-1]

        # 如果最新记录的飞机号为空，需要组合
        if not latest_record.get('ac') or latest_record.get('ac') == 'None':
            # 找到最新的完整记录（有飞机号的）
            complete_records = [r for r in sorted_records if r.get('ac') and r.get('ac') != 'None']
            if complete_records:
                latest_complete_record = max(complete_records, key=lambda x: x['datetime'])
                latest_record['ac'] = latest_complete_record['ac']
                latest_record['ac_source'] = 'combined'
                logger.info(f"  组合逻辑: 最新时间记录飞机号为空，使用最新完整记录的飞机号: {latest_complete_record['ac']}")
                logger.info(f"  最新完整记录时间: {latest_complete_record['datetime']}")
            else:
                # 如果文件中完全没有完整飞机号，尝试使用文件夹中的飞机号
                logger.info(f"  文件中完全没有完整飞机号记录，将在处理函数中使用文件夹飞机号")

        # 显示最新的几条记录用于验证
        logger.info(f"  最新的3条记录:")
        for i, record in enumerate(sorted_records[-3:]):
            idx = len(sorted_records) - 3 + i + 1
            ac_info = record['ac'] if record.get('ac') else 'None'
            source_info = ""
            if record.get('ac_source') == 'combined':
                source_info = " (组合)"
            elif record.get('ac_source') == 'latest_complete':
                source_info = " (推断)"
            elif record.get('parse_method') == 'cc_format':
                source_info = " (CC格式)"
            logger.info(f"    {idx}: {record['datetime']} - DATE={record['date']} TIME={record['time']} A/C={ac_info}{source_info}")

        # 返回最新记录
        result = {k: v for k, v in latest_record.items() if k not in ['datetime', 'line_num', 'ac_source', 'raw_line', 'parse_method']}

        # 如果是组合的飞机号，添加标记
        if latest_record.get('ac_source') == 'combined':
            result['ac_combined'] = True

        return result, len(all_records)

    except Exception as e:
        logger.error(f"  文件解析异常: {str(e)}")
        import traceback
        logger.error(f"  详细错误: {traceback.format_exc()}")
        return None, 0

def convert_utc_to_beijing(date_str, time_str):
    """将UTC时间转换为北京时间"""
    try:
        if not date_str or not time_str:
            return None, None
        
        # 解析日期和时间
        if '/' in date_str:
            # 处理yy/mm/dd格式
            parts = date_str.split('/')
            if len(parts) == 3:
                year_int = int(parts[0])
                if year_int < 50:
                    year = 2000 + year_int
                else:
                    year = 1900 + year_int
                month = int(parts[1])
                day = int(parts[2])
                
                # 转换为标准格式
                beijing_date = f"{year}-{month:02d}-{day:02d}"
                beijing_time = time_str
                
                return beijing_date, beijing_time
        
        return date_str, time_str
    except Exception:
        return None, None

class ZipCompressorApp:
    def __init__(self, master):
        self.master = master
        master.title("Non_WGL压缩工具")
        master.geometry("950x850")
        master.configure(bg='#f0f0f0')
        master.resizable(True, True)

        # 初始化变量
        self.input_path = ""
        self.output_path = ""
        self.is_analyzing = False
        self.processed_files = 0
        self.total_files = 0
        self.analysis_results = []
        self.start_time = None
        self.current_file_start_time = None  # 当前文件开始时间

        # 自动压缩倒计时相关变量
        self.auto_compress_timer = None
        self.countdown_seconds = 300  # 5分钟倒计时
        self.countdown_active = False

        # 创建队列用于线程间通信
        self.data_queue = queue.Queue()
        self.progress_queue = queue.Queue()

        # 设置样式（参考PC_BAK.py）
        style = ttk.Style()
        style.theme_use('clam')  # 使用现代主题

        # 创建界面
        self.create_widgets()

        # 启动队列处理
        self.process_queues()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.master, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)  # 结果区域可扩展

        # 标题
        title_label = ttk.Label(main_frame, text="Non_WGL压缩工具",
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 路径选择区域
        self.create_path_selection(main_frame)

        # 按钮区域
        self.create_buttons(main_frame)

        # 进度显示区域
        self.create_progress_area(main_frame)

        # 结果显示区域
        self.create_results_area(main_frame)

    def create_path_selection(self, parent):
        """创建路径选择区域"""
        # 输入路径
        input_frame = ttk.LabelFrame(parent, text="输入路径", padding="10")
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)

        ttk.Label(input_frame, text="PC卡数据:", width=10).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.input_path_var = tk.StringVar(value="Z:\\PC卡")
        input_entry = ttk.Entry(input_frame, textvariable=self.input_path_var, width=60)
        input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(input_frame, text="浏览", command=self.select_input_path).grid(row=0, column=2)

        # 输出路径
        output_frame = ttk.LabelFrame(parent, text="输出路径", padding="10")
        output_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="ZIP文件:", width=10).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_path_var = tk.StringVar(value="E:\\ZIP")
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=60)
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="浏览", command=self.select_output_path).grid(row=0, column=2)

    def create_buttons(self, parent):
        """创建按钮区域（参考PC_BAK.py风格）"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=3, column=0, columnspan=3, pady=15)

        # MSG读取按钮
        self.msg_button = ttk.Button(button_frame, text="📖 MSG读取",
                                    command=self.start_msg_reading,
                                    style='Accent.TButton',
                                    width=15)
        self.msg_button.pack(side=tk.LEFT, padx=(0, 10))

        # 开始压缩按钮
        self.compress_button = ttk.Button(button_frame, text="🗜️ 开始压缩",
                                         command=self.start_compression,
                                         state='disabled',
                                         width=15)
        self.compress_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="⏹ 停止",
                                     command=self.stop_operation,
                                     state='disabled',
                                     width=12)
        self.stop_button.pack(side=tk.LEFT)

    def create_progress_area(self, parent):
        """创建进度显示区域"""
        progress_frame = ttk.Frame(parent, padding="15")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)

        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪 - 请选择输入和输出路径")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                font=('Microsoft YaHei', 10),
                                foreground='#2980b9')
        status_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky="ew", pady=(0, 5))

        # 详细进度信息
        self.progress_detail_var = tk.StringVar(value="")
        progress_detail_label = ttk.Label(progress_frame, textvariable=self.progress_detail_var,
                                         font=('Microsoft YaHei', 9),
                                         foreground='#7f8c8d')
        progress_detail_label.grid(row=2, column=0, sticky="w")

        # 统计信息区域
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.grid(row=3, column=0, sticky="ew", pady=(10, 0))
        stats_frame.columnconfigure(1, weight=1)

        # 左侧统计信息
        self.stats_var = tk.StringVar(value="统计信息: 等待开始")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var,
                               font=('Microsoft YaHei', 9),
                               foreground='#34495e')
        stats_label.grid(row=0, column=0, sticky="w")

        # 右侧时间信息
        self.time_stats_var = tk.StringVar(value="")
        time_stats_label = ttk.Label(stats_frame, textvariable=self.time_stats_var,
                                    font=('Microsoft YaHei', 9),
                                    foreground='#7f8c8d')
        time_stats_label.grid(row=0, column=1, sticky="e")

    def create_results_area(self, parent):
        """创建结果显示区域"""
        # 结果框架
        results_frame = ttk.LabelFrame(parent, text="分析结果", padding="10")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # 创建Treeview - 压缩工具的列设计
        columns = ('folder_name', 'folder_aircraft', 'folder_date',
                  'msg_aircraft', 'msg_date', 'total_records', 'date_diff',
                  'final_aircraft', 'final_date', 'compress_progress')

        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=20)

        # 定义列标题和宽度
        column_config = {
            'folder_name': ('文件夹名称', 110),
            'folder_aircraft': ('飞机号_文件夹', 65),
            'folder_date': ('日期_文件夹', 65),
            'msg_aircraft': ('飞机号_MSG', 65),
            'msg_date': ('日期_MSG', 65),
            'total_records': ('记录数', 45),
            'date_diff': ('日期差', 40),
            'final_aircraft': ('飞机号_最终', 65),
            'final_date': ('日期_最终', 65),
            'compress_progress': ('压缩进度', 60)
        }

        for col, (heading, width) in column_config.items():
            self.tree.heading(col, text=heading)
            # 文件夹名称列左对齐，其他列居中对齐
            if col == 'folder_name':
                self.tree.column(col, width=width, minwidth=50, anchor='w')
            else:
                self.tree.column(col, width=width, minwidth=40, anchor='center')

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(results_frame, orient="vertical", command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(results_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")

        # 配置行颜色
        self.tree.tag_configure('consistent', background='#e8f5e8', foreground='#2e7d32')
        self.tree.tag_configure('inconsistent', background='#ffebee', foreground='#c62828')
        self.tree.tag_configure('processing', background='#e3f2fd', foreground='#1565c0')

        # 底部统计信息显示框（参考PC_BAK.py）
        stats_frame = ttk.Frame(results_frame)
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        stats_frame.columnconfigure(1, weight=1)

        # 左侧统计信息
        self.bottom_stats_var = tk.StringVar(value="统计信息: 等待开始...")
        stats_label = ttk.Label(stats_frame, textvariable=self.bottom_stats_var,
                               font=('Microsoft YaHei', 9),
                               foreground='#34495e')
        stats_label.grid(row=0, column=0, sticky=tk.W)

        # 右侧时间信息
        self.bottom_time_stats_var = tk.StringVar(value="")
        time_stats_label = ttk.Label(stats_frame, textvariable=self.bottom_time_stats_var,
                                    font=('Microsoft YaHei', 9),
                                    foreground='#7f8c8d')
        time_stats_label.grid(row=0, column=1, sticky=tk.E)

    def select_input_path(self):
        """选择输入路径"""
        path = filedialog.askdirectory(title="选择PC卡数据文件夹")
        if path:
            self.input_path_var.set(path)

    def select_output_path(self):
        """选择输出路径"""
        path = filedialog.askdirectory(title="选择ZIP文件保存文件夹")
        if path:
            self.output_path_var.set(path)

    def start_msg_reading(self):
        """开始MSG读取"""
        if self.is_analyzing:
            return

        input_path = self.input_path_var.get().strip()
        if not input_path or not os.path.exists(input_path):
            messagebox.showerror("错误", "请选择有效的输入路径")
            return

        # 清空结果
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.analysis_results.clear()

        # 更新界面状态
        self.is_analyzing = True
        self.msg_button.configure(state="disabled")
        self.compress_button.configure(state="disabled")
        self.stop_button.configure(state="normal")

        self.status_var.set("正在扫描文件夹...")
        self.progress_bar['value'] = 0
        self.start_time = time.time()

        # 启动分析线程
        threading.Thread(target=self.analyze_folders, args=(input_path,), daemon=True).start()

    def start_compression(self):
        """开始压缩"""
        if not self.analysis_results:
            messagebox.showwarning("警告", "请先进行MSG读取")
            return

        output_path = self.output_path_var.get().strip()
        if not output_path:
            messagebox.showerror("错误", "请选择输出路径")
            return

        # 停止倒计时
        self.stop_countdown()

        # 更新界面状态
        self.is_analyzing = True  # 重要：设置为True以允许压缩过程继续
        self.compress_button.configure(state="disabled")
        self.stop_button.configure(state="normal")

        self.status_var.set("正在压缩文件...")

        # 启动压缩线程
        threading.Thread(target=self.compress_folders, args=(output_path,), daemon=True).start()

    def stop_operation(self):
        """停止操作"""
        self.is_analyzing = False
        self.status_var.set("正在停止...")
        self.stop_button.configure(state="disabled")
        # 停止倒计时
        self.stop_countdown()

    def start_countdown(self):
        """开始自动压缩倒计时"""
        self.countdown_seconds = 300  # 重置为5分钟
        self.countdown_active = True
        self.update_countdown()

    def stop_countdown(self):
        """停止倒计时"""
        self.countdown_active = False
        if self.auto_compress_timer:
            self.master.after_cancel(self.auto_compress_timer)
            self.auto_compress_timer = None

    def update_countdown(self):
        """更新倒计时显示"""
        if not self.countdown_active:
            return

        if self.countdown_seconds <= 0:
            # 倒计时结束，自动开始压缩
            self.countdown_active = False
            self.status_var.set("倒计时结束，自动开始压缩...")
            self.start_compression()
            return

        # 更新倒计时显示
        minutes = self.countdown_seconds // 60
        seconds = self.countdown_seconds % 60
        countdown_text = f"MSG读取完成！将在 {minutes:02d}:{seconds:02d} 后自动开始压缩（可手动点击'开始压缩'）"
        self.status_var.set(countdown_text)

        # 减少1秒
        self.countdown_seconds -= 1

        # 1秒后再次更新
        self.auto_compress_timer = self.master.after(1000, self.update_countdown)

    def analyze_folders(self, input_path):
        """分析文件夹（在后台线程中运行）"""
        try:
            # 扫描所有包含MSG.DAT的文件夹
            folders_to_process = []
            for root, dirs, files in os.walk(input_path):
                if 'MSG.DAT' in files:
                    folders_to_process.append(root)

            self.total_files = len(folders_to_process)
            if self.total_files == 0:
                self.progress_queue.put(("status", "未找到包含MSG.DAT的文件夹"))
                return

            self.progress_queue.put(("status", f"开始分析 {self.total_files} 个文件夹"))
            self.progress_queue.put(("progress_max", self.total_files))

            successful_count = 0
            for index, folder_path in enumerate(folders_to_process):
                if not self.is_analyzing:
                    break

                folder_name = os.path.basename(folder_path)
                self.processed_files = index

                # 记录当前文件开始时间
                self.current_file_start_time = time.time()

                self.progress_queue.put(("status", f"正在分析: {folder_name} ({index + 1}/{self.total_files})"))

                # 先添加初始行显示正在处理（参考PC_BAK.py）
                initial_data = {
                    'folder_path': folder_path,
                    'folder_name': folder_name,
                    'folder_aircraft': extract_aircraft_info(folder_path) or '',
                    'folder_date': extract_date_info(folder_path) or '',
                    'msg_aircraft': '分析中...',
                    'msg_date': '',
                    'total_records': '',
                    'date_diff': '',
                    'final_aircraft': '',
                    'final_date': '',
                    'compress_progress': ''
                }
                self.data_queue.put(("add_row", initial_data))

                # 分析文件夹
                result = self.process_single_folder(folder_path, index + 1, self.total_files)

                if result:
                    self.data_queue.put(("update_row", (index, result)))
                    self.analysis_results.append(result)
                    successful_count += 1
                else:
                    # 处理失败的情况
                    logger.warning(f"文件夹处理失败: {folder_name}")
                    failed_data = initial_data.copy()
                    failed_data.update({
                        'msg_aircraft': '处理失败',
                        'final_aircraft': '处理失败',
                        'compress_progress': ''
                    })
                    self.data_queue.put(("update_row", (index, failed_data)))

                self.processed_files = index + 1
                self.progress_queue.put(("update_time", None))
                self.progress_queue.put(("progress", index + 1))

            if self.is_analyzing:
                self.progress_queue.put(("status", f"MSG读取完成！成功分析 {successful_count}/{self.total_files} 个文件夹"))
                self.progress_queue.put(("enable_compress", True))
                # 启动自动压缩倒计时
                self.progress_queue.put(("start_countdown", None))
            else:
                self.progress_queue.put(("status", "MSG读取已停止"))

        except Exception as e:
            logger.error(f"分析过程异常: {str(e)}")
            self.progress_queue.put(("status", f"分析过程异常: {str(e)}"))
        finally:
            self.progress_queue.put(("analysis_complete", None))

    def process_single_folder(self, folder_path, file_index, total_files):
        """处理单个文件夹（基于PC_BAK.py的逻辑）"""
        try:
            folder_name = os.path.basename(folder_path)

            # 从文件夹路径提取信息
            folder_aircraft = extract_aircraft_info(folder_path)
            folder_date = extract_date_info(folder_path)

            # 解析MSG.DAT文件
            msg_dat_path = os.path.join(folder_path, 'MSG.DAT')
            if not os.path.exists(msg_dat_path):
                logger.warning(f"  MSG.DAT文件不存在: {msg_dat_path}")
                return None

            # 解析MSG.DAT文件
            record, total_records = parse_mixed_format_file(msg_dat_path)

            # 简单的进度更新
            self.data_queue.put(("update_progress", (file_index, "100%")))

            if record:
                msg_aircraft = record.get('ac')
                msg_date = record.get('date')
                msg_time = record.get('time')

                # 转换MSG日期格式为标准格式
                beijing_date, beijing_time = convert_utc_to_beijing(msg_date, msg_time)

                # 确定最终使用的飞机号和日期
                final_aircraft = msg_aircraft if msg_aircraft else folder_aircraft
                final_date = beijing_date if beijing_date else folder_date

                # 计算日期差异
                date_diff = None
                if folder_date and beijing_date:
                    try:
                        folder_dt = datetime.strptime(folder_date, '%Y-%m-%d')
                        msg_dt = datetime.strptime(beijing_date, '%Y-%m-%d')
                        date_diff = abs((folder_dt - msg_dt).days)
                    except:
                        pass

                # 判断数据一致性
                status = "信息不足"
                if folder_aircraft and msg_aircraft and folder_date and beijing_date:
                    if folder_aircraft == msg_aircraft and (date_diff is None or date_diff <= 7):
                        status = "一致"
                    else:
                        status = "不一致"

                result = {
                    'folder_path': folder_path,
                    'folder_name': folder_name,
                    'folder_aircraft': folder_aircraft or '',
                    'folder_date': folder_date or '',
                    'msg_aircraft': msg_aircraft or '',
                    'msg_date': beijing_date or '',
                    'msg_time': beijing_time or '',
                    'total_records': total_records,
                    'date_diff': date_diff,
                    'final_aircraft': final_aircraft or '',
                    'final_date': final_date or '',
                    'status': status,
                    'compress_progress': ''
                }

                return result
            else:
                logger.warning(f"  MSG.DAT文件解析失败: {msg_dat_path}")
                return None

        except Exception as e:
            logger.error(f"处理文件夹异常: {folder_path}, 错误: {str(e)}")
            return None

    def compress_folders(self, output_path):
        """压缩文件夹（完整的三阶段处理流程）"""
        try:
            # 阶段1: 准备工作
            self.progress_queue.put(("status", "=== 阶段1: 准备压缩任务 ==="))
            if not self.prepare_compression_tasks(output_path):
                return

            # 阶段2: 复制文件到临时文件夹
            self.progress_queue.put(("status", "=== 阶段2: 复制文件到临时文件夹 ==="))
            if not self.copy_files_to_temp():
                return

            # 阶段3: 压缩文件
            self.progress_queue.put(("status", "=== 阶段3: 压缩文件 ==="))
            if not self.compress_temp_folders():
                return

            # 清理临时文件夹
            self.cleanup_temp_folder()

            if self.is_analyzing:
                successful_count = len([task for task in self.compress_tasks if task.get('success', False)])
                self.progress_queue.put(("status", f"全部完成! 共成功处理 {successful_count}/{len(self.compress_tasks)} 个文件夹"))
            else:
                self.progress_queue.put(("status", "压缩已停止"))

        except Exception as e:
            logger.error(f"压缩过程异常: {str(e)}")
            self.progress_queue.put(("status", f"压缩过程异常: {str(e)}"))
        finally:
            self.cleanup_temp_folder()
            self.progress_queue.put(("compress_complete", None))

    def prepare_compression_tasks(self, output_path):
        """准备压缩任务"""
        try:
            # 创建输出文件夹
            if not os.path.exists(output_path):
                os.makedirs(output_path)

            # 创建临时文件夹
            self.temp_path = os.path.join(output_path, "temp_compress")
            if os.path.exists(self.temp_path):
                shutil.rmtree(self.temp_path)
            os.makedirs(self.temp_path, exist_ok=True)

            # 准备压缩任务
            self.compress_tasks = []
            for result in self.analysis_results:
                final_aircraft = result.get('final_aircraft', '')
                final_date = result.get('final_date', '')

                # 生成文件夹名称（参考原始逻辑）
                if final_aircraft.startswith('B-'):
                    aircraft_code = final_aircraft[2:]
                else:
                    aircraft_code = final_aircraft[-4:] if final_aircraft else 'UNKN'

                date_formatted = final_date.replace('-', '') if final_date else 'UNKNOWN'
                new_folder_name = f"{aircraft_code}{date_formatted}"

                temp_folder = os.path.join(self.temp_path, new_folder_name)
                zip_filename = f"{new_folder_name}.zip"
                zip_path = os.path.join(output_path, zip_filename)

                task = {
                    'source_folder': result['folder_path'],
                    'temp_folder': temp_folder,
                    'new_folder_name': new_folder_name,
                    'folder_name': result['folder_name'],
                    'zip_filename': zip_filename,
                    'zip_path': zip_path
                }
                self.compress_tasks.append(task)

            logger.info(f"准备了 {len(self.compress_tasks)} 个压缩任务")
            return True

        except Exception as e:
            logger.error(f"准备压缩任务失败: {str(e)}")
            return False

    def copy_files_to_temp(self):
        """复制文件到临时文件夹"""
        try:
            successful_copies = 0
            total_tasks = len(self.compress_tasks)

            self.progress_queue.put(("progress_max", total_tasks))

            for index, task in enumerate(self.compress_tasks):
                if not self.is_analyzing:
                    logger.warning(f"复制阶段被中断: is_analyzing = {self.is_analyzing}")
                    break

                logger.info(f"开始复制任务 {index + 1}/{total_tasks}: {task['folder_name']}")
                logger.info(f"源文件夹: {task['source_folder']}")
                logger.info(f"目标文件夹: {task['temp_folder']}")

                self.progress_queue.put(("status", f"复制文件 {index + 1}/{total_tasks} - {task['folder_name']}"))
                self.data_queue.put(("update_compress_progress", (index, "复制中...")))

                try:
                    # 创建临时文件夹
                    os.makedirs(task['temp_folder'], exist_ok=True)
                    logger.info(f"创建临时文件夹: {task['temp_folder']}")

                    # 复制文件（只复制DAT、QAR、QA2文件和特定文件夹）
                    copied_count = 0
                    source_folder = task['source_folder']

                    if not os.path.exists(source_folder):
                        logger.error(f"源文件夹不存在: {source_folder}")
                        continue

                    logger.info(f"开始遍历源文件夹: {source_folder}")
                    for root, dirs, files in os.walk(source_folder):
                        logger.info(f"检查目录: {root}, 文件数: {len(files)}, 文件夹数: {len(dirs)}")

                        # 复制符合条件的文件
                        for file in files:
                            if file.upper().endswith(('.DAT', '.QAR', '.QA2')):
                                source_file = os.path.join(root, file)
                                rel_path = os.path.relpath(source_file, source_folder)
                                dest_file = os.path.join(task['temp_folder'], rel_path)

                                logger.info(f"复制文件: {file} -> {rel_path}")

                                # 创建目标目录
                                dest_dir = os.path.dirname(dest_file)
                                os.makedirs(dest_dir, exist_ok=True)

                                # 复制文件
                                shutil.copy2(source_file, dest_file)
                                copied_count += 1
                                logger.info(f"文件复制成功: {dest_file}")

                        # 复制符合条件的文件夹
                        for dir_name in dirs:
                            if dir_name.upper().endswith(('.REP', '.REC', '.QAR')):
                                source_dir = os.path.join(root, dir_name)
                                rel_path = os.path.relpath(source_dir, source_folder)
                                dest_dir = os.path.join(task['temp_folder'], rel_path)

                                logger.info(f"复制文件夹: {dir_name} -> {rel_path}")

                                # 复制整个文件夹
                                shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)
                                copied_count += 1
                                logger.info(f"文件夹复制成功: {dest_dir}")

                    if copied_count > 0:
                        self.data_queue.put(("update_compress_progress", (index, "复制完成")))
                        successful_copies += 1
                        logger.info(f"复制完成: {task['folder_name']} ({copied_count} 个文件/文件夹)")
                    else:
                        self.data_queue.put(("update_compress_progress", (index, "无文件")))
                        logger.warning(f"无符合条件的文件: {task['folder_name']}")

                except Exception as e:
                    logger.error(f"复制失败: {task['folder_name']}, 错误: {str(e)}")
                    self.data_queue.put(("update_compress_progress", (index, "复制失败")))

                self.progress_queue.put(("progress", index + 1))

            logger.info(f"阶段2完成: 成功复制 {successful_copies}/{total_tasks} 个文件夹")
            return successful_copies > 0

        except Exception as e:
            logger.error(f"复制阶段失败: {str(e)}")
            return False

    def compress_temp_folders(self):
        """压缩临时文件夹"""
        try:
            successful_compressions = 0
            total_tasks = len(self.compress_tasks)

            self.progress_queue.put(("progress_max", total_tasks))

            for index, task in enumerate(self.compress_tasks):
                if not self.is_analyzing:
                    break

                self.progress_queue.put(("status", f"压缩文件 {index + 1}/{total_tasks} - {task['new_folder_name']}"))
                self.data_queue.put(("update_compress_progress", (index, "压缩中...")))

                try:
                    # 检查临时文件夹是否存在且有内容
                    if not os.path.exists(task['temp_folder']) or not os.listdir(task['temp_folder']):
                        self.data_queue.put(("update_compress_progress", (index, "无内容")))
                        continue

                    # 创建ZIP文件
                    with zipfile.ZipFile(task['zip_path'], 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for root, dirs, files in os.walk(task['temp_folder']):
                            for file in files:
                                file_path = os.path.join(root, file)
                                # 计算相对路径（相对于临时文件夹）
                                arcname = os.path.relpath(file_path, task['temp_folder'])
                                zipf.write(file_path, arcname)

                    # 检查ZIP文件是否创建成功
                    if os.path.exists(task['zip_path']) and os.path.getsize(task['zip_path']) > 0:
                        self.data_queue.put(("update_compress_progress", (index, "完成")))
                        task['success'] = True
                        successful_compressions += 1
                        logger.info(f"压缩完成: {task['zip_filename']}")
                    else:
                        self.data_queue.put(("update_compress_progress", (index, "失败")))
                        logger.error(f"压缩失败: {task['zip_filename']} - ZIP文件无效")

                except Exception as e:
                    logger.error(f"压缩失败: {task['new_folder_name']}, 错误: {str(e)}")
                    self.data_queue.put(("update_compress_progress", (index, "失败")))

                self.progress_queue.put(("progress", index + 1))

            logger.info(f"阶段3完成: 成功压缩 {successful_compressions}/{total_tasks} 个文件夹")
            return successful_compressions > 0

        except Exception as e:
            logger.error(f"压缩阶段失败: {str(e)}")
            return False

    def cleanup_temp_folder(self):
        """清理临时文件夹"""
        try:
            if hasattr(self, 'temp_path') and os.path.exists(self.temp_path):
                shutil.rmtree(self.temp_path)
                logger.info(f"清理临时文件夹: {self.temp_path}")
        except Exception as e:
            logger.warning(f"清理临时文件夹失败: {str(e)}")

    def add_tree_row(self, data):
        """添加新行到表格"""
        values = (
            data.get('folder_name', ''),
            data.get('folder_aircraft', ''),
            data.get('folder_date', ''),
            data.get('msg_aircraft', ''),
            data.get('msg_date', ''),
            data.get('total_records', ''),
            data.get('date_diff', ''),
            data.get('final_aircraft', ''),
            data.get('final_date', ''),
            data.get('compress_progress', '')
        )

        item = self.tree.insert('', 'end', values=values)

        # 设置行颜色
        status = data.get('status', '')
        if status == '不一致':
            self.tree.item(item, tags=('inconsistent',))
        elif status == '一致':
            self.tree.item(item, tags=('consistent',))

        # 滚动到最新添加的行
        self.tree.see(item)

    def update_tree_row(self, row_index, data):
        """更新树形视图中的行"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]

            values = (
                data.get('folder_name', ''),
                data.get('folder_aircraft', ''),
                data.get('folder_date', ''),
                data.get('msg_aircraft', ''),
                data.get('msg_date', ''),
                data.get('total_records', ''),
                data.get('date_diff', ''),
                data.get('final_aircraft', ''),
                data.get('final_date', ''),
                data.get('compress_progress', '')
            )

            self.tree.item(item, values=values)

            # 设置行颜色
            status = data.get('status', '')
            if status == '不一致':
                self.tree.item(item, tags=('inconsistent',))
            elif status == '一致':
                self.tree.item(item, tags=('consistent',))

    def update_progress_in_row(self, row_index, progress_text):
        """更新行中的进度显示"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]
            # 只更新MSG飞机号列显示进度
            current_values = list(self.tree.item(item, 'values'))
            current_values[3] = progress_text  # msg_aircraft列
            self.tree.item(item, values=current_values)

    def update_compress_progress_in_row(self, row_index, progress_text):
        """更新行中的压缩进度显示"""
        children = self.tree.get_children()
        if row_index < len(children):
            item = children[row_index]
            # 更新压缩进度列显示进度
            current_values = list(self.tree.item(item, 'values'))
            current_values[9] = progress_text  # compress_progress列
            self.tree.item(item, values=current_values)

    def process_queues(self):
        """处理队列中的消息"""
        # 处理数据队列
        while True:
            try:
                msg_type, data = self.data_queue.get_nowait()
                if msg_type == "add_row":
                    self.add_tree_row(data)
                elif msg_type == "update_row":
                    row_index, row_data = data
                    self.update_tree_row(row_index, row_data)
                elif msg_type == "update_progress":
                    row_index, progress_text = data
                    self.update_progress_in_row(row_index, progress_text)
                elif msg_type == "update_compress_progress":
                    row_index, progress_text = data
                    self.update_compress_progress_in_row(row_index, progress_text)
            except queue.Empty:
                break

        # 处理进度队列
        while True:
            try:
                msg_type, data = self.progress_queue.get_nowait()
                if msg_type == "status":
                    self.status_var.set(data)
                elif msg_type == "progress":
                    self.progress_bar['value'] = data
                    if hasattr(self, 'total_files') and self.total_files > 0:
                        percentage = (data / self.total_files) * 100
                        self.progress_detail_var.set(f"进度: {data}/{self.total_files} ({percentage:.1f}%)")
                elif msg_type == "progress_max":
                    self.progress_bar['maximum'] = data
                elif msg_type == "enable_compress":
                    self.compress_button.configure(state="normal")
                elif msg_type == "analysis_complete":
                    self.msg_button.configure(state="normal")
                    self.stop_button.configure(state="disabled")
                    self.is_analyzing = False
                elif msg_type == "compress_complete":
                    self.compress_button.configure(state="normal")
                    self.stop_button.configure(state="disabled")
                    self.is_analyzing = False
                    self.stop_countdown()  # 压缩完成时停止倒计时
                elif msg_type == "start_countdown":
                    self.start_countdown()
                elif msg_type == "update_time":
                    self.update_time_display()
            except queue.Empty:
                break

        # 每100ms检查一次队列
        self.master.after(100, self.process_queues)

    def update_time_statistics(self):
        """更新时间统计信息（参考PC_BAK.py）"""
        if not self.start_time:
            return

        current_time = time.time()
        total_elapsed = current_time - self.start_time

        # 当前文件已用时间
        current_file_elapsed = 0
        if self.current_file_start_time:
            current_file_elapsed = current_time - self.current_file_start_time

        # 计算预估剩余时间
        remaining_time = 0
        if self.processed_files > 0 and self.total_files > 0:
            avg_time_per_file = total_elapsed / self.processed_files
            remaining_files = self.total_files - self.processed_files
            remaining_time = avg_time_per_file * remaining_files

        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.0f}秒"
            elif seconds < 3600:
                minutes = seconds / 60
                return f"{minutes:.1f}分"
            else:
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                return f"{hours:.0f}时{minutes:.0f}分"

        # 更新底部时间显示
        time_text = f"总用时: {format_time(total_elapsed)} | 预估剩余: {format_time(remaining_time)} | 当前文件: {format_time(current_file_elapsed)}"
        self.bottom_time_stats_var.set(time_text)

        # 更新底部统计信息
        if hasattr(self, 'processed_files') and hasattr(self, 'total_files'):
            stats_text = f"统计信息: 已处理 {self.processed_files}/{self.total_files} 个文件夹"
            self.bottom_stats_var.set(stats_text)

    def update_time_display(self):
        """更新时间显示"""
        if self.start_time:
            elapsed_seconds = time.time() - self.start_time
            elapsed_str = f"{int(elapsed_seconds // 3600):02d}:{int((elapsed_seconds % 3600) // 60):02d}:{int(elapsed_seconds % 60):02d}"

            # 计算剩余时间
            if self.processed_files > 0 and self.total_files > 0:
                remaining_seconds = (elapsed_seconds / self.processed_files) * (self.total_files - self.processed_files)
                remaining_str = f"{int(remaining_seconds // 3600):02d}:{int((remaining_seconds % 3600) // 60):02d}:{int(remaining_seconds % 60):02d}"
            else:
                remaining_str = "计算中..."

            self.time_stats_var.set(f"已用时间: {elapsed_str} | 剩余时间: {remaining_str}")

            # 更新统计信息
            if hasattr(self, 'processed_files') and hasattr(self, 'total_files'):
                self.stats_var.set(f"统计信息: 已处理 {self.processed_files}/{self.total_files} 个文件夹")

            # 更新底部详细统计
            self.update_time_statistics()

def start_gui():
    """启动GUI应用"""
    root = tk.Tk()
    app = ZipCompressorApp(root)
    root.mainloop()

if __name__ == "__main__":
    start_gui()
