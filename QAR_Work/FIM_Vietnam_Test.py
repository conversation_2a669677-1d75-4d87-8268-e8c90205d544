import os
import re
import shutil
import time
import zipfile
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fim_vietnam_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 测试用路径（请根据实际情况修改）
MON_PATH = r"D:\test_vietnam"  # 测试用监控路径
DATA_BAK_PATH = r"D:\test_data_bak"  # 测试用备份路径
AIRFASE_PATH = r"D:\test_airfase"  # 测试用AirFASE路径

# 目标飞机号
TARGET_AIRCRAFT = ["B-652G", "B-656E"]

# 需要保留的文件扩展名
KEEP_EXTENSIONS = ['.DAT', '.QAR', '.QA2']

def test_single_run():
    """测试单次运行（不使用文件监控）"""
    logger.info("="*60)
    logger.info("FIM Vietnam 测试版本启动")
    logger.info(f"监控路径: {MON_PATH}")
    logger.info(f"DATA_BAK路径: {DATA_BAK_PATH}")
    logger.info(f"AirFASE路径: {AIRFASE_PATH}")
    logger.info("="*60)
    
    # 创建测试目录
    os.makedirs(MON_PATH, exist_ok=True)
    os.makedirs(DATA_BAK_PATH, exist_ok=True)
    os.makedirs(AIRFASE_PATH, exist_ok=True)
    
    # 检查监控路径是否存在
    if not os.path.exists(MON_PATH):
        logger.error(f"监控路径不存在: {MON_PATH}")
        return
    
    logger.info("开始处理现有数据...")
    
    # 扫描并处理有效文件夹
    valid_folders = []
    
    # 扫描所有包含DAR.DAT的文件夹
    for root, dirs, files in os.walk(MON_PATH):
        if 'DAR.DAT' in files:
            logger.info(f"发现有效文件夹: {root}")
            valid_folders.append(root)
    
    logger.info(f"共发现 {len(valid_folders)} 个有效文件夹")
    
    if len(valid_folders) == 0:
        logger.info("没有发现包含DAR.DAT的文件夹")
        logger.info("测试提示：请在测试目录中创建包含DAR.DAT文件的文件夹进行测试")
        return
    
    # 处理每个有效文件夹
    for folder_path in valid_folders:
        process_test_folder(folder_path)
    
    logger.info("测试处理完成")

def process_test_folder(folder_path):
    """处理测试文件夹"""
    try:
        logger.info(f"开始处理文件夹: {folder_path}")
        
        # 简化的飞机号提取（仅用于测试）
        folder_name = os.path.basename(folder_path)
        aircraft = None
        
        # 查找B-XXXX格式的飞机号
        match = re.search(r'[Bb]-[A-Za-z0-9]{4}', folder_name, re.I)
        if match:
            aircraft = match.group(0).upper()
            if not aircraft.startswith('B-'):
                aircraft = 'B-' + aircraft[2:]
        
        logger.info(f"提取的飞机号: {aircraft}")
        
        # 检查是否为目标飞机
        if aircraft not in TARGET_AIRCRAFT:
            logger.info(f"飞机号 {aircraft} 不在目标列表中，跳过处理")
            return
        
        logger.info(f"飞机号 {aircraft} 在目标列表中，继续处理")
        
        # 使用当前日期作为测试日期
        test_date = datetime.now().strftime('%Y-%m-%d')
        logger.info(f"使用测试日期: {test_date}")
        
        # 测试复制到DATA_BAK
        copy_to_data_bak_test(folder_path, aircraft, test_date)
        
        # 测试重命名并复制到AirFASE
        rename_and_copy_to_airfase_test(folder_path, aircraft, test_date)
        
        logger.info(f"文件夹处理完成: {folder_path}")
        
    except Exception as e:
        logger.error(f"处理文件夹失败: {folder_path}, 错误: {str(e)}")

def copy_to_data_bak_test(folder_path, aircraft, date_str):
    """测试复制到DATA_BAK目录"""
    try:
        logger.info(f"开始复制到DATA_BAK: {aircraft} {date_str}")
        
        # 解析年份和月日
        year = date_str.split('-')[0]
        month_day = date_str.replace('-', '')[4:]  # 去掉年份和横线，得到mmdd
        
        # 构建目标路径
        target_base = os.path.join(DATA_BAK_PATH, year, aircraft)
        target_folder = os.path.join(target_base, f"{year}-{month_day}")
        
        # 创建目标目录
        os.makedirs(target_folder, exist_ok=True)
        
        # 复制文件夹内容（仅复制DAT/QAR/QA2文件）
        copy_folder_contents_test(folder_path, target_folder)
        
        logger.info(f"复制到DATA_BAK完成: {target_folder}")
        
    except Exception as e:
        logger.error(f"复制到DATA_BAK失败: {folder_path}, 错误: {str(e)}")

def rename_and_copy_to_airfase_test(folder_path, aircraft, date_str):
    """测试重命名文件夹并复制到AirFASE目录"""
    try:
        logger.info(f"开始重命名并复制到AirFASE: {aircraft} {date_str}")
        
        # 构建新的文件夹名称
        date_formatted = date_str.replace('-', '')  # YYYYMMDD
        new_folder_name = f"{aircraft}_{date_formatted}0000.PC"
        
        # 获取父目录
        parent_dir = os.path.dirname(folder_path)
        new_folder_path = os.path.join(parent_dir, new_folder_name)
        
        # 创建临时复制（测试模式不实际重命名原文件夹）
        temp_folder = os.path.join(parent_dir, f"temp_{new_folder_name}")
        
        if os.path.exists(temp_folder):
            shutil.rmtree(temp_folder)
        
        shutil.copytree(folder_path, temp_folder)
        logger.info(f"创建临时文件夹: {temp_folder}")
        
        # 构建AirFASE目标路径
        airfase_target = os.path.join(AIRFASE_PATH, aircraft)
        os.makedirs(airfase_target, exist_ok=True)
        
        final_target = os.path.join(airfase_target, new_folder_name)
        
        # 复制到AirFASE目录
        if os.path.exists(final_target):
            shutil.rmtree(final_target)
        
        shutil.copytree(temp_folder, final_target)
        logger.info(f"复制到AirFASE完成: {final_target}")
        
        # 删除临时文件夹
        shutil.rmtree(temp_folder)
        logger.info(f"删除临时文件夹: {temp_folder}")
        
        logger.info("注意：测试模式不会删除原文件夹")
        
    except Exception as e:
        logger.error(f"重命名并复制到AirFASE失败: {folder_path}, 错误: {str(e)}")

def copy_folder_contents_test(source_folder, target_folder):
    """测试复制文件夹内容"""
    try:
        for item in os.listdir(source_folder):
            source_item = os.path.join(source_folder, item)
            target_item = os.path.join(target_folder, item)
            
            if os.path.isfile(source_item):
                # 检查文件扩展名
                ext = os.path.splitext(item)[1].upper()
                if ext in KEEP_EXTENSIONS:
                    if not os.path.exists(target_item):
                        shutil.copy2(source_item, target_item)
                        logger.info(f"复制文件: {item}")
                    else:
                        logger.info(f"跳过已存在的文件: {item}")
                else:
                    logger.info(f"跳过不需要的文件: {item}")
            
            elif os.path.isdir(source_item):
                # 简化处理：复制所有子文件夹
                if not os.path.exists(target_item):
                    shutil.copytree(source_item, target_item)
                    logger.info(f"复制文件夹: {item}")
                else:
                    logger.info(f"跳过已存在的文件夹: {item}")
    
    except Exception as e:
        logger.error(f"复制文件夹内容失败: {source_folder} -> {target_folder}, 错误: {str(e)}")

if __name__ == "__main__":
    print("FIM Vietnam 测试版本")
    print("="*50)
    print(f"测试监控路径: {MON_PATH}")
    print(f"测试备份路径: {DATA_BAK_PATH}")
    print(f"测试AirFASE路径: {AIRFASE_PATH}")
    print("="*50)
    print()
    
    # 提示用户创建测试数据
    print("测试准备：")
    print(f"1. 请在 {MON_PATH} 目录下创建测试文件夹")
    print("2. 文件夹名称应包含飞机号（如：B-652G_20250117）")
    print("3. 在文件夹中创建 DAR.DAT 文件（可以是空文件）")
    print("4. 可以添加一些 .DAT、.QAR、.QA2 文件进行测试")
    print()
    
    input("准备完成后按回车键开始测试...")
    
    test_single_run()
    
    print()
    print("测试完成！请检查以下目录的结果：")
    print(f"- 备份目录: {DATA_BAK_PATH}")
    print(f"- AirFASE目录: {AIRFASE_PATH}")
    print(f"- 日志文件: fim_vietnam_test.log")
