
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional)
missing module named org - imported by pickle (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), importlib._bootstrap_external (conditional), shutil (conditional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), pathlib (delayed, optional), shutil (delayed, optional), tarfile (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
missing module named grp - imported by pathlib (delayed, optional), shutil (delayed, optional), tarfile (optional), subprocess (delayed, conditional, optional)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named fcntl - imported by subprocess (optional)
