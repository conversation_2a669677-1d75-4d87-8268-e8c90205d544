# FIM Vietnam 自动化监控脚本

## 功能描述

这是一个自动化脚本，用于持续监控"Z:\Vietnam\PC卡"目录，当检测到新文件或文件夹时，自动进行以下处理：

1. **解压处理**: 自动解压新增的压缩文件（支持ZIP、RAR、7Z格式）
2. **数据分析**: 使用PC_BAK.py的方法论提取飞机号和日期信息
3. **筛选处理**: 只处理B-652G和B-656E的数据
4. **文件清理**: 删除不需要的文件，只保留DAT/QAR/QA2文件和特定文件夹
5. **数据复制**: 复制到两个不同的目标位置
6. **清理监控**: 删除无效文件夹

## 安装依赖

### Python包依赖

运行以下命令安装所需的Python包：

```bash
pip install watchdog rarfile py7zr
```

或者直接运行提供的批处理文件：
```bash
install_dependencies.bat
```

### RAR文件解压支持

脚本支持多种RAR解压方式，按优先级排序：

1. **WinRAR（推荐）**
   - 下载地址：https://www.winrar.com/
   - 安装后会自动检测以下路径：
     - `C:\Program Files\WinRAR\WinRAR.exe`
     - `C:\Program Files (x86)\WinRAR\WinRAR.exe`

2. **7-Zip（免费推荐）**
   - 下载地址：https://www.7-zip.org/
   - 安装后会自动检测以下路径：
     - `C:\Program Files\7-Zip\7z.exe`
     - `C:\Program Files (x86)\7-Zip\7z.exe`

3. **rarfile库 + unrar工具**
   - 需要额外配置unrar程序
   - 适用于高级用户

### 测试RAR解压功能

运行以下脚本测试RAR解压是否正常工作：

```bash
python test_rar_extract.py
```

## 配置说明

脚本中的关键路径配置：

- **MON_PATH**: `Z:\Vietnam\PC卡` - 监控路径
- **DATA_BAK_PATH**: `Z:\DATA_BAK\FDIMU_PC` - 数据备份路径
- **AIRFASE_PATH**: `D:\AirFASE\FIMRoot\ARJ21` - AirFASE系统路径

目标飞机号：
- B-652G
- B-656E

## 使用方法

1. 确保所有依赖已安装
2. 确保监控路径和目标路径存在且有访问权限
3. 运行脚本：

```bash
python FIM_Vietnam.py
```

## 处理流程

### 1. 压缩文件处理
- 扫描监控目录下的所有压缩文件
- 解压到以原文件名命名的文件夹
- 删除原压缩文件

### 2. 数据分析
- 扫描包含DAR.DAT文件的文件夹
- 从文件夹路径提取飞机号和日期信息
- 解析MSG.DAT文件获取飞机号和日期
- 综合两种信息确定最终的飞机号和日期

### 3. 数据筛选
- 只处理飞机号为B-652G或B-656E的数据
- 其他飞机号的数据跳过处理

### 4. 文件清理
- 删除除DAT、QAR、QA2以外的所有文件
- 保留后缀为.REP、.REC、.QAR的文件夹
- 保留包含DAR.DAT文件的文件夹

### 5. 数据复制
- 复制到`Z:\DATA_BAK\FDIMU_PC\YYYY\B-XXXX\YYYY-MMDD`
- 重命名为`B-XXXX_YYYYMMDD0000.PC`格式
- 复制到`D:\AirFASE\FIMRoot\ARJ21\B-XXXX`
- 删除原文件夹

### 6. 清理监控
- 删除不包含DAR.DAT文件的文件夹
- 如果监控目录完全没有有效数据，清空所有内容

## 日志记录

脚本会生成详细的日志记录：
- 控制台输出：实时显示处理状态
- 日志文件：`fim_vietnam.log`，记录所有操作详情

## 注意事项

1. **权限要求**: 确保脚本有读写监控目录和目标目录的权限
2. **路径存在**: 确保所有配置的路径都存在
3. **磁盘空间**: 确保目标路径有足够的磁盘空间
4. **RAR支持**: 如需解压RAR文件，需要安装WinRAR或7-Zip
5. **持续运行**: 脚本设计为持续运行，使用Ctrl+C停止

## 错误处理

脚本包含完善的错误处理机制：
- 文件访问错误会记录但不会中断整个流程
- 解压失败会跳过该文件继续处理其他文件
- 网络路径不可用时会等待重试

## 停止脚本

使用 `Ctrl+C` 组合键可以安全停止脚本运行。
