"""
高质量图标创建工具
用于创建清晰的ICO格式图标文件
"""

import os
from PIL import Image, ImageDraw, ImageFilter
import math

def create_sun_icon(color, size=256, filename=None):
    """创建太阳图案图标"""
    # 创建透明背景
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    
    # 根据颜色设置渐变
    if color == 'gold':
        colors = [(255, 215, 0), (255, 165, 0), (255, 140, 0)]  # 金色渐变
    elif color == 'green':
        colors = [(50, 205, 50), (34, 139, 34), (0, 128, 0)]   # 绿色渐变
    elif color == 'red':
        colors = [(255, 69, 0), (255, 0, 0), (139, 0, 0)]      # 红色渐变
    else:
        colors = [(255, 215, 0), (255, 165, 0), (255, 140, 0)]  # 默认金色
    
    # 绘制外圈光芒
    ray_count = 16
    outer_radius = size * 0.45
    inner_radius = size * 0.35
    
    for i in range(ray_count):
        angle = (2 * math.pi * i) / ray_count
        
        # 计算光芒的点
        x1 = center + inner_radius * math.cos(angle)
        y1 = center + inner_radius * math.sin(angle)
        x2 = center + outer_radius * math.cos(angle)
        y2 = center + outer_radius * math.sin(angle)
        
        # 绘制光芒
        ray_width = size // 20
        draw.line([(x1, y1), (x2, y2)], fill=colors[0], width=ray_width)
    
    # 绘制主圆盘（渐变效果）
    main_radius = size * 0.3
    
    # 创建渐变圆盘
    for r in range(int(main_radius), 0, -2):
        alpha = int(255 * (r / main_radius))
        color_index = int((main_radius - r) / main_radius * (len(colors) - 1))
        color_index = min(color_index, len(colors) - 1)
        
        current_color = colors[color_index] + (alpha,)
        
        draw.ellipse([
            center - r, center - r,
            center + r, center + r
        ], fill=current_color)
    
    # 绘制内圈装饰
    inner_circle_radius = size * 0.15
    draw.ellipse([
        center - inner_circle_radius, center - inner_circle_radius,
        center + inner_circle_radius, center + inner_circle_radius
    ], fill=colors[2])
    
    # 绘制中心点
    center_radius = size * 0.05
    draw.ellipse([
        center - center_radius, center - center_radius,
        center + center_radius, center + center_radius
    ], fill=(255, 255, 255))
    
    # 应用轻微的模糊效果使边缘更平滑
    img = img.filter(ImageFilter.SMOOTH)
    
    if filename:
        img.save(filename, 'PNG')
    
    return img

def png_to_ico(png_path, ico_path, sizes=[16, 32, 48, 64, 128, 256]):
    """将PNG转换为多尺寸ICO文件"""
    try:
        # 打开PNG图像
        img = Image.open(png_path)
        
        # 创建不同尺寸的图像列表
        images = []
        for size in sizes:
            resized = img.resize((size, size), Image.Resampling.LANCZOS)
            images.append(resized)
        
        # 保存为ICO文件
        images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
        print(f"✅ 成功创建ICO文件: {ico_path}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False

def create_all_icons():
    """创建所有需要的图标"""
    print("🎨 开始创建高质量图标...")
    
    # 创建高分辨率PNG图标
    icons = [
        ('gold', 'app_hd.png'),
        ('green', 'app_green_hd.png'),
        ('red', 'app_red_hd.png')
    ]
    
    for color, filename in icons:
        print(f"创建 {filename}...")
        create_sun_icon(color, size=256, filename=filename)
    
    # 转换为ICO格式
    ico_conversions = [
        ('app_hd.png', 'app_hd.ico'),
        ('app_green_hd.png', 'app_green_hd.ico'),
        ('app_red_hd.png', 'app_red_hd.ico')
    ]
    
    print("\n🔄 转换为ICO格式...")
    for png_file, ico_file in ico_conversions:
        if os.path.exists(png_file):
            png_to_ico(png_file, ico_file)
    
    # 也转换现有的PNG文件
    existing_pngs = ['app_green.png', 'app_red.png']
    for png_file in existing_pngs:
        if os.path.exists(png_file):
            ico_file = png_file.replace('.png', '.ico')
            png_to_ico(png_file, ico_file)
    
    print("\n✅ 所有图标创建完成！")
    print("\n📁 生成的文件：")
    print("- app_hd.png / app_hd.ico          (高清主图标)")
    print("- app_green_hd.png / app_green_hd.ico  (高清绿色图标)")
    print("- app_red_hd.png / app_red_hd.ico      (高清红色图标)")
    print("- app_green.ico                    (原绿色图标转换)")
    print("- app_red.ico                      (原红色图标转换)")

if __name__ == "__main__":
    # 检查PIL库
    try:
        from PIL import Image, ImageDraw, ImageFilter
        create_all_icons()
    except ImportError:
        print("❌ 需要安装Pillow库")
        print("运行: pip install Pillow")
        input("按回车键退出...")
