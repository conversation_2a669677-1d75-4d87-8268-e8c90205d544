"""
FIM Vietnam 主窗口GUI界面
功能丰富、美观的监控界面，支持系统托盘
"""

import os
import sys
import json
import time
import threading
import subprocess
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox
import pystray
from PIL import Image, ImageTk
from pathlib import Path

class FIMVietnamGUI:
    """FIM Vietnam 主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_styles()
        self.create_widgets()
        self.setup_tray()
        
        # 启动监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind('<Unmap>', self.on_minimize)
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("FIM Vietnam 监控中心")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置图标
        try:
            if os.path.exists("app_hd.ico"):
                self.root.iconbitmap("app_hd.ico")
            elif os.path.exists("app.ico"):
                self.root.iconbitmap("app.ico")
        except:
            pass
        
        # 设置背景色
        self.root.configure(bg='#f0f2f5')
    
    def setup_variables(self):
        """设置变量"""
        self.service_running = tk.BooleanVar(value=False)
        self.current_task = tk.StringVar(value="待机中")
        self.uptime = tk.StringVar(value="00:00:00")
        self.processed_files = tk.IntVar(value=0)
        self.processed_folders = tk.IntVar(value=0)
        self.error_count = tk.IntVar(value=0)
        self.last_activity = tk.StringVar(value="无")
        
        # 状态文件路径
        self.status_file = "status.json"
        self.service_exe = "FIM_Vietnam_Service.exe"
        
        # 托盘相关
        self.tray_icon = None
        self.is_minimized_to_tray = False
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'), 
                       background='#f0f2f5', foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'),
                       background='#f0f2f5', foreground='#34495e')
        style.configure('Status.TLabel', font=('Microsoft YaHei', 10),
                       background='#f0f2f5', foreground='#7f8c8d')
        style.configure('Value.TLabel', font=('Microsoft YaHei', 11, 'bold'),
                       background='#f0f2f5', foreground='#2980b9')
        
        # 按钮样式
        style.configure('Action.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(20, 10))
        style.configure('Success.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(15, 8))
        style.configure('Danger.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(15, 8))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.create_header(main_frame)
        
        # 状态卡片区域
        self.create_status_cards(main_frame)
        
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        
        # 详细信息区域
        self.create_details_section(main_frame)
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(header_frame, text="🛡️ FIM Vietnam 监控中心", 
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # 状态指示器
        self.status_indicator = ttk.Label(header_frame, text="●", 
                                         font=('Microsoft YaHei', 20),
                                         foreground='#e74c3c')
        self.status_indicator.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态文本
        self.status_text = ttk.Label(header_frame, text="服务已停止", 
                                    style='Header.TLabel')
        self.status_text.pack(side=tk.RIGHT)
    
    def create_status_cards(self, parent):
        """创建状态卡片"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 配置网格
        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)
        
        # 运行时间卡片
        self.create_card(cards_frame, "⏱️ 运行时间", self.uptime, 0, 0)
        
        # 处理统计卡片 - 创建动态标签
        self.stats_card = self.create_card(cards_frame, "📊 处理统计", "", 0, 1)
        self.stats_label = ttk.Label(self.stats_card, text="文件: 0 | 文件夹: 0",
                                    style='Value.TLabel')
        self.stats_label.pack()
        
        # 错误统计卡片
        self.create_card(cards_frame, "⚠️ 错误统计", self.error_count, 0, 2)
        
        # 当前任务卡片
        self.create_card(cards_frame, "🔄 当前任务", self.current_task, 1, 0, columnspan=2)
        
        # 最后活动卡片
        self.create_card(cards_frame, "📅 最后活动", self.last_activity, 1, 2)
    
    def create_card(self, parent, title, value_var, row, col, columnspan=1):
        """创建单个状态卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="15")
        card_frame.grid(row=row, column=col, columnspan=columnspan, 
                       sticky="ew", padx=5, pady=5)
        
        if isinstance(value_var, tk.StringVar) or isinstance(value_var, tk.IntVar):
            value_label = ttk.Label(card_frame, textvariable=value_var, 
                                   style='Value.TLabel')
        else:
            value_label = ttk.Label(card_frame, text=str(value_var), 
                                   style='Value.TLabel')
        value_label.pack()
        
        return card_frame
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 启动服务按钮
        self.start_btn = ttk.Button(button_frame, text="🚀 启动服务", 
                                   command=self.start_service,
                                   style='Success.TButton')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止服务按钮
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止服务", 
                                  command=self.stop_service,
                                  style='Danger.TButton')
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重启服务按钮
        self.restart_btn = ttk.Button(button_frame, text="🔄 重启服务", 
                                     command=self.restart_service,
                                     style='Action.TButton')
        self.restart_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看日志按钮
        self.log_btn = ttk.Button(button_frame, text="📋 查看日志", 
                                 command=self.view_logs,
                                 style='Action.TButton')
        self.log_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开目录按钮
        self.folder_btn = ttk.Button(button_frame, text="📁 打开目录", 
                                    command=self.open_folder,
                                    style='Action.TButton')
        self.folder_btn.pack(side=tk.RIGHT)
    
    def create_details_section(self, parent):
        """创建详细信息区域"""
        details_frame = ttk.LabelFrame(parent, text="📈 实时监控", padding="15")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本显示区域
        self.details_text = tk.Text(details_frame, height=10, wrap=tk.WORD,
                                   font=('Consolas', 9), bg='#2c3e50', fg='#ecf0f1',
                                   insertbackground='#ecf0f1')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, 
                                 command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始信息
        self.add_log_message("FIM Vietnam 监控中心已启动")
        self.add_log_message("等待服务状态更新...")
    
    def add_log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.details_text.insert(tk.END, formatted_message)
        self.details_text.see(tk.END)
        
        # 限制行数
        lines = self.details_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.details_text.delete("1.0", "10.0")
    
    def setup_tray(self):
        """设置系统托盘"""
        try:
            # 加载托盘图标
            if os.path.exists("app_red_hd.ico"):
                icon_path = "app_red_hd.ico"
            elif os.path.exists("app_red.ico"):
                icon_path = "app_red.ico"
            else:
                icon_path = "app.ico"
            
            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示主窗口", self.show_window),
                pystray.MenuItem("启动服务", self.start_service),
                pystray.MenuItem("停止服务", self.stop_service),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("退出程序", self.quit_application)
            )
            
            # 创建托盘图标
            image = Image.open(icon_path)
            self.tray_icon = pystray.Icon("FIM_Vietnam", image, 
                                         "FIM Vietnam 监控", menu)
            
        except Exception as e:
            print(f"托盘设置失败: {str(e)}")
            self.tray_icon = None

    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self.update_status()
                time.sleep(2)  # 每2秒更新一次
            except Exception as e:
                print(f"监控循环错误: {str(e)}")
                time.sleep(5)

    def update_status(self):
        """更新状态信息"""
        try:
            # 检查服务进程
            is_running = self.check_service_running()

            # 读取状态文件
            status_data = self.read_status_file()

            # 更新界面
            self.root.after(0, self.update_ui, is_running, status_data)

        except Exception as e:
            print(f"更新状态失败: {str(e)}")

    def check_service_running(self):
        """检查服务是否运行"""
        try:
            result = subprocess.run(['tasklist', '/fi', f'imagename eq {self.service_exe}'],
                                  capture_output=True, text=True)
            return self.service_exe in result.stdout
        except:
            return False

    def read_status_file(self):
        """读取状态文件"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return {}

    def update_ui(self, is_running, status_data):
        """更新UI界面"""
        # 更新服务状态
        self.service_running.set(is_running)

        if is_running:
            self.status_indicator.configure(foreground='#27ae60')
            self.status_text.configure(text="服务运行中")
            self.update_tray_icon('green')
        else:
            self.status_indicator.configure(foreground='#e74c3c')
            self.status_text.configure(text="服务已停止")
            self.update_tray_icon('red')

        # 更新按钮状态
        if is_running:
            self.start_btn.configure(state='disabled')
            self.stop_btn.configure(state='normal')
        else:
            self.start_btn.configure(state='normal')
            self.stop_btn.configure(state='disabled')

        # 更新状态数据
        if status_data:
            # 运行时间
            if 'start_time' in status_data:
                start_time = datetime.fromisoformat(status_data['start_time'])
                uptime_delta = datetime.now() - start_time
                uptime_str = str(uptime_delta).split('.')[0]  # 去掉微秒
                self.uptime.set(uptime_str)

            # 处理统计
            files = status_data.get('processed_files', 0)
            folders = status_data.get('processed_folders', 0)
            self.processed_files.set(files)
            self.processed_folders.set(folders)
            self.stats_label.configure(text=f"文件: {files} | 文件夹: {folders}")

            # 错误统计
            self.error_count.set(status_data.get('errors', 0))

            # 当前任务
            self.current_task.set(status_data.get('current_action', '待机中'))

            # 最后活动
            if 'last_activity' in status_data:
                last_time = datetime.fromisoformat(status_data['last_activity'])
                time_diff = datetime.now() - last_time
                if time_diff.total_seconds() < 60:
                    self.last_activity.set("刚刚")
                elif time_diff.total_seconds() < 3600:
                    minutes = int(time_diff.total_seconds() // 60)
                    self.last_activity.set(f"{minutes}分钟前")
                else:
                    hours = int(time_diff.total_seconds() // 3600)
                    self.last_activity.set(f"{hours}小时前")

        # 更新托盘提示
        if self.tray_icon:
            tooltip = f"FIM Vietnam - {'运行中' if is_running else '已停止'}"
            if status_data:
                tooltip += f" | 文件: {status_data.get('processed_files', 0)}"
            self.tray_icon.title = tooltip

    def update_tray_icon(self, status):
        """更新托盘图标"""
        if not self.tray_icon:
            return

        try:
            if status == 'green':
                icon_path = "app_green_hd.ico" if os.path.exists("app_green_hd.ico") else "app_green.ico"
            else:
                icon_path = "app_red_hd.ico" if os.path.exists("app_red_hd.ico") else "app_red.ico"

            if os.path.exists(icon_path):
                image = Image.open(icon_path)
                self.tray_icon.icon = image
        except Exception as e:
            print(f"更新托盘图标失败: {str(e)}")

    def start_service(self):
        """启动服务"""
        try:
            if os.path.exists(self.service_exe):
                subprocess.Popen([self.service_exe],
                               creationflags=subprocess.CREATE_NO_WINDOW)
                self.add_log_message("正在启动服务...")
            else:
                self.add_log_message("错误: 找不到服务程序")
                messagebox.showerror("错误", f"找不到服务程序: {self.service_exe}")
        except Exception as e:
            self.add_log_message(f"启动服务失败: {str(e)}")
            messagebox.showerror("错误", f"启动服务失败: {str(e)}")

    def stop_service(self):
        """停止服务"""
        try:
            subprocess.run(['taskkill', '/f', '/im', self.service_exe],
                          capture_output=True)
            self.add_log_message("服务已停止")
        except Exception as e:
            self.add_log_message(f"停止服务失败: {str(e)}")
            messagebox.showerror("错误", f"停止服务失败: {str(e)}")

    def restart_service(self):
        """重启服务"""
        self.add_log_message("正在重启服务...")
        self.stop_service()
        time.sleep(2)
        self.start_service()

    def view_logs(self):
        """查看日志"""
        try:
            log_dir = "logs"
            if os.path.exists(log_dir):
                os.startfile(log_dir)
            else:
                messagebox.showinfo("提示", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开日志目录失败: {str(e)}")

    def open_folder(self):
        """打开程序目录"""
        try:
            os.startfile(".")
        except Exception as e:
            messagebox.showerror("错误", f"打开目录失败: {str(e)}")

    def on_minimize(self, event):
        """窗口最小化事件"""
        if event.widget == self.root:
            # 最小化到任务栏，显示简要状态
            self.root.title(f"FIM Vietnam - {'运行中' if self.service_running.get() else '已停止'}")

    def on_closing(self):
        """窗口关闭事件"""
        # 最小化到系统托盘
        if self.tray_icon:
            self.root.withdraw()
            self.is_minimized_to_tray = True
            self.start_tray()
            self.add_log_message("程序已最小化到系统托盘")
        else:
            self.quit_application()

    def start_tray(self):
        """启动系统托盘"""
        if self.tray_icon and not self.is_minimized_to_tray:
            threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.is_minimized_to_tray = False
        if self.tray_icon:
            self.tray_icon.stop()

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        self.monitoring = False
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    # 检查依赖
    try:
        import pystray
        from PIL import Image, ImageTk
    except ImportError as e:
        print(f"缺少依赖库: {str(e)}")
        print("请运行: pip install pystray pillow")
        input("按回车键退出...")
        return

    # 启动GUI
    app = FIMVietnamGUI()
    app.run()

if __name__ == "__main__":
    main()
