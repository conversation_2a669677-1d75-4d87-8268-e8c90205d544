@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    FIM Vietnam 自动化监控服务
echo ========================================
echo.
echo 请选择运行模式：
echo.
echo 1. GUI界面模式 (推荐)
echo 2. 命令行模式 (无界面)
echo 3. 纯服务模式 (后台运行)
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 启动GUI界面模式...
    python FIM_Vietnam_WithGUI.py
) else if "%choice%"=="2" (
    echo.
    echo 启动命令行模式...
    python FIM_Vietnam_WithGUI.py --no-gui
) else if "%choice%"=="3" (
    echo.
    echo 启动纯服务模式...
    python FIM_Vietnam_WithGUI.py --service-only
) else if "%choice%"=="4" (
    echo.
    echo 退出程序
    exit /b 0
) else (
    echo.
    echo 无效选择，请重新运行脚本
    pause
    exit /b 1
)

pause
