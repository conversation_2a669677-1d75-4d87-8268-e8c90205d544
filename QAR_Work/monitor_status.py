"""
FIM Vietnam 状态监控工具
用于查看服务运行状态、处理进度和日志信息
"""

import os
import json
import time
import glob
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class FIMStatusMonitor:
    """FIM状态监控器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FIM Vietnam 状态监控")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        self.status_file = "status.json"
        self.log_dir = "logs"
        
        self.create_widgets()
        self.update_status()
        
        # 自动刷新
        self.auto_refresh = True
        self.refresh_interval = 5000  # 5秒
        self.schedule_refresh()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FIM Vietnam 监控面板", 
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="服务状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态标签
        self.status_labels = {}
        status_items = [
            ("服务状态", "service_status"),
            ("运行时间", "uptime"),
            ("最后活动", "last_activity"),
            ("处理文件数", "processed_files"),
            ("处理文件夹数", "processed_folders"),
            ("错误次数", "errors"),
            ("当前操作", "current_action")
        ]
        
        for i, (label_text, key) in enumerate(status_items):
            ttk.Label(status_frame, text=f"{label_text}:", 
                     font=('Microsoft YaHei', 10)).grid(row=i//2, column=(i%2)*2, sticky=tk.W, padx=(0, 10), pady=2)
            
            self.status_labels[key] = ttk.Label(status_frame, text="--", 
                                              font=('Microsoft YaHei', 10, 'bold'),
                                              foreground='#2980b9')
            self.status_labels[key].grid(row=i//2, column=(i%2)*2+1, sticky=tk.W, padx=(0, 20), pady=2)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="🔄 刷新状态", 
                  command=self.update_status).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📊 查看今日日志", 
                  command=self.show_today_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📈 查看进度日志", 
                  command=self.show_progress_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="❌ 查看错误日志", 
                  command=self.show_error_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动刷新开关
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="自动刷新", 
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack(side=tk.RIGHT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="实时日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=100,
                                                 font=('Consolas', 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 读取状态文件
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    status = json.load(f)
                
                # 更新状态标签
                self.status_labels["service_status"].config(text="🟢 运行中", foreground='green')
                
                # 计算运行时间
                start_time = datetime.fromisoformat(status['start_time'])
                uptime = datetime.now() - start_time
                uptime_str = f"{int(uptime.total_seconds() // 3600)}小时 {int((uptime.total_seconds() % 3600) // 60)}分钟"
                self.status_labels["uptime"].config(text=uptime_str)
                
                # 最后活动时间
                last_activity = datetime.fromisoformat(status['last_activity'])
                time_diff = datetime.now() - last_activity
                if time_diff.total_seconds() < 60:
                    last_activity_str = "刚刚"
                elif time_diff.total_seconds() < 3600:
                    last_activity_str = f"{int(time_diff.total_seconds() // 60)}分钟前"
                else:
                    last_activity_str = f"{int(time_diff.total_seconds() // 3600)}小时前"
                self.status_labels["last_activity"].config(text=last_activity_str)
                
                # 其他状态
                self.status_labels["processed_files"].config(text=str(status.get('processed_files', 0)))
                self.status_labels["processed_folders"].config(text=str(status.get('processed_folders', 0)))
                self.status_labels["errors"].config(text=str(status.get('errors', 0)))
                self.status_labels["current_action"].config(text=status.get('current_action', '待机'))
                
            else:
                self.status_labels["service_status"].config(text="🔴 未运行", foreground='red')
                for key in ["uptime", "last_activity", "processed_files", "processed_folders", "errors", "current_action"]:
                    self.status_labels[key].config(text="--")
        
        except Exception as e:
            messagebox.showerror("错误", f"读取状态失败: {str(e)}")
    
    def show_today_logs(self):
        """显示今日日志"""
        today = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.log_dir, f'fim_vietnam_{today}.log')
        self.show_log_file(log_file, "今日主日志")
    
    def show_progress_logs(self):
        """显示进度日志"""
        today = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.log_dir, f'fim_vietnam_progress_{today}.log')
        self.show_log_file(log_file, "今日进度日志")
    
    def show_error_logs(self):
        """显示错误日志"""
        today = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.log_dir, f'fim_vietnam_error_{today}.log')
        self.show_log_file(log_file, "今日错误日志")
    
    def show_log_file(self, log_file, title):
        """显示日志文件内容"""
        try:
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 显示最后1000行
                lines = content.split('\n')
                if len(lines) > 1000:
                    content = '\n'.join(lines[-1000:])
                    content = "... (显示最后1000行) ...\n\n" + content
                
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(1.0, content)
                self.log_text.see(tk.END)
                
                # 更新标题
                log_frame = self.log_text.master
                log_frame.config(text=f"{title} ({len(lines)} 行)")
            else:
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(1.0, f"日志文件不存在: {log_file}")
        
        except Exception as e:
            messagebox.showerror("错误", f"读取日志失败: {str(e)}")
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        self.auto_refresh = self.auto_refresh_var.get()
    
    def schedule_refresh(self):
        """定时刷新"""
        if self.auto_refresh:
            self.update_status()
        self.root.after(self.refresh_interval, self.schedule_refresh)
    
    def run(self):
        """运行监控界面"""
        self.root.mainloop()

def main():
    """主函数"""
    monitor = FIMStatusMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
