# FIM Vietnam 生产环境部署指南

## 📋 部署概述

本指南将帮助您在没有Python环境的Windows电脑上部署FIM Vietnam自动化监控服务，实现开机自动启动和后台持续监控。

## 🛠️ 准备工作

### 1. 在开发机器上打包程序

```bash
# 1. 安装打包依赖
pip install pyinstaller psutil

# 2. 运行打包脚本
python build_exe.py

# 3. 创建Windows服务配置
python create_windows_service.py
```

### 2. 准备部署包

打包完成后，`dist` 目录将包含以下文件：
- `FIM_Vietnam_Service.exe` - 主程序
- `FIM_Vietnam_Task.xml` - 任务计划程序配置
- `install_service.bat` - 服务安装脚本
- `start_service.bat` - 服务启动脚本
- `stop_service.bat` - 服务停止脚本
- `uninstall_service.bat` - 服务卸载脚本
- `check_status.bat` - 状态检查脚本

## 🚀 目标机器部署

### 1. 复制文件

将 `dist` 目录下的所有文件复制到目标机器的固定位置，例如：
```
C:\FIM_Vietnam\
```

### 2. 安装解压工具

确保目标机器安装了以下软件之一：
- **7-Zip**（推荐，免费）: https://www.7-zip.org/
- **WinRAR**: https://www.winrar.com/

### 3. 安装服务

1. 右键点击 `install_service.bat`
2. 选择"以管理员身份运行"
3. 按照提示完成安装

### 4. 启动服务

运行 `start_service.bat` 或重启电脑自动启动

## 📊 监控和管理

### 1. 状态监控

在开发机器上运行监控工具：
```bash
python monitor_status.py
```

监控界面显示：
- 服务运行状态
- 处理统计信息
- 实时日志
- 错误信息

### 2. 日志系统

程序会自动创建以下日志文件：

```
logs/
├── fim_vietnam_20250117.log          # 主日志（按日期）
├── fim_vietnam_error_20250117.log    # 错误日志
├── fim_vietnam_progress_20250117.log # 进度日志
└── status.json                       # 实时状态文件
```

### 3. 日志内容说明

**主日志** - 记录所有操作：
```
2025-01-17 10:30:15 - INFO - [process_valid_folder:245] - 开始处理文件夹: Z:\Vietnam\PC卡\B-652G_20250117
2025-01-17 10:30:16 - INFO - [extract_aircraft_info:298] - 找到完整飞机号: B-652G
2025-01-17 10:30:17 - INFO - [copy_to_data_bak:856] - 复制到DATA_BAK完成: Z:\DATA_BAK\FDIMU_PC\2025\B-652G\2025-0117
```

**进度日志** - 简化的处理记录：
```
2025-01-17 10:30:15 - ACTION=处理新增项目 FILES=15 FOLDERS=3 ERRORS=0
2025-01-17 10:35:20 - ACTION=监控中 FILES=15 FOLDERS=3 ERRORS=0
```

**错误日志** - 仅记录错误：
```
2025-01-17 10:32:10 - ERROR - [extract_rar:187] - RAR解压失败: 文件损坏
```

## ⚙️ 性能优化

### 1. 资源占用优化

- **低优先级运行**: 程序自动设置为低优先级，不影响其他应用
- **智能监控**: 使用文件系统事件，避免轮询扫描
- **延迟处理**: 文件事件延迟5秒处理，避免重复操作
- **内存管理**: 自动清理旧的事件记录，防止内存泄漏

### 2. 监控频率设置

- **文件事件**: 实时响应
- **心跳检查**: 每5分钟一次
- **日志清理**: 每5分钟检查，保留7天
- **状态更新**: 每次操作时更新

### 3. 系统要求

- **CPU**: 几乎无占用（事件驱动）
- **内存**: 约50-100MB
- **磁盘**: 日志文件每天约10-50MB
- **网络**: 仅访问本地和网络磁盘

## 🔧 故障排除

### 1. 服务无法启动

**检查步骤**：
1. 运行 `check_status.bat` 查看状态
2. 检查任务计划程序中是否有 "FIM_Vietnam_Service"
3. 查看错误日志文件

**常见问题**：
- 路径不存在：检查监控路径是否可访问
- 权限不足：确保以管理员身份安装
- 端口占用：检查是否有其他实例运行

### 2. 文件处理失败

**检查步骤**：
1. 查看主日志文件
2. 检查解压工具是否安装
3. 验证目标路径权限

### 3. 监控界面无法连接

**检查步骤**：
1. 确认 `status.json` 文件存在
2. 检查日志目录权限
3. 验证服务是否正在运行

## 📱 远程监控

### 1. 网络共享日志

将日志目录设置为网络共享：
```
\\目标机器IP\FIM_Vietnam\logs
```

### 2. 远程桌面

使用Windows远程桌面连接查看状态：
```
mstsc /v:目标机器IP
```

### 3. 邮件通知（可选）

可以修改程序添加邮件通知功能，在出现错误时发送邮件。

## 🔄 维护操作

### 1. 更新程序

1. 停止服务：`stop_service.bat`
2. 替换可执行文件
3. 启动服务：`start_service.bat`

### 2. 清理日志

程序自动清理7天前的日志，也可手动删除 `logs` 目录下的旧文件。

### 3. 备份配置

定期备份以下文件：
- 程序目录下的所有 `.bat` 文件
- `FIM_Vietnam_Task.xml`
- `status.json`

## 📞 技术支持

如遇到问题，请提供：
1. 错误日志文件
2. 系统环境信息
3. 具体错误现象描述

---

**注意**: 本服务设计为7x24小时持续运行，建议在生产环境中进行充分测试后部署。
