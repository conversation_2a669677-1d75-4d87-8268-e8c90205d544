"""
FIM Vietnam 带GUI的完整版本
支持GUI界面和后台服务两种模式
"""

import os
import sys
import argparse
import threading
import time
from datetime import datetime

# 导入原有的监控功能
from FIM_Vietnam import *

# 导入GUI功能
try:
    from FIM_Vietnam_GUI import FIMVietnamGUI
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("GUI依赖未安装，将以命令行模式运行")

class FIMVietnamManager:
    """FIM Vietnam 管理器"""
    
    def __init__(self, gui_mode=True):
        self.gui_mode = gui_mode and GUI_AVAILABLE
        self.service_thread = None
        self.monitor = None
        self.gui = None
        
    def start_service(self):
        """启动后台服务"""
        if self.service_thread and self.service_thread.is_alive():
            print("服务已在运行中")
            return
        
        print("启动FIM Vietnam后台服务...")
        self.service_thread = threading.Thread(target=self.run_service, daemon=True)
        self.service_thread.start()
        
    def run_service(self):
        """运行后台服务"""
        try:
            # 创建系统监控器
            self.monitor = SystemMonitor()
            self.monitor.update_status("服务启动", {
                "mon_path": MON_PATH,
                "data_bak_path": DATA_BAK_PATH,
                "airfase_path": AIRFASE_PATH,
                "target_aircraft": TARGET_AIRCRAFT
            })
            
            logger.info("="*60)
            logger.info("FIM Vietnam 自动化监控服务启动")
            logger.info(f"监控路径: {MON_PATH}")
            logger.info(f"DATA_BAK路径: {DATA_BAK_PATH}")
            logger.info(f"AirFASE路径: {AIRFASE_PATH}")
            logger.info(f"目标飞机: {TARGET_AIRCRAFT}")
            logger.info(f"进程ID: {os.getpid()}")
            logger.info("="*60)
            
            # 检查监控路径是否存在
            if not os.path.exists(MON_PATH):
                logger.error(f"监控路径不存在: {MON_PATH}")
                self.monitor.update_status("错误", {"error": f"监控路径不存在: {MON_PATH}"})
                return
            
            # 创建目标路径
            os.makedirs(DATA_BAK_PATH, exist_ok=True)
            os.makedirs(AIRFASE_PATH, exist_ok=True)
            logger.info("目标路径检查完成")
            
            # 首次启动时处理现有数据
            logger.info("首次启动，处理现有数据...")
            self.monitor.update_status("初始化处理", {"action": "处理现有数据"})
            
            processor = VietnamDataProcessor(self.monitor)
            processor.process_new_data()
            
            # 设置文件系统监控
            event_handler = VietnamFileHandler(self.monitor)
            observer = Observer()
            observer.schedule(event_handler, MON_PATH, recursive=True)
            
            # 启动监控
            observer.start()
            logger.info("文件系统监控已启动")
            self.monitor.update_status("监控中", {"action": "等待新文件"})
            
            # 主循环
            heartbeat_counter = 0
            while True:
                time.sleep(30)  # 每30秒检查一次
                
                # 每5分钟发送一次心跳
                heartbeat_counter += 1
                if heartbeat_counter >= 10:  # 30秒 * 10 = 5分钟
                    self.monitor.update_status("监控中", {"heartbeat": datetime.now().isoformat()})
                    heartbeat_counter = 0
                    
                    # 清理旧日志文件（保留7天）
                    cleanup_old_logs()
                
        except KeyboardInterrupt:
            logger.info("接收到停止信号，正在关闭监控...")
            self.monitor.update_status("正在停止", {"action": "用户中断"})
        except Exception as e:
            logger.error(f"服务运行异常: {str(e)}")
            self.monitor.update_status("异常停止", {"error": str(e)})
            self.monitor.increment_errors()
        finally:
            try:
                observer.stop()
                observer.join(timeout=10)
                logger.info("监控已停止")
                self.monitor.update_status("已停止", {"stop_time": datetime.now().isoformat()})
            except:
                pass
    
    def start_gui(self):
        """启动GUI界面"""
        if not GUI_AVAILABLE:
            print("GUI依赖未安装，无法启动图形界面")
            print("请运行: pip install pystray pillow")
            return
        
        print("启动FIM Vietnam GUI界面...")
        self.gui = FIMVietnamGUI()
        self.gui.run()
    
    def run(self):
        """运行程序"""
        if self.gui_mode:
            # GUI模式：启动GUI界面
            self.start_gui()
        else:
            # 命令行模式：直接运行服务
            self.run_service()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FIM Vietnam 自动化监控服务')
    parser.add_argument('--no-gui', action='store_true', 
                       help='以命令行模式运行（无GUI）')
    parser.add_argument('--service-only', action='store_true',
                       help='仅运行后台服务')
    
    args = parser.parse_args()
    
    # 设置进程优先级
    try:
        import psutil
        p = psutil.Process()
        p.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if os.name == 'nt' else 10)
        logger.info("已设置进程为低优先级")
    except ImportError:
        logger.info("psutil未安装，无法设置进程优先级")
    except Exception as e:
        logger.warning(f"设置进程优先级失败: {str(e)}")
    
    # 创建管理器
    gui_mode = not args.no_gui and not args.service_only
    manager = FIMVietnamManager(gui_mode=gui_mode)
    
    if args.service_only:
        print("以纯服务模式运行...")
        manager.run_service()
    else:
        manager.run()

if __name__ == "__main__":
    main()
