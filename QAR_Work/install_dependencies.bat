@echo off
echo 安装FIM_Vietnam.py所需的依赖包...
echo.

echo 安装watchdog (文件系统监控)...
pip install watchdog

echo.
echo 安装rarfile (RAR文件解压)...
pip install rarfile

echo.
echo 安装py7zr (7z文件解压)...
pip install py7zr

echo.
echo 依赖包安装完成！
echo.
echo ========================================
echo RAR文件解压支持说明：
echo ========================================
echo 脚本支持多种RAR解压方式：
echo 1. 自动查找WinRAR程序 (推荐)
echo 2. 自动查找7-Zip程序
echo 3. 使用rarfile库 + unrar工具
echo.
echo 建议安装以下软件之一：
echo - WinRAR: https://www.winrar.com/
echo - 7-Zip: https://www.7-zip.org/
echo.
echo 常见安装路径：
echo - C:\Program Files\WinRAR\WinRAR.exe
echo - C:\Program Files\7-Zip\7z.exe
echo.
echo 如果解压仍然失败，请检查：
echo 1. 是否已安装WinRAR或7-Zip
echo 2. 压缩文件是否损坏
echo 3. 是否有足够的磁盘空间
echo ========================================
echo.
pause
