"""
创建Windows服务配置
用于将FIM Vietnam设置为Windows服务，开机自动启动
"""

import os
import sys

def create_task_scheduler_xml():
    """创建任务计划程序XML配置"""
    
    # 获取当前目录
    current_dir = os.path.abspath(".")
    exe_path = os.path.join(current_dir, "FIM_Vietnam_Service.exe")
    
    xml_content = f"""<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>2025-01-17T12:00:00</Date>
    <Author>FIM Vietnam</Author>
    <Description>FIM Vietnam 自动化监控服务</Description>
  </RegistrationInfo>
  <Triggers>
    <BootTrigger>
      <Enabled>true</Enabled>
      <Delay>PT30S</Delay>
    </BootTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <UserId>S-1-5-18</UserId>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
    <RestartOnFailure>
      <Interval>PT1M</Interval>
      <Count>3</Count>
    </RestartOnFailure>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{exe_path}</Command>
      <WorkingDirectory>{current_dir}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>"""
    
    return xml_content

def create_service_scripts():
    """创建服务管理脚本"""
    
    # 任务计划程序XML
    xml_content = create_task_scheduler_xml()
    with open("FIM_Vietnam_Task.xml", "w", encoding="utf-16") as f:
        f.write(xml_content)
    
    # 安装服务脚本
    install_script = """@echo off
echo 安装FIM Vietnam服务到任务计划程序...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续安装...
) else (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 删除已存在的任务（如果有）
schtasks /delete /tn "FIM_Vietnam_Service" /f >nul 2>&1

REM 创建新任务
schtasks /create /xml "FIM_Vietnam_Task.xml" /tn "FIM_Vietnam_Service"

if %errorLevel% == 0 (
    echo.
    echo [OK] 服务安装成功！
    echo.
    echo 服务名称: FIM_Vietnam_Service
    echo 启动方式: 开机自动启动（延迟30秒）
    echo 重启策略: 失败时自动重启（最多3次，间隔1分钟）
    echo.
    echo 可以通过以下方式管理服务：
    echo 1. 运行 start_service.bat 立即启动服务
    echo 2. 运行 stop_service.bat 停止服务
    echo 3. 运行 monitor_status.py 查看服务状态
    echo 4. 在任务计划程序中查看和管理
    echo.
) else (
    echo [ERROR] 服务安装失败！
    echo 请检查XML文件是否存在，或联系技术支持。
)

pause
"""
    
    # 启动服务脚本
    start_script = """@echo off
echo 启动FIM Vietnam服务...

schtasks /run /tn "FIM_Vietnam_Service"

if %errorLevel% == 0 (
    echo [OK] 服务启动命令已发送
    echo 请稍等片刻，服务将在后台启动
) else (
    echo [ERROR] 启动失败，请检查服务是否已安装
)

timeout /t 3 >nul
"""

    # 停止服务脚本
    stop_script = """@echo off
echo 停止FIM Vietnam服务...

REM 停止任务计划程序中的任务
schtasks /end /tn "FIM_Vietnam_Service" >nul 2>&1

REM 强制结束进程
taskkill /f /im "FIM_Vietnam_Service.exe" >nul 2>&1

echo [OK] 服务已停止

timeout /t 2 >nul
"""
    
    # 卸载服务脚本
    uninstall_script = """@echo off
echo 卸载FIM Vietnam服务...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续卸载...
) else (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 停止服务
call stop_service.bat

REM 删除任务计划程序中的任务
schtasks /delete /tn "FIM_Vietnam_Service" /f

if %errorLevel% == 0 (
    echo [OK] 服务卸载成功！
) else (
    echo [WARNING] 任务删除可能失败，请手动检查任务计划程序
)

echo.
echo 如需完全清理，请手动删除以下文件：
echo - 程序目录下的所有文件
echo - logs 目录下的日志文件
echo - status.json 状态文件
echo.

pause
"""
    
    # 状态检查脚本
    status_script = """@echo off
echo 检查FIM Vietnam服务状态...
echo.

REM 检查任务计划程序中的任务
schtasks /query /tn "FIM_Vietnam_Service" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] 服务已安装在任务计划程序中

    REM 检查进程是否运行
    tasklist /fi "imagename eq FIM_Vietnam_Service.exe" | find /i "FIM_Vietnam_Service.exe" >nul
    if %errorLevel% == 0 (
        echo [OK] 服务进程正在运行
    ) else (
        echo [WARNING] 服务已安装但未运行
    )
) else (
    echo [ERROR] 服务未安装
)

echo.
echo 要查看详细状态，请运行: python monitor_status.py
echo.

pause
"""
    
    # 写入脚本文件
    scripts = [
        ("install_service.bat", install_script),
        ("start_service.bat", start_script),
        ("stop_service.bat", stop_script),
        ("uninstall_service.bat", uninstall_script),
        ("check_status.bat", status_script)
    ]
    
    for filename, content in scripts:
        with open(filename, "w", encoding="gbk") as f:
            f.write(content)
    
    print("[OK] 服务管理脚本创建完成！")
    print("\n📁 创建的文件：")
    print("- FIM_Vietnam_Task.xml      (任务计划程序配置)")
    print("- install_service.bat       (安装服务)")
    print("- start_service.bat         (启动服务)")
    print("- stop_service.bat          (停止服务)")
    print("- uninstall_service.bat     (卸载服务)")
    print("- check_status.bat          (检查状态)")

def main():
    """主函数"""
    print("FIM Vietnam Windows服务配置生成器")
    print("="*50)
    
    if not os.path.exists("FIM_Vietnam_Service.exe"):
        print("[ERROR] 找不到 FIM_Vietnam_Service.exe")
        print("请先运行 build_exe.py 生成可执行文件")
        return
    
    create_service_scripts()
    
    print("\n📋 部署步骤：")
    print("1. 以管理员身份运行 install_service.bat")
    print("2. 运行 start_service.bat 启动服务")
    print("3. 运行 python monitor_status.py 查看状态")
    print("4. 重启电脑测试开机自动启动")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
