import os
import shutil
from tkinter import *
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import tkcalendar as tkcal
from smbprotocol.connection import Connection
from smbprotocol.open import Open, CreateDisposition, CreateOptions, FilePipePrinterAccessMask
from smbprotocol.tree import TreeConnect
import socket
from smb.SMBConnection import SMBConnection

# 配置参数（根据实际情况调整）
SHARE_SERVER = "*********"    # SMB服务器IP
SHARE_NAME = "qar_nas"        # 共享名称
SHARE_PATH = "/WGL"           # 共享路径
USERNAME = "qar"              # 用户名
PASSWORD = "66832340@zy"      # 密码
LOCAL_PATH = r"D:\AirFASE\FIMRoot"  # 本地存储路径


# 飞机型号映射
AIRCRAFT_TYPE_MAP = {
        # A319
        "B-1011": "A319", "B-1093": "A319", "B-6163": "A319",
        "B-6229": "A319", "B-6230": "A319", "B-8852": "A319",
        "B-8853": "A319",

        # A320
        "B-1856": "A320", "B-6728": "A320", "B-6729": "A320",
        "B-6730": "A320", "B-6850": "A320", "B-6900": "A320",
        "B-6907": "A320", "B-6940": "A320", "B-9985": "A320",

        # A320S
        "B-1071": "A320S", "B-1095": "A320S", "B-1630": "A320S",
        "B-1631": "A320S", "B-1632": "A320S", "B-1633": "A320S",
        "B-30CP": "A320S", "B-30CQ": "A320S", "B-30DW": "A320S",
        "B-30DX": "A320S", "B-321Y": "A320S", "B-323X": "A320S",
        "B-329W": "A320S", "B-329X": "A320S", "B-32AE": "A320S",
        "B-32AF": "A320S", "B-32D7": "A320S", "B-8162": "A320S",
        "B-8185": "A320S", "B-8186": "A320S", "B-8342": "A320S",
        "B-8345": "A320S", "B-8606": "A320S", "B-8607": "A320S",
        "B-8608": "A320S", "B-8609": "A320S", "B-8610": "A320S",
        "B-8875": "A320S", "B-8876": "A320S", "B-8877": "A320S",
        "B-8878": "A320S", "B-8879": "A320S",

        # A320N
        "B-32D8": "A320N", "B-32DC": "A320N", "B-32EQ": "A320N",
        "B-32ER": "A320N", "B-32ES": "A320N", "B-32EX": "A320N",
        "B-32FA": "A320N", "B-32GV": "A320N", "B-32GW": "A320N",
        "B-32HC": "A320N", "B-32HL": "A320N", "B-32M2": "A320N",

        # A321N
        "B-32F9": "A321N", "B-32G5": "A321N", "B-32GM": "A321N",
        "B-32GT": "A321N", "B-32GU": "A321N",

        # ARJ21
        "B-001K": "ARJ21", "B-001L": "ARJ21", "B-001S": "ARJ21",
        "B-001U": "ARJ21", "B-001W": "ARJ21", "B-001Y": "ARJ21",
        "B-001Z": "ARJ21", "B-099C": "ARJ21", "B-099J": "ARJ21",
        "B-099K": "ARJ21", "B-099U": "ARJ21", "B-099X": "ARJ21",
        "B-099Z": "ARJ21", "B-104X": "ARJ21", "B-3321": "ARJ21",
        "B-3322": "ARJ21", "B-3328": "ARJ21", "B-3329": "ARJ21",
        "B-3386": "ARJ21", "B-3387": "ARJ21", "B-3388": "ARJ21",
        "B-602A": "ARJ21", "B-602C": "ARJ21", "B-603M": "ARJ21",
        "B-603N": "ARJ21", "B-603P": "ARJ21", "B-603Q": "ARJ21",
        "B-603W": "ARJ21", "B-603Z": "ARJ21", "B-604A": "ARJ21",
        "B-604C": "ARJ21", "B-604D": "ARJ21", "B-604F": "ARJ21",
        "B-605M": "ARJ21", "B-605N": "ARJ21", "B-620D": "ARJ21",
        "B-620E": "ARJ21", "B-650S": "ARJ21", "B-650T": "ARJ21",
        "B-651M": "ARJ21", "B-651P": "ARJ21", "B-651Q": "ARJ21",
        "B-651R": "ARJ21", "B-651S": "ARJ21", "B-651T": "ARJ21",
        "B-652F": "ARJ21", "B-652G": "ARJ21", "B-653E": "ARJ21",
        "B-656C": "ARJ21", "B-656D": "ARJ21", "B-656E": "ARJ21",
        "B-658A": "ARJ21", "B-658C": "ARJ21"
    }

# 制造商映射
MANUFACTURER_MAP = {
    "A319": "AIRBUS", "A319S": "AIRBUS", "A320": "AIRBUS", "A320S": "AIRBUS",
    "A320N": "AIRBUS", "A321": "AIRBUS", "A321N": "AIRBUS", "A330": "AIRBUS",
    "A350": "AIRBUS", "ARJ21": "COMAC", "C909": "COMAC", "C919": "COMAC",
    "B737": "BOEING", "UNKNOWN": "UNKNOWN"
}


class WGLCopyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("WGL数据拷贝工具")

        # 默认路径
        self.qar_path = SHARE_PATH
        self.fim_path = r"D:\AirFASE\FIMRoot"

        # SMB连接对象
        self.conn = None
        self.tree = None

        # 默认日期设置为2025年3月1日
        default_date = datetime(2025, 3, 1)

        # 创建GUI组件
        self.create_widgets(default_date)

    def check_network_connectivity(self):
        try:
            # 检查是否能解析主机名
            socket.gethostbyname(SHARE_SERVER)

            # 检查445端口是否开放
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(3)
            result = s.connect_ex((SHARE_SERVER, 445))
            s.close()

            if result == 0:
                print("网络连通性检查通过，445端口开放")
                return True
            else:
                messagebox.showerror("连接错误", "无法连接到SMB端口445，请检查防火墙设置")
                return False

        except socket.error as e:
            messagebox.showerror("连接错误", f"网络连接检查失败:\n{str(e)}")
            return False

    def connect_smb(self):
        try:
            print(f"尝试连接到: {SHARE_SERVER}, 共享名: {SHARE_NAME}")

            # 创建SMB连接
            self.conn = SMBConnection(
                USERNAME,
                PASSWORD,
                "PYTHON_SMB_CLIENT",  # 客户端名称（任意）
                SHARE_SERVER,
                use_ntlm_v2=True,
                is_direct_tcp=True  # 直接TCP连接
            )

            # 尝试连接
            if self.conn.connect(SHARE_SERVER, 445, timeout=10):
                print("SMB连接成功!")
                return True
            else:
                messagebox.showerror("连接错误", "认证失败，请检查用户名和密码")
                return False

        except Exception as e:
            error_msg = f"无法连接到SMB服务器:\n{str(e)}"
            print(error_msg)
            messagebox.showerror("连接错误", error_msg)
            return False

    def disconnect_smb(self):
        if hasattr(self, 'conn') and self.conn:
            try:
                self.conn.close()
            except:
                pass

    def create_widgets(self, default_date):
        # 飞机注册号输入
        Label(self.root, text="飞机注册号(默认ALL):").grid(row=0, column=0, padx=5, pady=5, sticky=W)
        self.ac_reg_entry = Entry(self.root, width=20)
        self.ac_reg_entry.grid(row=0, column=1, padx=5, pady=5)
        self.ac_reg_entry.insert(0, "ALL")

        # 飞机类型/制造商输入
        Label(self.root, text="飞机类型/制造商(默认ALL):").grid(row=1, column=0, padx=5, pady=5, sticky=W)
        self.ac_type_entry = Entry(self.root, width=20)
        self.ac_type_entry.grid(row=1, column=1, padx=5, pady=5)
        self.ac_type_entry.insert(0, "ALL")

        # 开始日期选择
        Label(self.root, text="开始日期:").grid(row=2, column=0, padx=5, pady=5, sticky=W)
        self.start_date = tkcal.DateEntry(self.root, width=12, date_pattern='y-mm-dd')
        self.start_date.grid(row=2, column=1, padx=5, pady=5, sticky=W)
        self.start_date.set_date(default_date)

        # 结束日期选择
        Label(self.root, text="结束日期:").grid(row=3, column=0, padx=5, pady=5, sticky=W)
        self.end_date = tkcal.DateEntry(self.root, width=12, date_pattern='y-mm-dd')
        self.end_date.grid(row=3, column=1, padx=5, pady=5, sticky=W)
        self.end_date.set_date(default_date)

        # 执行按钮
        self.copy_btn = Button(self.root, text="拷贝WGL数据", command=self.copy_wgl_data)
        self.copy_btn.grid(row=4, column=0, columnspan=2, pady=10)

        # 状态文本框
        self.status_text = Text(self.root, height=10, width=50)
        self.status_text.grid(row=5, column=0, columnspan=2, padx=5, pady=5)

    def aircraft_type(self, aircraft):
        return AIRCRAFT_TYPE_MAP.get(aircraft, "")

    def air_manuf(self, air_type):
        return MANUFACTURER_MAP.get(air_type, "UNKNOWN")

    def copy_wgl_data(self):
        # 获取用户输入
        ac_reg = self.ac_reg_entry.get().strip().upper()
        ac_type = self.ac_type_entry.get().strip().upper()
        start_date = self.start_date.get_date()
        end_date = self.end_date.get_date()

        # 验证飞机注册号 - 修复括号不匹配问题
        if not (ac_reg == "ALL" or (len(ac_reg) == 6 and ac_reg.startswith("B-"))):  # 这里添加了缺失的右括号
            messagebox.showerror("错误", "无效的飞机注册号! 格式应为B-XXXX或ALL")
            return

        # 验证日期
        if start_date > end_date:
            messagebox.showerror("错误", "结束日期不能早于开始日期!")
            return

        # 更新按钮状态
        self.copy_btn.config(text="拷贝中...", state=DISABLED)
        self.root.update()

        try:
            if not self.connect_smb():
                return

            # 获取年份并构建完整路径
            data_year = start_date.year
            full_qar_path = f"/WGL/WGL_{data_year}"  # SMB路径使用正斜杠

            # 获取飞机列表
            if ac_reg != "ALL":
                aircraft_list = [ac_reg]
            else:
                aircraft_list = self.get_aircraft_list(full_qar_path, ac_type)

            # 拷贝数据
            self.copy_data(aircraft_list, full_qar_path, start_date, end_date)

        except Exception as e:
            messagebox.showerror("错误", f"拷贝过程中发生错误:\n{str(e)}")
        finally:
            self.disconnect_smb()
            self.copy_btn.config(text="拷贝WGL数据", state=NORMAL)

    def get_aircraft_list(self, qar_path, ac_type):
        aircraft_list = []

        try:
            if not self.connect_smb():
                return []

            # 列出共享目录
            files = self.conn.listPath(SHARE_NAME, qar_path)

            for f in files:
                if f.isDirectory and len(f.filename) == 6 and f.filename.startswith("B-"):
                    if ac_type == "ALL":
                        aircraft_list.append(f.filename)
                    else:
                        temp_type = self.aircraft_type(f.filename)
                        temp_manuf = self.air_manuf(temp_type)
                        if ac_type == temp_type or ac_type == temp_manuf:
                            aircraft_list.append(f.filename)
        except Exception as e:
            self.log_status(f"获取飞机列表时出错: {str(e)}")
        finally:
            self.disconnect_smb()

        return aircraft_list

    def copy_data(self, aircraft_list, qar_path, start_date, end_date):
        copied_files = []

        if not self.connect_smb():
            return

        try:
            # 调试日期范围
            print(f"【调试】搜索日期范围: {start_date} 到 {end_date}")

            for aircraft in aircraft_list:
                aircraft_dir = f"{qar_path}/{aircraft}"
                print(f"\n【调试】正在检查飞机: {aircraft} 在目录: {aircraft_dir}")

                try:
                    files = self.conn.listPath(SHARE_NAME, aircraft_dir)
                    print(f"【调试】找到 {len(files)} 个条目")

                    for f in files:
                        filename = f.filename
                        # 跳过特殊目录和不符合命名规则的文件
                        if filename in ('.', '..') or not (filename.startswith(aircraft) and filename.endswith('.wgl')):
                            continue

                        print(f"【调试】处理文件: {filename}")

                        try:
                            # 解析文件名中的日期 (格式: B-XXXX_YYYYMMDDHHMMSS.wgl)
                            date_part = filename.split('_')[1][:8]  # 提取YYYYMMDD
                            year = int(date_part[:4])
                            month = int(date_part[4:6])
                            day = int(date_part[6:8])

                            # 验证日期有效性
                            if not (1 <= month <= 12) or not (1 <= day <= 31):
                                print(f"【调试】无效日期: {date_part}")
                                continue

                            file_date = datetime(year, month, day)
                            print(f"【调试】文件日期: {file_date}")

                            if start_date <= file_date <= end_date:
                                print("【调试】文件在日期范围内")
                                air_type = self.aircraft_type(aircraft)

                                if air_type:
                                    # 构建目标路径
                                    dest_dir = os.path.join(self.fim_path, air_type, aircraft)
                                    os.makedirs(dest_dir, exist_ok=True)
                                    dest_path = os.path.join(dest_dir, filename)

                                    print(f"【调试】准备拷贝到: {dest_path}")

                                    # 拷贝文件
                                    with open(dest_path, 'wb') as local_file:
                                        self.conn.retrieveFile(SHARE_NAME, f"{aircraft_dir}/{filename}", local_file)

                                    copied_files.append(filename)
                                    self.log_status(f"{filename} 已拷贝\n")
                            else:
                                print(f"【调试】文件日期不在范围内")

                        except Exception as e:
                            print(f"【调试】处理文件出错: {str(e)}")
                            self.log_status(f"处理 {filename} 时出错: {str(e)}\n")
                            continue

                except Exception as e:
                    print(f"【调试】处理飞机出错: {str(e)}")
                    self.log_status(f"处理 {aircraft} 时出错: {str(e)}\n")
                    continue

        finally:
            self.disconnect_smb()

        if copied_files:
            self.log_status(f"\n成功拷贝 {len(copied_files)} 个文件")
        else:
            self.log_status("\n没有找到符合条件的文件")
            print("【调试】未找到文件的可能原因：")
            print(f"- 日期范围: {start_date} 到 {end_date}")
            print("- 文件名格式不符合 B-XXXX_YYYYMMDDHHMMSS.wgl")
            print("- 文件实际不存在于服务器")

    def _copy_smb_folder(self, smb_path, local_path):
        """递归拷贝SMB共享文件夹到本地"""
        if not os.path.exists(local_path):
            os.makedirs(local_path)

        for f in self.conn.listPath(SHARE_NAME, smb_path):
            if f.filename in ('.', '..'):
                continue

            remote_full_path = f"{smb_path}/{f.filename}"
            local_full_path = os.path.join(local_path, f.filename)

            if f.isDirectory:
                self._copy_smb_folder(remote_full_path, local_full_path)
            else:
                with open(local_full_path, 'wb') as local_file:
                    self.conn.retrieveFile(SHARE_NAME, remote_full_path, local_file)

    def log_status(self, message):
        self.status_text.insert(END, message)
        self.status_text.see(END)
        self.root.update()


if __name__ == "__main__":
    root = Tk()
    app = WGLCopyApp(root)
    root.mainloop()
