import os
import shutil
import datetime
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import ctypes
from ctypes import wintypes
import string

# ==================== 全局配置 ====================
APP_TITLE = "QAR数据下载系统"
BG_COLOR = "#f5f7fa"
HEADER_COLOR = "#3a7ca5"
TEXT_COLOR = "#2f3e46"
HIGHLIGHT_COLOR = "#2a9d8f"
BUTTON_COLOR = "#3a7ca5"
FONT_NAME = "Microsoft YaHei"
FONT_SIZE = 10
COMPACT_PADDING = 3


class QARApp:
    def __init__(self, root):
        self.root = root
        self.flight_data = {}  # 先初始化字典
        self.setup_vars()  # 然后设置变量
        self.setup_ui()  # 最后设置UI

    def setup_vars(self):
        """初始化数据变量"""
        self.flight_data = {
            "aircraft": tk.StringVar(value="B-XXXX"),
            "date": tk.StringVar(value="YYYY-MM-DD"),
            "time": tk.StringVar(value="HH:MM:SS"),
            "flight_num": tk.StringVar(value="EUXXXX"),
            "departure": tk.StringVar(value="XXXX"),
            "destination": tk.StringVar(value="YYYY"),
            "leg_time": tk.StringVar(value="00:00:00"),
            "fuel_consumption": tk.StringVar(value="0.0")
        }
        self.DownLoadPath = "D:\\PCMCIA\\"
        self.DrivePath = None

    def setup_ui(self):
        """设置用户界面"""
        self.root.title(APP_TITLE)
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        self.root.configure(bg=BG_COLOR)

        # 自定义样式
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 配置样式
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR,
                             font=(FONT_NAME, FONT_SIZE))
        self.style.configure('Header.TLabel', font=(FONT_NAME, 12, 'bold'),
                             foreground='white', background=HEADER_COLOR)
        self.style.configure('Highlight.TLabel', foreground=HIGHLIGHT_COLOR,
                             font=(FONT_NAME, FONT_SIZE, 'bold'))
        self.style.configure('TButton', font=(FONT_NAME, FONT_SIZE),
                             background=BUTTON_COLOR, foreground='white',
                             borderwidth=1)
        self.style.map('TButton',
                       background=[('active', '#2a6f97'), ('disabled', '#cccccc')])

        # 构建UI
        self.create_header()
        self.create_info_panel()
        self.create_buttons()

    def create_header(self):
        """创建标题区域"""
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=COMPACT_PADDING, pady=COMPACT_PADDING)

        header_label = ttk.Label(
            header_frame,
            text="QAR数据下载系统",
            style='Header.TLabel',
            anchor='center'
        )
        header_label.pack(fill=tk.X, ipady=8)

        ttk.Separator(header_frame, orient='horizontal').pack(fill=tk.X, pady=3)

    def create_info_panel(self):
        """创建信息显示面板"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.create_info_row(main_frame, "飞机号:", "aircraft", 0)
        self.create_info_row(main_frame, "日期:", "date", 1)
        self.create_info_row(main_frame, "时间:", "time", 2)
        self.create_info_row(main_frame, "航班号:", "flight_num", 3)
        self.create_info_row(main_frame, "起飞机场:", "departure", 4)
        self.create_info_row(main_frame, "目的机场:", "destination", 5)
        self.create_info_row(main_frame, "航段时间:", "leg_time", 6, "HH:MM:SS")
        self.create_info_row(main_frame, "耗油量:", "fuel_consumption", 7, "kg")

    def create_info_row(self, parent, label_text, data_key, row, unit=None):
        """创建信息行"""
        label = ttk.Label(parent, text=label_text)
        label.grid(row=row, column=0, padx=COMPACT_PADDING, pady=COMPACT_PADDING, sticky=tk.E)

        value_frame = ttk.Frame(parent)
        value_frame.grid(row=row, column=1, sticky=tk.W)

        value_label = ttk.Label(
            value_frame,
            textvariable=self.flight_data[data_key],
            style='Highlight.TLabel',
            width=15
        )
        value_label.pack(side=tk.LEFT)

        if unit:
            unit_label = ttk.Label(value_frame, text=unit)
            unit_label.pack(side=tk.LEFT, padx=(2, 0))

    def create_buttons(self):
        """创建操作按钮"""
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(
            button_frame,
            text="检测U盘",
            command=self.detect_drive,
            width=10
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="解析数据",
            command=self.parse_data,
            width=10
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="下载数据",
            command=self.download_data,
            width=10
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="退出",
            command=self.root.quit,
            width=10
        ).pack(side=tk.RIGHT, padx=5)

    def detect_drive(self):
        """检测可移动驱动器"""
        kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
        kernel32.GetDriveTypeW.argtypes = [wintypes.LPCWSTR]
        kernel32.GetDriveTypeW.restype = wintypes.UINT

        for drive in [f"{d}:\\" for d in string.ascii_uppercase[2:]]:
            if os.path.exists(drive):
                if kernel32.GetDriveTypeW(drive) == 2:
                    self.DrivePath = drive
                    messagebox.showinfo("检测成功", f"找到U盘: {drive}")
                    return

        messagebox.showwarning("警告", "未检测到可移动存储设备")
        self.DrivePath = None

    def parse_data(self):
        """解析MSG.DAT文件"""
        if not self.DrivePath:
            messagebox.showwarning("警告", "请先检测U盘")
            return

        file_path = os.path.join(self.DrivePath, "MSG.DAT")
        if not os.path.exists(file_path):
            messagebox.showerror("错误", "未找到MSG.DAT文件")
            return

        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
        except Exception as e:
            messagebox.showerror("错误", f"文件读取失败: {str(e)}")
            return

        fields = {
            "DATE:": ("date", lambda x: x.replace("/", "-")),
            "TIME:": "time",
            "DEPT:": "departure",
            "A/C:": ("aircraft", lambda x: x.strip()),
            "FLT:": ("flight_num", lambda x: x.split()[0]),
            "DEST:": "destination",
            "FLIGHT LEG TIME:": "leg_time",
            "FUEL CONSUMPTION:": "fuel_consumption"
        }

        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if not line:
                continue

            for keyword, target in fields.items():
                if keyword in line:
                    start = line.find(keyword) + len(keyword)
                    value = line[start:].split()[0] if ' ' in line[start:] else line[start:]

                    if isinstance(target, tuple):
                        var_name, processor = target
                        self.flight_data[var_name].set(processor(value))
                    else:
                        self.flight_data[target].set(value)

        messagebox.showinfo("解析完成", "数据解析成功!")

    def download_data(self):
        """下载数据到本地"""
        if not all(v.get() for v in self.flight_data.values()):
            messagebox.showwarning("警告", "请先解析完整数据")
            return

        now = datetime.datetime.now()
        download_dir = os.path.join(
            self.DownLoadPath,
            f"{now.year}年",
            f"DL_{now.strftime('%Y-%m%d')}",
            self.flight_data["aircraft"].get(),
            self.flight_data["date"].get()
        )

        confirm = messagebox.askyesno("确认",
                                      f"数据将下载到:\n{download_dir}\n\n是否继续?")
        if not confirm:
            return

        try:
            os.makedirs(download_dir, exist_ok=True)
            for item in os.listdir(self.DrivePath):
                src = os.path.join(self.DrivePath, item)
                dst = os.path.join(download_dir, item)
                if os.path.isdir(src):
                    shutil.copytree(src, dst)
                else:
                    shutil.copy2(src, dst)
            messagebox.showinfo("成功", "数据下载完成!")
        except Exception as e:
            messagebox.showerror("错误", f"下载失败: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = QARApp(root)

    # 窗口居中显示
    window_width = 600
    window_height = 500
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    root.mainloop()
