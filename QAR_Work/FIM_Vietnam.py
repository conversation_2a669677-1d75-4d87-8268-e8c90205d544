"""
FIM Vietnam 完整版本
集成所有功能的单文件版本，包含GUI界面和文件监控
支持直接运行，退出时完全清理所有进程
"""

import os
import sys
import re
import shutil
import time
import zipfile
import threading
import logging
import json
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# GUI相关导入
import tkinter as tk
from tkinter import ttk, messagebox

# 文件监控相关导入
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("警告: watchdog库未安装，文件监控功能将不可用")

# 压缩文件处理相关导入
try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False
    print("警告: rarfile库未安装，RAR文件解压功能将不可用")

try:
    import py7zr
    PY7ZR_AVAILABLE = True
except ImportError:
    PY7ZR_AVAILABLE = False
    print("警告: py7zr库未安装，7Z文件解压功能将不可用")

# 系统托盘相关导入（可选）
try:
    import pystray
    from PIL import Image
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    print("提示: 系统托盘功能不可用，程序将以普通窗口模式运行")

# 配置日志系统
def setup_logging():
    """设置日志系统"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    current_date = datetime.now().strftime('%Y%m%d')
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s')

    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.handlers.clear()

    # 主日志文件
    main_log_file = os.path.join(log_dir, f'fim_vietnam_{current_date}.log')
    main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
    main_handler.setFormatter(formatter)
    logger.addHandler(main_handler)

    # 错误日志文件
    error_log_file = os.path.join(log_dir, f'fim_vietnam_error_{current_date}.log')
    error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    logger.addHandler(error_handler)

    # 控制台输出（调试模式）
    if os.getenv('FIM_DEBUG', '0') == '1':
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger

# 初始化日志
logger = setup_logging()

# 常量定义
MON_PATH = r"Z:\Vietnam\PC卡"
DATA_BAK_PATH = r"Z:\DATA_BAK\FDIMU_PC"
AIRFASE_PATH = r"D:\AirFASE\FIMRoot\ARJ21"

# 目标飞机号
TARGET_AIRCRAFT = ["B-652G", "B-656E"]

# 支持的压缩文件格式
ARCHIVE_EXTENSIONS = ['.zip', '.rar', '.7z']

# 需要保留的文件扩展名
KEEP_EXTENSIONS = ['.DAT', '.QAR', '.QA2']

# 需要保留的文件夹后缀
KEEP_FOLDER_SUFFIXES = ['.REP', '.REC', '.QAR']

# 从PC_BAK.py导入的常量和函数
AIRCRAFT_PATTERN_FULL = r'[Bb]-[A-Za-z0-9]{4}'
AIRCRAFT_PATTERN_SHORT = r'[1368][A-Za-z0-9]{3}'
DATE_PATTERNS = [
    r'(20\d{2})[-\/\.](\d{1,2})[-\/\.](\d{1,2})',
    r'(\d{1,2})[-\/\.](\d{1,2})[-\/\.](20\d{2})',
    r'(20\d{2})(\d{2})(\d{2})',
    r'(\d{2})(\d{2})(20\d{2})',
    r'(20\d{2})[-\/\.](\d{1,2})(\d{1,2})',
    r'(\d{1,2})\.(\d{1,2})',
]

class SystemMonitor:
    """系统监控器 - 集成版本"""

    def __init__(self, gui_callback=None):
        self.start_time = datetime.now()
        self.processed_files = 0
        self.processed_folders = 0
        self.errors = 0
        self.last_activity = datetime.now()
        self.status_file = "status.json"
        self.gui_callback = gui_callback  # GUI更新回调函数

    def update_status(self, action, details=None):
        """更新系统状态"""
        self.last_activity = datetime.now()

        status = {
            'start_time': self.start_time.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'uptime_hours': (self.last_activity - self.start_time).total_seconds() / 3600,
            'processed_files': self.processed_files,
            'processed_folders': self.processed_folders,
            'errors': self.errors,
            'current_action': action,
            'details': details or {}
        }

        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"更新状态文件失败: {str(e)}")

        # 通知GUI更新
        if self.gui_callback:
            try:
                self.gui_callback(action, self.processed_files, self.processed_folders, self.errors)
            except Exception as e:
                logger.error(f"GUI更新回调失败: {str(e)}")

    def increment_files(self):
        """增加处理文件计数"""
        self.processed_files += 1
        self.update_status("处理文件", {"files": self.processed_files})

    def increment_folders(self):
        """增加处理文件夹计数"""
        self.processed_folders += 1
        self.update_status("处理文件夹", {"folders": self.processed_folders})

    def increment_errors(self):
        """增加错误计数"""
        self.errors += 1
        self.update_status("错误", {"errors": self.errors})

class VietnamFileHandler(FileSystemEventHandler):
    """文件系统事件处理器"""

    def __init__(self, monitor):
        self.processing_lock = threading.Lock()
        self.is_processing = False
        self.monitor = monitor
        self.last_event_time = {}  # 防止重复事件
        self.event_delay = 5  # 事件延迟处理时间（秒）

    def on_created(self, event):
        """文件或文件夹创建事件"""
        self._handle_event(event.src_path, "创建")

    def on_moved(self, event):
        """文件或文件夹移动事件"""
        self._handle_event(event.dest_path, "移动")

    def on_modified(self, event):
        """文件修改事件（仅处理文件夹）"""
        if event.is_directory:
            self._handle_event(event.src_path, "修改")

    def _handle_event(self, path, event_type):
        """处理文件系统事件"""
        # 防止重复处理同一路径的事件
        current_time = time.time()
        if path in self.last_event_time:
            if current_time - self.last_event_time[path] < self.event_delay:
                return  # 跳过重复事件

        self.last_event_time[path] = current_time

        # 记录事件
        if os.path.isfile(path):
            logger.info(f"检测到{event_type}文件: {path}")
        else:
            logger.info(f"检测到{event_type}文件夹: {path}")

        # 延迟处理，确保文件完全写入
        threading.Timer(self.event_delay, self.process_new_item, args=[path]).start()

    def process_new_item(self, path):
        """处理新增的文件或文件夹"""
        # 检查路径是否仍然存在
        if not os.path.exists(path):
            logger.info(f"路径不存在，跳过处理: {path}")
            return

        with self.processing_lock:
            if self.is_processing:
                logger.info("正在处理其他文件，跳过当前处理")
                return

            self.is_processing = True

        try:
            logger.info(f"开始处理新增项目: {path}")
            self.monitor.update_status("处理新增项目", {"path": path})

            processor = VietnamDataProcessor(self.monitor)
            processor.process_new_data()

        except Exception as e:
            logger.error(f"处理新增项目时发生错误: {str(e)}")
            self.monitor.increment_errors()
            self.monitor.update_status("处理错误", {"error": str(e), "path": path})
        finally:
            with self.processing_lock:
                self.is_processing = False

            # 清理旧的事件记录（避免内存泄漏）
            current_time = time.time()
            self.last_event_time = {
                k: v for k, v in self.last_event_time.items()
                if current_time - v < 3600  # 保留1小时内的记录
            }

class VietnamDataProcessor:
    """越南数据处理器"""

    def __init__(self, monitor=None):
        self.processed_folders = []
        self.monitor = monitor or SystemMonitor()

    def process_new_data(self):
        """处理新数据的主流程"""
        logger.info("="*60)
        logger.info("开始处理新数据")

        try:
            # 步骤1: 解压所有压缩文件
            self.extract_archives()

            # 步骤2: 扫描并处理有效文件夹
            self.scan_and_process_folders()

            # 步骤3: 清理无效文件夹
            self.cleanup_invalid_folders()

            logger.info("数据处理完成")

        except Exception as e:
            logger.error(f"处理数据时发生错误: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def extract_archives(self):
        """解压所有压缩文件"""
        logger.info("开始扫描和解压压缩文件...")

        for root, dirs, files in os.walk(MON_PATH):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                if file_ext in ARCHIVE_EXTENSIONS:
                    logger.info(f"发现压缩文件: {file}")
                    self.extract_single_archive(file_path)

    def extract_single_archive(self, archive_path):
        """解压单个压缩文件"""
        try:
            file_name = os.path.basename(archive_path)
            file_name_without_ext = os.path.splitext(file_name)[0]
            extract_dir = os.path.join(os.path.dirname(archive_path), file_name_without_ext)

            # 如果解压目录已存在，跳过
            if os.path.exists(extract_dir):
                logger.info(f"解压目录已存在，跳过: {extract_dir}")
                return

            logger.info(f"开始解压: {file_name}")

            # 根据文件类型选择解压方法
            file_ext = os.path.splitext(archive_path)[1].lower()

            success = False

            if file_ext == '.zip':
                success = self.extract_zip(archive_path, extract_dir)
            elif file_ext == '.rar':
                success = self.extract_rar(archive_path, extract_dir)
            elif file_ext == '.7z':
                success = self.extract_7z(archive_path, extract_dir)
            else:
                logger.warning(f"不支持的压缩格式: {file_ext}")
                return

            if success:
                logger.info(f"解压完成: {file_name}")
                # 删除原压缩文件
                os.remove(archive_path)
                logger.info(f"删除原压缩文件: {file_name}")
            else:
                logger.error(f"解压失败: {file_name}")
                # 如果解压失败但创建了目录，删除空目录
                if os.path.exists(extract_dir) and not os.listdir(extract_dir):
                    os.rmdir(extract_dir)

        except Exception as e:
            logger.error(f"解压文件失败: {archive_path}, 错误: {str(e)}")

    def extract_zip(self, archive_path, extract_dir):
        """解压ZIP文件"""
        try:
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            return True
        except Exception as e:
            logger.error(f"ZIP解压失败: {str(e)}")
            return False

    def extract_rar(self, archive_path, extract_dir):
        """解压RAR文件"""
        try:
            # 方法1: 使用rarfile库
            try:
                import rarfile
                # 设置unrar程序路径
                rarfile.UNRAR_TOOL = self.find_unrar_tool()

                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_dir)
                logger.info("使用rarfile库解压RAR成功")
                return True
            except Exception as e:
                logger.warning(f"rarfile库解压失败: {str(e)}")

            # 方法2: 使用系统命令
            return self.extract_rar_with_command(archive_path, extract_dir)

        except Exception as e:
            logger.error(f"RAR解压失败: {str(e)}")
            return False

    def extract_7z(self, archive_path, extract_dir):
        """解压7Z文件"""
        try:
            # 方法1: 使用py7zr库
            try:
                import py7zr
                with py7zr.SevenZipFile(archive_path, mode='r') as z:
                    z.extractall(extract_dir)
                logger.info("使用py7zr库解压7Z成功")
                return True
            except Exception as e:
                logger.warning(f"py7zr库解压失败: {str(e)}")

            # 方法2: 使用系统命令
            return self.extract_7z_with_command(archive_path, extract_dir)

        except Exception as e:
            logger.error(f"7Z解压失败: {str(e)}")
            return False

    def find_unrar_tool(self):
        """查找unrar工具"""
        # 常见的unrar程序路径
        possible_paths = [
            r"C:\Program Files\WinRAR\UnRAR.exe",
            r"C:\Program Files (x86)\WinRAR\UnRAR.exe",
            r"C:\Program Files\7-Zip\7z.exe",
            r"C:\Program Files (x86)\7-Zip\7z.exe",
            "unrar.exe",  # 系统PATH中
            "7z.exe"      # 系统PATH中
        ]

        for path in possible_paths:
            if os.path.exists(path) or shutil.which(path):
                logger.info(f"找到解压工具: {path}")
                return path

        logger.warning("未找到unrar工具")
        return None

    def extract_rar_with_command(self, archive_path, extract_dir):
        """使用命令行解压RAR文件"""
        try:
            import subprocess

            # 创建解压目录
            os.makedirs(extract_dir, exist_ok=True)

            # 尝试使用WinRAR
            winrar_paths = [
                r"C:\Program Files\WinRAR\WinRAR.exe",
                r"C:\Program Files (x86)\WinRAR\WinRAR.exe"
            ]

            for winrar_path in winrar_paths:
                if os.path.exists(winrar_path):
                    cmd = [winrar_path, 'x', '-y', archive_path, extract_dir + os.sep]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        logger.info("使用WinRAR命令解压RAR成功")
                        return True
                    else:
                        logger.warning(f"WinRAR解压失败: {result.stderr}")

            # 尝试使用7-Zip
            sevenzip_paths = [
                r"C:\Program Files\7-Zip\7z.exe",
                r"C:\Program Files (x86)\7-Zip\7z.exe"
            ]

            for sevenzip_path in sevenzip_paths:
                if os.path.exists(sevenzip_path):
                    cmd = [sevenzip_path, 'x', f'-o{extract_dir}', '-y', archive_path]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        logger.info("使用7-Zip命令解压RAR成功")
                        return True
                    else:
                        logger.warning(f"7-Zip解压失败: {result.stderr}")

            logger.error("未找到可用的RAR解压工具")
            return False

        except Exception as e:
            logger.error(f"命令行解压RAR失败: {str(e)}")
            return False

    def extract_7z_with_command(self, archive_path, extract_dir):
        """使用命令行解压7Z文件"""
        try:
            import subprocess

            # 创建解压目录
            os.makedirs(extract_dir, exist_ok=True)

            # 尝试使用7-Zip
            sevenzip_paths = [
                r"C:\Program Files\7-Zip\7z.exe",
                r"C:\Program Files (x86)\7-Zip\7z.exe"
            ]

            for sevenzip_path in sevenzip_paths:
                if os.path.exists(sevenzip_path):
                    cmd = [sevenzip_path, 'x', f'-o{extract_dir}', '-y', archive_path]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        logger.info("使用7-Zip命令解压7Z成功")
                        return True
                    else:
                        logger.warning(f"7-Zip解压失败: {result.stderr}")

            logger.error("未找到7-Zip工具")
            return False

        except Exception as e:
            logger.error(f"命令行解压7Z失败: {str(e)}")
            return False

    def scan_and_process_folders(self):
        """扫描并处理有效文件夹"""
        logger.info("开始扫描有效文件夹...")

        valid_folders = []

        # 扫描所有包含DAR.DAT的文件夹
        for root, dirs, files in os.walk(MON_PATH):
            if 'DAR.DAT' in files:
                logger.info(f"发现有效文件夹: {root}")
                valid_folders.append(root)

        logger.info(f"共发现 {len(valid_folders)} 个有效文件夹")

        # 处理每个有效文件夹
        for folder_path in valid_folders:
            self.process_valid_folder(folder_path)

    def process_valid_folder(self, folder_path):
        """处理单个有效文件夹"""
        try:
            logger.info(f"开始处理文件夹: {folder_path}")

            # 步骤1: 提取飞机号和日期信息
            folder_aircraft = self.extract_aircraft_info(folder_path)
            folder_date = self.extract_date_info(folder_path)

            logger.info(f"文件夹信息 - 飞机号: {folder_aircraft}, 日期: {folder_date}")

            # 步骤2: 读取MSG.DAT文件信息
            msg_aircraft, msg_date = self.parse_msg_file(folder_path)

            logger.info(f"MSG信息 - 飞机号: {msg_aircraft}, 日期: {msg_date}")

            # 步骤3: 综合推出最终信息
            final_aircraft, final_date = self.determine_final_info(
                folder_aircraft, folder_date, msg_aircraft, msg_date
            )

            logger.info(f"最终信息 - 飞机号: {final_aircraft}, 日期: {final_date}")

            # 步骤4: 检查是否为目标飞机
            if final_aircraft not in TARGET_AIRCRAFT:
                logger.info(f"飞机号 {final_aircraft} 不在目标列表中，跳过处理")
                return

            logger.info(f"飞机号 {final_aircraft} 在目标列表中，继续处理")

            # 步骤5: 清理不需要的文件
            self.cleanup_folder_files(folder_path)

            # 步骤6: 复制到DATA_BAK目录
            self.copy_to_data_bak(folder_path, final_aircraft, final_date)

            # 步骤7: 重命名并复制到AirFASE目录
            self.rename_and_copy_to_airfase(folder_path, final_aircraft, final_date)

            logger.info(f"文件夹处理完成: {folder_path}")

        except Exception as e:
            logger.error(f"处理文件夹失败: {folder_path}, 错误: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def extract_aircraft_info(self, folder_path):
        """从文件夹路径中提取飞机号信息（复用PC_BAK.py的逻辑）"""
        path_parts = folder_path.split(os.sep)

        for part in reversed(path_parts):
            if not part:
                continue

            # 首先查找完整的B-XXXX格式
            match = re.search(AIRCRAFT_PATTERN_FULL, part, re.I)
            if match:
                aircraft = match.group(0).upper()
                if not aircraft.startswith('B-'):
                    aircraft = 'B-' + aircraft[2:]
                logger.info(f"    找到完整飞机号: {aircraft}")
                return aircraft

            # 然后查找1XXX, 3XXX, 6XXX, 8XXX格式
            match = re.search(AIRCRAFT_PATTERN_SHORT, part)
            if match:
                aircraft = 'B-' + match.group(0).upper()
                logger.info(f"    找到短格式飞机号: {aircraft}")
                return aircraft

        logger.info(f"    未找到飞机号信息")
        return None

    def extract_date_info(self, folder_path):
        """从文件夹路径中提取日期信息（复用PC_BAK.py的逻辑）"""
        path_parts = folder_path.split(os.sep)
        current_date = datetime.now()
        current_year = current_date.year

        for part in reversed(path_parts):
            if not part:
                continue

            for pattern in DATE_PATTERNS:
                match = re.search(pattern, part)
                if match:
                    groups = match.groups()
                    year = None
                    month = None
                    day = None

                    # 根据不同的模式处理日期
                    if len(groups) == 3:
                        if len(groups[0]) == 4:  # yyyy-mm-dd 格式
                            year, month, day = groups
                        elif len(groups[2]) == 4:  # mm-dd-yyyy 格式
                            month, day, year = groups
                        elif pattern == r'(20\d{2})[-\/\.](\d{1,2})(\d{1,2})':  # yyyy-mmdd 格式
                            year, month_day = groups[0], groups[1] + groups[2]
                            if len(month_day) == 4:
                                month = month_day[:2]
                                day = month_day[2:]
                            elif len(month_day) == 3:
                                month = month_day[:1]
                                day = month_day[1:]
                            else:
                                continue
                        else:  # 其他格式，尝试智能识别
                            if int(groups[0]) > 12:
                                year, month, day = groups
                            else:
                                month, day, year = groups
                    elif len(groups) == 2:  # mm.dd, m.d 格式（无年份）
                        month, day = groups
                        year = str(current_year)

                    if year is None or month is None or day is None:
                        continue

                    try:
                        # 验证日期有效性
                        year = int(year)
                        month = int(month)
                        day = int(day)

                        # 确保年份是4位数
                        if year < 100:
                            if year < 80:
                                year += 2000
                            else:
                                year += 1900

                        # 验证月份和日期范围
                        if not (1 <= month <= 12 and 1 <= day <= 31):
                            continue

                        # 构建日期对象进行验证
                        test_date = datetime(year, month, day)

                        # 如果没有年份信息且构建的日期比当前日期晚，使用上一年
                        if len(groups) == 2 and test_date > current_date:
                            year = current_year - 1
                            test_date = datetime(year, month, day)
                            logger.info(f"    日期 {month:02d}.{day:02d} 比当前日期晚，使用上一年: {year}")

                        date_str = f"{year:04d}-{month:02d}-{day:02d}"
                        logger.info(f"    找到日期信息: {date_str} (从 '{part}' 提取)")
                        return date_str

                    except ValueError:
                        continue

        logger.info(f"    未找到日期信息")
        return None

    def parse_msg_file(self, folder_path):
        """解析MSG.DAT文件获取飞机号和日期信息"""
        msg_file_path = os.path.join(folder_path, 'MSG.DAT')

        if not os.path.exists(msg_file_path):
            logger.warning(f"MSG.DAT文件不存在: {msg_file_path}")
            return None, None

        try:
            # 使用PC_BAK.py中的解析逻辑
            record, total_records = self.parse_mixed_format_file(msg_file_path)

            if not record:
                logger.warning(f"MSG.DAT文件解析失败: {msg_file_path}")
                return None, None

            # 转换UTC时间为北京时间
            beijing_date, beijing_time = self.convert_utc_to_beijing(record['date'], record['time'])

            aircraft = record.get('ac')
            if not aircraft or aircraft == 'None':
                aircraft = None

            return aircraft, beijing_date

        except Exception as e:
            logger.error(f"解析MSG.DAT文件失败: {msg_file_path}, 错误: {str(e)}")
            return None, None

    def parse_mixed_format_file(self, file_path):
        """解析混合格式文件（复用PC_BAK.py的逻辑）"""
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            logger.info(f"  文件大小: {file_size_mb:.2f} MB")

            # 尝试多种编码方式
            encodings_to_try = ['utf-8', 'latin1', 'cp1252', 'utf-8-sig', 'ascii']

            best_records = []
            best_encoding = None

            for encoding in encodings_to_try:
                try:
                    records = self.extract_records_with_encoding(file_path, encoding)
                    if records and len(records) > len(best_records):
                        best_records = records
                        best_encoding = encoding
                        if len(records) > 50:
                            break
                except Exception as e:
                    continue

            # 检查是否需要启动第二种识别方式
            has_valid_aircraft = False
            has_valid_date = False

            if best_records:
                for record in best_records:
                    if record.get('ac') and record.get('ac') != 'None':
                        has_valid_aircraft = True
                    if record.get('date'):
                        has_valid_date = True
                    if has_valid_aircraft and has_valid_date:
                        break

            # 如果现有方法没有找到完整信息，启动第二种识别方式
            cc_records = []
            if not best_records or not (has_valid_aircraft and has_valid_date):
                logger.info(f"  现有方法未找到完整信息，启动CC B-关键字搜索...")
                try:
                    cc_records = self.parse_cc_format(file_path, best_encoding or 'latin1')
                    if cc_records:
                        logger.info(f"  CC格式解析找到 {len(cc_records)} 条记录")
                    else:
                        logger.info(f"  CC格式解析未找到记录")
                except Exception as e:
                    logger.warning(f"  CC格式解析失败: {str(e)}")

            # 合并两种方式的记录
            all_records = best_records + cc_records

            if not all_records:
                logger.warning(f"  两种解析方式都未找到任何有效记录")
                return None, 0

            logger.info(f"  总共找到 {len(all_records)} 条记录")

            # 按时间排序
            sorted_records = sorted(all_records, key=lambda x: x['datetime'])

            # 找到绝对最新的记录
            latest_record = sorted_records[-1]

            # 如果最新记录的飞机号为空，需要组合
            if not latest_record.get('ac') or latest_record.get('ac') == 'None':
                # 找到最新的完整记录（有飞机号的）
                complete_records = [r for r in sorted_records if r.get('ac') and r.get('ac') != 'None']
                if complete_records:
                    latest_complete_record = max(complete_records, key=lambda x: x['datetime'])
                    latest_record['ac'] = latest_complete_record['ac']
                    latest_record['ac_source'] = 'combined'
                    logger.info(f"  组合逻辑: 最新时间记录飞机号为空，使用最新完整记录的飞机号: {latest_complete_record['ac']}")
                else:
                    logger.info(f"  文件中完全没有完整飞机号记录")

            # 返回最新记录
            result = {k: v for k, v in latest_record.items() if k not in ['datetime', 'line_num', 'ac_source', 'raw_line', 'parse_method']}

            return result, len(all_records)

        except Exception as e:
            logger.error(f"  文件解析异常: {str(e)}")
            return None, 0

    def extract_records_with_encoding(self, file_path, encoding):
        """使用指定编码提取记录"""
        # 预编译正则表达式
        date_time_pattern = re.compile(r'DATE:\s*(\d{2})/(\d{2})/(\d{2})\s+TIME:\s*(\d{2}:\d{2}:\d{2})', re.I)
        ac_pattern = re.compile(r'A/C:\s*(B-[A-Z0-9]{4})', re.I)
        ac_empty_pattern = re.compile(r"A/C:\s*['\s]*", re.I)

        complete_records = []
        time_only_records = []

        line_count = 0
        current_record = {}

        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line in f:
                line_count += 1

                # 跳过二进制行
                if self.is_binary_line(line):
                    continue

                line = line.strip()
                if not line:
                    continue

                # 查找日期时间行
                date_time_match = date_time_pattern.search(line)
                if date_time_match:
                    year, month, day, time = date_time_match.groups()
                    current_record = {
                        'date': f"{year}/{month}/{day}",
                        'time': time,
                        'line_num': line_count,
                        'raw_line': line
                    }
                    continue

                # 查找飞机号行
                if current_record and 'ac' not in current_record:
                    # 检查是否有完整的飞机号
                    ac_match = ac_pattern.search(line)
                    if ac_match:
                        aircraft_code = ac_match.group(1).upper()
                        current_record['ac'] = aircraft_code

                        # 转换日期时间
                        dt = self.convert_to_datetime(
                            current_record['date'].split('/')[0],
                            current_record['date'].split('/')[1],
                            current_record['date'].split('/')[2],
                            current_record['time']
                        )

                        if dt:
                            current_record['datetime'] = dt
                            complete_records.append(current_record.copy())

                        current_record = {}
                        continue

                    # 检查是否是空的飞机号行
                    ac_empty_match = ac_empty_pattern.search(line)
                    if ac_empty_match:
                        # 转换日期时间
                        dt = self.convert_to_datetime(
                            current_record['date'].split('/')[0],
                            current_record['date'].split('/')[1],
                            current_record['date'].split('/')[2],
                            current_record['time']
                        )

                        if dt:
                            current_record['datetime'] = dt
                            current_record['ac'] = None
                            time_only_records.append(current_record.copy())

                        current_record = {}

        # 合并所有记录
        all_records = complete_records + time_only_records
        return all_records

    def is_binary_line(self, line):
        """判断一行是否为二进制数据"""
        if len(line) < 5:
            return False

        # 计算非打印字符的比例
        non_printable_count = sum(1 for c in line if ord(c) < 32 or ord(c) > 126)
        non_printable_ratio = non_printable_count / len(line)

        # 如果非打印字符超过30%，认为是二进制行
        return non_printable_ratio > 0.3

    def convert_to_datetime(self, year, month, day, time_str):
        """将日期时间字符串转换为datetime对象"""
        try:
            year_int = int(year)
            # 修正年份转换逻辑
            if year_int < 50:
                full_year = 2000 + year_int
            elif year_int < 100:
                full_year = 1900 + year_int
            else:
                full_year = year_int

            month = month.zfill(2)
            day = day.zfill(2)

            dt = datetime.strptime(f"{full_year}-{month}-{day} {time_str}", "%Y-%m-%d %H:%M:%S")
            return dt
        except Exception as e:
            return None

    def parse_cc_format(self, file_path, encoding):
        """解析CC B-格式的记录"""
        try:
            logger.info(f"    开始CC格式解析，使用编码: {encoding}")

            # CC格式的正则表达式
            cc_pattern = re.compile(r'CC\s+(B-[A-Z0-9]{4})\s+([A-Z]{3})(\d{2})\s+(\d{6})\s+([A-Z]{4})\s+([A-Z]{4})\s+(\d{4})', re.I)

            records = []
            line_count = 0

            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                for line in f:
                    line_count += 1

                    # 跳过二进制行
                    if self.is_binary_line(line):
                        continue

                    line = line.strip()
                    if not line:
                        continue

                    # 查找CC格式
                    cc_match = cc_pattern.search(line)
                    if cc_match:
                        aircraft, month_abbr, day_str, time_str, dep_airport, arr_airport, flight_suffix = cc_match.groups()

                        # 转换月份
                        month_num = self.convert_month_abbr_to_number(month_abbr)
                        if not month_num:
                            logger.warning(f"    无法识别月份: {month_abbr}")
                            continue

                        # 解析日期
                        day_int = int(day_str)
                        if day_int < 1 or day_int > 31:
                            logger.warning(f"    无效日期: {day_int}")
                            continue

                        # 智能年份判断
                        current_date = datetime.now()
                        current_year = current_date.year

                        try:
                            test_date = datetime(current_year, int(month_num), day_int)
                            if test_date > current_date:
                                current_year = current_year - 1
                                logger.info(f"    日期 {month_abbr}{day_str} 比当前日期晚，使用上一年: {current_year}")
                        except ValueError:
                            logger.warning(f"    无效日期组合: {current_year}-{month_num}-{day_int}，使用当前年份")

                        # 解析时间 (HHMMSS)
                        if len(time_str) == 6:
                            hour = time_str[:2]
                            minute = time_str[2:4]
                            second = time_str[4:6]
                            formatted_time = f"{hour}:{minute}:{second}"
                        else:
                            logger.warning(f"    时间格式错误: {time_str}")
                            continue

                        # 构建日期字符串
                        year_suffix = str(current_year)[-2:]
                        date_str = f"{year_suffix}/{month_num}/{day_int:02d}"

                        # 转换为datetime对象
                        try:
                            dt = datetime.strptime(f"{current_year}-{month_num}-{day_int:02d} {formatted_time}", "%Y-%m-%d %H:%M:%S")
                        except Exception as e:
                            logger.warning(f"    日期时间转换失败: {current_year}-{month_num}-{day_int:02d} {formatted_time}, 错误: {e}")
                            continue

                        record = {
                            'date': date_str,
                            'time': formatted_time,
                            'ac': aircraft.upper(),
                            'line_num': line_count,
                            'raw_line': line,
                            'datetime': dt,
                            'parse_method': 'cc_format'
                        }

                        records.append(record)
                        logger.info(f"    找到CC记录: {aircraft} {month_abbr}{day_str} {formatted_time}")

            logger.info(f"    CC格式解析完成，找到 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.error(f"    CC格式解析异常: {str(e)}")
            return []

    def convert_month_abbr_to_number(self, month_abbr):
        """将英文月份缩写转换为数字"""
        month_map = {
            'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
            'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
        }
        return month_map.get(month_abbr.upper(), None)

    def convert_utc_to_beijing(self, utc_date, utc_time):
        """UTC转北京时间"""
        try:
            match = re.match(r'(\d{2})[/\-\.](\d{2})[/\-\.](\d{2})', utc_date)
            if not match:
                return None, None

            year, month, day = match.groups()
            dt = self.convert_to_datetime(year, month, day, utc_time)
            if not dt:
                return None, None

            beijing_dt = dt + timedelta(hours=8)
            return beijing_dt.strftime('%Y-%m-%d'), beijing_dt.strftime('%H:%M:%S')
        except Exception as e:
            logger.warning(f"UTC转换失败: {utc_date} {utc_time} - {str(e)}")
            return None, None

    def determine_final_info(self, folder_aircraft, folder_date, msg_aircraft, msg_date):
        """综合推出最终的飞机号和日期信息"""
        # 飞机号优先级：MSG > 文件夹
        final_aircraft = msg_aircraft if msg_aircraft else folder_aircraft

        # 日期优先级：MSG > 文件夹
        final_date = msg_date if msg_date else folder_date

        return final_aircraft, final_date

    def cleanup_folder_files(self, folder_path):
        """清理文件夹中不需要的文件"""
        logger.info(f"开始清理文件夹: {folder_path}")

        try:
            for root, dirs, files in os.walk(folder_path, topdown=False):
                # 处理文件
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].upper()

                    # 如果不是需要保留的文件类型，删除
                    if file_ext not in KEEP_EXTENSIONS:
                        try:
                            os.remove(file_path)
                            logger.info(f"删除文件: {file}")
                        except Exception as e:
                            logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")

                # 处理文件夹
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)

                    # 检查文件夹是否包含DAR.DAT文件
                    if self.has_dar_dat_file(dir_path):
                        logger.info(f"保留包含DAR.DAT的文件夹: {dir_name}")
                        continue

                    # 检查文件夹后缀
                    keep_folder = False
                    for suffix in KEEP_FOLDER_SUFFIXES:
                        if dir_name.upper().endswith(suffix.upper()):
                            keep_folder = True
                            break

                    if not keep_folder:
                        try:
                            shutil.rmtree(dir_path)
                            logger.info(f"删除文件夹: {dir_name}")
                        except Exception as e:
                            logger.error(f"删除文件夹失败: {dir_path}, 错误: {str(e)}")

            logger.info(f"文件夹清理完成: {folder_path}")

        except Exception as e:
            logger.error(f"清理文件夹失败: {folder_path}, 错误: {str(e)}")

    def has_dar_dat_file(self, folder_path):
        """检查文件夹及其子文件夹是否包含DAR.DAT文件"""
        for root, dirs, files in os.walk(folder_path):
            if 'DAR.DAT' in files:
                return True
        return False

    def copy_to_data_bak(self, folder_path, aircraft, date_str):
        """复制文件夹到DATA_BAK目录"""
        try:
            logger.info(f"开始复制到DATA_BAK: {aircraft} {date_str}")

            # 解析年份和月日
            year = date_str.split('-')[0]
            month_day = date_str.replace('-', '')[4:]  # 去掉年份和横线，得到mmdd

            # 构建目标路径
            target_base = os.path.join(DATA_BAK_PATH, year, aircraft)
            target_folder = os.path.join(target_base, f"{year}-{month_day}")

            # 创建目标目录
            os.makedirs(target_folder, exist_ok=True)

            # 复制文件夹内容
            self.copy_folder_contents(folder_path, target_folder)

            logger.info(f"复制到DATA_BAK完成: {target_folder}")

        except Exception as e:
            logger.error(f"复制到DATA_BAK失败: {folder_path}, 错误: {str(e)}")

    def rename_and_copy_to_airfase(self, folder_path, aircraft, date_str):
        """重命名文件夹并复制到AirFASE目录"""
        try:
            logger.info(f"开始重命名并复制到AirFASE: {aircraft} {date_str}")

            # 构建新的文件夹名称
            date_formatted = date_str.replace('-', '')  # YYYYMMDD
            new_folder_name = f"{aircraft}_{date_formatted}0000.PC"

            # 获取父目录
            parent_dir = os.path.dirname(folder_path)
            new_folder_path = os.path.join(parent_dir, new_folder_name)

            # 重命名文件夹
            if folder_path != new_folder_path:
                if os.path.exists(new_folder_path):
                    logger.warning(f"目标文件夹已存在，先删除: {new_folder_path}")
                    shutil.rmtree(new_folder_path)

                os.rename(folder_path, new_folder_path)
                logger.info(f"文件夹重命名: {os.path.basename(folder_path)} -> {new_folder_name}")
                folder_path = new_folder_path

            # 构建AirFASE目标路径
            airfase_target = os.path.join(AIRFASE_PATH, aircraft)
            os.makedirs(airfase_target, exist_ok=True)

            final_target = os.path.join(airfase_target, new_folder_name)

            # 复制到AirFASE目录
            if os.path.exists(final_target):
                logger.warning(f"AirFASE目标文件夹已存在，先删除: {final_target}")
                shutil.rmtree(final_target)

            shutil.copytree(folder_path, final_target)
            logger.info(f"复制到AirFASE完成: {final_target}")

            # 删除原文件夹
            shutil.rmtree(folder_path)
            logger.info(f"删除原文件夹: {folder_path}")

        except Exception as e:
            logger.error(f"重命名并复制到AirFASE失败: {folder_path}, 错误: {str(e)}")

    def copy_folder_contents(self, source_folder, target_folder):
        """复制文件夹内容"""
        try:
            for item in os.listdir(source_folder):
                source_item = os.path.join(source_folder, item)
                target_item = os.path.join(target_folder, item)

                if os.path.isfile(source_item):
                    # 检查文件扩展名
                    ext = os.path.splitext(item)[1].upper()
                    if ext in KEEP_EXTENSIONS:
                        if not os.path.exists(target_item):
                            shutil.copy2(source_item, target_item)
                            logger.info(f"复制文件: {item}")
                        else:
                            logger.info(f"跳过已存在的文件: {item}")

                elif os.path.isdir(source_item):
                    # 检查文件夹后缀
                    keep_folder = False
                    for suffix in KEEP_FOLDER_SUFFIXES:
                        if item.upper().endswith(suffix.upper()):
                            keep_folder = True
                            break

                    if keep_folder or self.has_dar_dat_file(source_item):
                        if not os.path.exists(target_item):
                            shutil.copytree(source_item, target_item)
                            logger.info(f"复制文件夹: {item}")
                        else:
                            logger.info(f"跳过已存在的文件夹: {item}")

        except Exception as e:
            logger.error(f"复制文件夹内容失败: {source_folder} -> {target_folder}, 错误: {str(e)}")

    def cleanup_invalid_folders(self):
        """清理无效文件夹"""
        logger.info("开始清理无效文件夹...")

        try:
            # 递归检查所有文件夹
            self.cleanup_folder_recursive(MON_PATH)

            # 检查MON_PATH是否为空
            if self.is_folder_empty_or_no_dar(MON_PATH):
                logger.info("MON_PATH中没有包含DAR.DAT的文件夹，清空所有内容")
                for item in os.listdir(MON_PATH):
                    item_path = os.path.join(MON_PATH, item)
                    try:
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        logger.info(f"删除: {item}")
                    except Exception as e:
                        logger.error(f"删除失败: {item_path}, 错误: {str(e)}")

            logger.info("无效文件夹清理完成")

        except Exception as e:
            logger.error(f"清理无效文件夹失败: {str(e)}")

    def cleanup_folder_recursive(self, folder_path):
        """递归清理文件夹"""
        try:
            items_to_remove = []

            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)

                if os.path.isdir(item_path):
                    # 递归处理子文件夹
                    self.cleanup_folder_recursive(item_path)

                    # 检查子文件夹是否包含DAR.DAT
                    if not self.has_dar_dat_file(item_path):
                        items_to_remove.append(item_path)

            # 删除不包含DAR.DAT的文件夹
            for item_path in items_to_remove:
                try:
                    shutil.rmtree(item_path)
                    logger.info(f"删除无效文件夹: {item_path}")
                except Exception as e:
                    logger.error(f"删除无效文件夹失败: {item_path}, 错误: {str(e)}")

        except Exception as e:
            logger.error(f"递归清理文件夹失败: {folder_path}, 错误: {str(e)}")

    def is_folder_empty_or_no_dar(self, folder_path):
        """检查文件夹是否为空或不包含DAR.DAT文件"""
        try:
            if not os.path.exists(folder_path):
                return True

            # 检查是否有任何包含DAR.DAT的文件夹
            for root, dirs, files in os.walk(folder_path):
                if 'DAR.DAT' in files:
                    return False

            return True

        except Exception as e:
            logger.error(f"检查文件夹状态失败: {folder_path}, 错误: {str(e)}")
            return True

class FIMVietnamGUI:
    """FIM Vietnam 主界面 - 集成版本"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_styles()
        self.create_widgets()

        # 监控相关
        self.monitor = SystemMonitor(gui_callback=self.update_gui_callback)
        self.observer = None
        self.monitoring_active = False

        # 托盘相关（可选）
        self.tray_icon = None
        self.is_minimized_to_tray = False
        if TRAY_AVAILABLE:
            self.setup_tray()

        # 启动监控
        self.start_monitoring()

        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind('<Unmap>', self.on_minimize)

        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("FIM Vietnam 监控中心")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)

        # 设置图标
        try:
            if os.path.exists("app.ico"):
                self.root.iconbitmap("app.ico")
        except:
            pass

        self.root.configure(bg='#f0f2f5')

    def setup_variables(self):
        """设置变量"""
        self.current_task = tk.StringVar(value="正在启动...")
        self.uptime = tk.StringVar(value="00:00:00")
        self.processed_files = tk.IntVar(value=0)
        self.processed_folders = tk.IntVar(value=0)
        self.error_count = tk.IntVar(value=0)
        self.last_activity = tk.StringVar(value="刚刚")
        self.start_time = datetime.now()

    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')

        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'),
                       background='#f0f2f5', foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'),
                       background='#f0f2f5', foreground='#34495e')
        style.configure('Value.TLabel', font=('Microsoft YaHei', 11, 'bold'),
                       background='#f0f2f5', foreground='#2980b9')
        style.configure('Action.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(20, 10))

    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.create_header(main_frame)
        # 状态卡片区域
        self.create_status_cards(main_frame)
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        # 详细信息区域
        self.create_details_section(main_frame)

    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk.Label(header_frame, text="🛡️ FIM Vietnam 监控中心",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        self.status_indicator = ttk.Label(header_frame, text="●",
                                         font=('Microsoft YaHei', 20),
                                         foreground='#27ae60')
        self.status_indicator.pack(side=tk.RIGHT, padx=(10, 0))

        self.status_text = ttk.Label(header_frame, text="监控运行中",
                                    style='Header.TLabel')
        self.status_text.pack(side=tk.RIGHT)

    def create_status_cards(self, parent):
        """创建状态卡片"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, pady=(0, 20))

        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)

        # 运行时间卡片
        self.create_card(cards_frame, "⏱️ 运行时间", self.uptime, 0, 0)

        # 处理统计卡片
        self.stats_card = self.create_card(cards_frame, "📊 处理统计", "", 0, 1)
        self.stats_label = ttk.Label(self.stats_card, text="文件: 0 | 文件夹: 0",
                                    style='Value.TLabel')
        self.stats_label.pack()

        # 错误统计卡片
        self.create_card(cards_frame, "⚠️ 错误统计", self.error_count, 0, 2)

        # 当前任务卡片
        self.create_card(cards_frame, "🔄 当前任务", self.current_task, 1, 0, columnspan=2)

        # 最后活动卡片
        self.create_card(cards_frame, "📅 最后活动", self.last_activity, 1, 2)

    def create_card(self, parent, title, value_var, row, col, columnspan=1):
        """创建单个状态卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="15")
        card_frame.grid(row=row, column=col, columnspan=columnspan,
                       sticky="ew", padx=5, pady=5)

        if isinstance(value_var, (tk.StringVar, tk.IntVar)):
            value_label = ttk.Label(card_frame, textvariable=value_var,
                                   style='Value.TLabel')
            value_label.pack()
        elif value_var:
            value_label = ttk.Label(card_frame, text=str(value_var),
                                   style='Value.TLabel')
            value_label.pack()

        return card_frame

    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 20))

        self.reprocess_btn = ttk.Button(button_frame, text="🔄 重新处理现有数据",
                                       command=self.reprocess_data,
                                       style='Action.TButton')
        self.reprocess_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.log_btn = ttk.Button(button_frame, text="📋 查看日志",
                                 command=self.view_logs,
                                 style='Action.TButton')
        self.log_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.folder_btn = ttk.Button(button_frame, text="📁 打开监控目录",
                                    command=self.open_monitor_folder,
                                    style='Action.TButton')
        self.folder_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.exit_btn = ttk.Button(button_frame, text="❌ 退出程序",
                                  command=self.exit_application,
                                  style='Action.TButton')
        self.exit_btn.pack(side=tk.RIGHT)

    def create_details_section(self, parent):
        """创建详细信息区域"""
        details_frame = ttk.LabelFrame(parent, text="📈 实时监控日志", padding="15")
        details_frame.pack(fill=tk.BOTH, expand=True)

        self.details_text = tk.Text(details_frame, height=10, wrap=tk.WORD,
                                   font=('Consolas', 9), bg='#2c3e50', fg='#ecf0f1',
                                   insertbackground='#ecf0f1')

        scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL,
                                 command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=scrollbar.set)

        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.add_log_message("FIM Vietnam 集成版本启动")
        self.add_log_message("正在初始化文件监控...")

    def add_log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.details_text.insert(tk.END, formatted_message)
        self.details_text.see(tk.END)

        # 限制行数
        lines = self.details_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.details_text.delete("1.0", "10.0")

    def setup_tray(self):
        """设置系统托盘（可选功能）"""
        if not TRAY_AVAILABLE:
            return

        try:
            # 使用原始图标
            icon_path = "app_green.png" if os.path.exists("app_green.png") else "app.ico"

            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示主窗口", self.show_window),
                pystray.MenuItem("重新处理数据", self.reprocess_data),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("退出程序", self.exit_application)
            )

            # 创建托盘图标
            image = Image.open(icon_path)
            self.tray_icon = pystray.Icon("FIM_Vietnam", image,
                                         "FIM Vietnam 监控中", menu)

        except Exception as e:
            logger.error(f"托盘设置失败: {str(e)}")
            self.tray_icon = None

    def update_gui_callback(self, action, files, folders, errors):
        """GUI更新回调函数"""
        try:
            # 使用after方法确保在主线程中更新GUI
            self.root.after(0, self._update_gui_safe, action, files, folders, errors)
        except Exception as e:
            logger.error(f"GUI回调更新失败: {str(e)}")

    def _update_gui_safe(self, action, files, folders, errors):
        """安全的GUI更新方法"""
        try:
            self.processed_files.set(files)
            self.processed_folders.set(folders)
            self.error_count.set(errors)
            self.stats_label.configure(text=f"文件: {files} | 文件夹: {folders}")
            self.current_task.set(action)
            self.last_activity.set("刚刚")

            # 添加日志消息
            self.add_log_message(f"{action} - 文件:{files} 文件夹:{folders}")

        except Exception as e:
            logger.error(f"GUI安全更新失败: {str(e)}")

    def start_monitoring(self):
        """启动文件监控"""
        if not WATCHDOG_AVAILABLE:
            self.add_log_message("错误：watchdog库未安装，无法启动文件监控")
            self.current_task.set("错误：缺少依赖库")
            return

        try:
            self.add_log_message("正在启动文件监控...")

            # 检查监控路径
            if not os.path.exists(MON_PATH):
                self.add_log_message(f"错误：监控路径不存在 - {MON_PATH}")
                self.current_task.set("错误：监控路径不存在")
                return

            # 创建目标路径
            os.makedirs(DATA_BAK_PATH, exist_ok=True)
            os.makedirs(AIRFASE_PATH, exist_ok=True)

            # 更新状态
            self.monitor.update_status("启动监控", {
                "mon_path": MON_PATH,
                "data_bak_path": DATA_BAK_PATH,
                "airfase_path": AIRFASE_PATH,
                "target_aircraft": TARGET_AIRCRAFT
            })

            # 首次处理现有数据
            self.add_log_message("处理现有数据...")
            self.current_task.set("处理现有数据")

            # 在后台线程中处理现有数据
            threading.Thread(target=self.process_existing_data, daemon=True).start()

            # 设置文件系统监控
            event_handler = VietnamFileHandler(self.monitor)
            self.observer = Observer()
            self.observer.schedule(event_handler, MON_PATH, recursive=True)
            self.observer.start()

            self.monitoring_active = True
            self.add_log_message("文件监控已启动")
            self.current_task.set("监控中 - 等待新文件")

            logger.info("FIM Vietnam 集成版本启动完成")

        except Exception as e:
            self.add_log_message(f"启动监控失败: {str(e)}")
            self.current_task.set("启动失败")
            logger.error(f"启动监控失败: {str(e)}")

    def process_existing_data(self):
        """处理现有数据（后台线程）"""
        try:
            processor = VietnamDataProcessor(self.monitor)
            processor.process_new_data()

            # 更新界面
            self.root.after(0, lambda: self.current_task.set("监控中 - 等待新文件"))
            self.root.after(0, lambda: self.add_log_message("现有数据处理完成"))

        except Exception as e:
            self.root.after(0, lambda: self.add_log_message(f"处理现有数据失败: {str(e)}"))
            logger.error(f"处理现有数据失败: {str(e)}")

    def update_loop(self):
        """状态更新循环"""
        while True:
            try:
                # 更新运行时间
                uptime_delta = datetime.now() - self.start_time
                uptime_str = str(uptime_delta).split('.')[0]
                self.root.after(0, lambda: self.uptime.set(uptime_str))

                # 更新最后活动时间
                if hasattr(self.monitor, 'last_activity'):
                    last_time = self.monitor.last_activity
                    time_diff = datetime.now() - last_time
                    if time_diff.total_seconds() < 60:
                        activity_text = "刚刚"
                    elif time_diff.total_seconds() < 3600:
                        minutes = int(time_diff.total_seconds() // 60)
                        activity_text = f"{minutes}分钟前"
                    else:
                        hours = int(time_diff.total_seconds() // 3600)
                        activity_text = f"{hours}小时前"

                    self.root.after(0, lambda: self.last_activity.set(activity_text))

                time.sleep(2)  # 每2秒更新一次

            except Exception as e:
                logger.error(f"更新循环错误: {str(e)}")
                time.sleep(5)

    def reprocess_data(self):
        """重新处理现有数据"""
        self.add_log_message("开始重新处理现有数据...")
        self.current_task.set("重新处理数据中...")
        threading.Thread(target=self.process_existing_data, daemon=True).start()

    def view_logs(self):
        """查看日志"""
        try:
            log_dir = "logs"
            if os.path.exists(log_dir):
                os.startfile(log_dir)
            else:
                messagebox.showinfo("提示", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开日志目录失败: {str(e)}")

    def open_monitor_folder(self):
        """打开监控目录"""
        try:
            if os.path.exists(MON_PATH):
                os.startfile(MON_PATH)
            else:
                messagebox.showwarning("警告", f"监控目录不存在: {MON_PATH}")
        except Exception as e:
            messagebox.showerror("错误", f"打开监控目录失败: {str(e)}")

    def on_minimize(self, event):
        """窗口最小化事件"""
        if event.widget == self.root:
            files = self.processed_files.get()
            folders = self.processed_folders.get()
            self.root.title(f"运行中 | 文件:{files} 文件夹:{folders}")

    def on_closing(self):
        """窗口关闭事件"""
        if TRAY_AVAILABLE and self.tray_icon:
            # 最小化到系统托盘
            self.root.withdraw()
            self.is_minimized_to_tray = True
            self.start_tray()
            self.add_log_message("程序已最小化到系统托盘")
        else:
            # 直接退出
            self.exit_application()

    def start_tray(self):
        """启动系统托盘"""
        if self.tray_icon:
            threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.is_minimized_to_tray = False
        self.root.title("FIM Vietnam 监控中心")
        if self.tray_icon:
            self.tray_icon.stop()

    def exit_application(self, icon=None, item=None):
        """退出应用程序"""
        self.add_log_message("正在退出程序...")

        # 停止文件监控
        if self.observer:
            try:
                self.observer.stop()
                self.observer.join(timeout=5)
                self.add_log_message("文件监控已停止")
            except:
                pass

        # 停止托盘
        if self.tray_icon:
            self.tray_icon.stop()

        # 更新状态
        if self.monitor:
            self.monitor.update_status("程序退出", {"exit_time": datetime.now().isoformat()})

        logger.info("FIM Vietnam 程序退出")

        # 退出程序
        self.root.quit()
        self.root.destroy()

        # 强制退出所有线程
        os._exit(0)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def cleanup_old_logs():
    """清理旧日志文件"""
    try:
        log_dir = "logs"
        if not os.path.exists(log_dir):
            return

        cutoff_date = datetime.now() - timedelta(days=7)

        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))

                if file_time < cutoff_date:
                    try:
                        os.remove(file_path)
                        logger.info(f"清理旧日志文件: {filename}")
                    except Exception as e:
                        logger.warning(f"清理日志文件失败: {filename}, {str(e)}")

    except Exception as e:
        logger.warning(f"清理日志文件时发生错误: {str(e)}")

def main():
    """主程序入口"""
    print("FIM Vietnam 集成版本")
    print("="*50)

    # 检查依赖
    missing_deps = []
    if not WATCHDOG_AVAILABLE:
        missing_deps.append("watchdog")

    if missing_deps:
        print("⚠️ 缺少以下依赖库:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_deps)}")

        # 询问是否继续
        try:
            choice = input("\n是否继续运行（部分功能可能不可用）？(y/N): ")
            if choice.lower() != 'y':
                return
        except KeyboardInterrupt:
            return

    # 设置进程优先级
    try:
        import psutil
        p = psutil.Process()
        p.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if os.name == 'nt' else 10)
        print("✅ 已设置进程为低优先级")
    except ImportError:
        print("ℹ️ psutil未安装，无法设置进程优先级")
    except Exception as e:
        print(f"⚠️ 设置进程优先级失败: {str(e)}")

    # 显示配置信息
    print(f"\n📁 监控路径: {MON_PATH}")
    print(f"📁 备份路径: {DATA_BAK_PATH}")
    print(f"📁 AirFASE路径: {AIRFASE_PATH}")
    print(f"✈️ 目标飞机: {', '.join(TARGET_AIRCRAFT)}")

    if not TRAY_AVAILABLE:
        print("ℹ️ 系统托盘功能不可用，程序将以普通窗口模式运行")

    print("\n🚀 启动GUI界面...")

    try:
        # 启动GUI
        app = FIMVietnamGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {str(e)}")
        logger.error(f"程序运行异常: {str(e)}")
    finally:
        print("👋 程序已退出")

if __name__ == "__main__":
    main()