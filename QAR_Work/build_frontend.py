"""
构建FIM Vietnam前端版本
纯前端运行，不使用后台服务
"""

import os
import sys
import subprocess
import shutil

def build_frontend():
    """构建前端版本"""
    print("🔨 构建FIM Vietnam前端版本...")
    
    # 检查原始图标是否存在
    if not os.path.exists("app.ico"):
        print("⚠️ 找不到原始图标文件 app.ico")
        return False
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                           # 单文件
        "--windowed",                          # 无控制台窗口
        "--name=FIM_Vietnam",                  # 可执行文件名
        "--icon=app.ico",                      # 主图标（使用原始图标）
        "--add-data=app.ico;.",                # 包含主图标
        "--add-data=app_green.png;.",          # 包含绿色图标
        "--add-data=app_red.png;.",            # 包含红色图标
        "--hidden-import=rarfile",             # 隐式导入
        "--hidden-import=py7zr",
        "--hidden-import=watchdog",
        "--hidden-import=psutil",
        "--hidden-import=pystray",
        "--hidden-import=PIL",
        "--collect-all=pystray",               # 收集所有相关文件
        "--collect-all=PIL",
        "FIM_Vietnam_Frontend.py"              # 源文件
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 前端版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 前端版本构建失败")
        print(f"错误信息: {e.stderr}")
        return False

def create_deployment_package():
    """创建部署包"""
    print("📦 创建部署包...")
    
    # 确保dist目录存在
    if not os.path.exists("dist"):
        print("❌ dist目录不存在")
        return False
    
    # 复制必要文件到dist目录（使用原始图标）
    files_to_copy = [
        "app.ico",
        "app_green.png",
        "app_red.png"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, "dist/")
            print(f"复制文件: {file}")
    
    # 创建启动脚本
    create_start_script()
    
    # 创建使用说明
    create_readme()
    
    print("✅ 部署包创建完成")
    return True

def create_start_script():
    """创建启动脚本"""
    start_script = """@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    FIM Vietnam 自动化监控程序
echo ========================================
echo.
echo 正在启动监控程序...
echo 程序启动后将自动开始文件监控
echo 关闭程序窗口即完全退出所有进程
echo.

start "" "FIM_Vietnam.exe"

echo 程序已启动！
echo 如需退出，请关闭程序窗口
echo.
timeout /t 3 >nul
"""
    
    with open("dist/启动FIM_Vietnam.bat", "w", encoding="gbk") as f:
        f.write(start_script)
    
    print("创建启动脚本: 启动FIM_Vietnam.bat")

def create_readme():
    """创建使用说明"""
    readme_content = """# FIM Vietnam 前端版本 - 使用说明

## 🚀 快速开始

### 启动程序
- **方式一**: 双击运行 `FIM_Vietnam.exe`
- **方式二**: 双击运行 `启动FIM_Vietnam.bat`

### 程序特点
- ✅ **即开即用**: 程序启动即开始监控
- ✅ **完全退出**: 关闭窗口即完全退出所有进程
- ✅ **无后台服务**: 不使用Windows服务，避免权限问题
- ✅ **实时监控**: 图形界面显示所有状态信息

## 📊 界面功能

### 状态显示
- **🛡️ 监控状态**: 绿色●表示正在监控
- **⏱️ 运行时间**: 程序持续运行时间
- **📊 处理统计**: 已处理的文件和文件夹数量
- **⚠️ 错误统计**: 处理过程中的错误次数
- **🔄 当前任务**: 当前正在执行的操作
- **📅 最后活动**: 最近一次处理文件的时间

### 控制功能
- **🔄 重新处理现有数据**: 手动触发处理现有文件
- **📋 查看日志**: 打开日志目录查看详细记录
- **📁 打开监控目录**: 快速打开监控文件夹
- **❌ 退出程序**: 安全退出程序

### 系统托盘
- **最小化**: 点击关闭按钮最小化到系统托盘
- **绿色图标**: 程序正常运行
- **右键菜单**: 显示窗口、重新处理、退出程序

## ⚙️ 监控配置

- **监控路径**: Z:\\Vietnam\\PC卡
- **备份路径**: Z:\\DATA_BAK\\FDIMU_PC
- **AirFASE路径**: D:\\AirFASE\\FIMRoot\\ARJ21
- **目标飞机**: B-652G, B-656E

## 📝 工作流程

1. **启动监控**: 程序启动时自动开始监控
2. **处理现有数据**: 首次启动时处理已存在的文件
3. **实时监控**: 监控新增文件并自动处理
4. **状态显示**: 界面实时显示处理状态和统计
5. **完全退出**: 关闭程序时停止所有监控和处理

## 🔧 注意事项

1. **路径要求**: 确保监控路径存在且可访问
2. **解压工具**: 需要安装7-Zip或WinRAR处理压缩文件
3. **权限要求**: 确保对目标路径有写入权限
4. **单实例运行**: 同时只能运行一个程序实例

## 📞 故障排除

1. **程序无法启动**: 检查是否安装了7-Zip
2. **监控路径错误**: 确认Z:\\Vietnam\\PC卡路径存在
3. **处理失败**: 查看日志目录了解详细错误信息
4. **界面异常**: 重启程序即可恢复

## 📁 文件说明

- `FIM_Vietnam.exe` - 主程序（约35MB）
- `启动FIM_Vietnam.bat` - 启动脚本
- `app_*.ico` - 程序图标文件
- `logs/` - 日志目录（自动创建）
- `status.json` - 状态文件（自动创建）

---

**版本**: 前端版 v1.0  
**构建时间**: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """  
**特点**: 纯前端运行，无后台服务
"""
    
    with open("dist/使用说明.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("FIM Vietnam 前端版本构建工具")
    print("="*50)
    
    # 检查源文件
    if not os.path.exists("FIM_Vietnam_Frontend.py"):
        print("❌ 找不到源文件: FIM_Vietnam_Frontend.py")
        return
    
    if not os.path.exists("FIM_Vietnam.py"):
        print("❌ 找不到依赖文件: FIM_Vietnam.py")
        return
    
    # 执行构建步骤
    steps = [
        ("构建前端版本", build_frontend),
        ("创建部署包", create_deployment_package)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，构建中止")
            return
    
    print("\n🎉 构建完成！")
    print("\n📦 部署包内容:")
    print("- FIM_Vietnam.exe           (前端主程序)")
    print("- 启动FIM_Vietnam.bat       (启动脚本)")
    print("- app_*.ico                 (程序图标)")
    print("- 使用说明.md               (详细说明)")
    
    print("\n📋 使用方法:")
    print("1. 将dist目录下的所有文件复制到目标电脑")
    print("2. 双击运行 FIM_Vietnam.exe")
    print("3. 程序启动即开始监控，关闭即完全退出")
    
    print("\n✨ 特点:")
    print("- 无需管理员权限")
    print("- 无后台服务")
    print("- 即开即用")
    print("- 完全退出")

if __name__ == "__main__":
    from datetime import datetime
    main()
    input("\n按回车键退出...")
