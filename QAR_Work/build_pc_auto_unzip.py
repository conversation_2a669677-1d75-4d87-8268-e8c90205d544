"""
PC_AUTO_UNZIP 打包脚本
将PC_Auto_Unzip.py封装成exe文件
"""

import os
import sys
import subprocess
import shutil

def build_pc_auto_unzip():
    """构建PC_AUTO_UNZIP可执行文件"""
    print("🔨 构建PC_AUTO_UNZIP可执行文件...")
    
    # 检查源文件
    if not os.path.exists("PC_Auto_Unzip.py"):
        print("❌ 找不到源文件: PC_Auto_Unzip.py")
        return False
    
    # 检查图标文件
    icon_file = "app.ico"
    if not os.path.exists(icon_file):
        print(f"⚠️ 找不到图标文件: {icon_file}")
        icon_file = None
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                           # 单文件
        "--windowed",                          # 无控制台窗口
        "--name=PC_AUTO_UNZIP",                # 可执行文件名
        "--hidden-import=tkinter",             # 确保包含tkinter
        "--hidden-import=tkinter.ttk",
        "--hidden-import=watchdog",
        "--hidden-import=watchdog.observers",
        "--hidden-import=watchdog.events",
        "--hidden-import=rarfile",
        "--hidden-import=py7zr",
        "--hidden-import=zipfile",
        "--hidden-import=shutil",
        "--hidden-import=threading",
        "--hidden-import=logging",
        "--hidden-import=json",
        "--hidden-import=re",
        "--hidden-import=pathlib",
        "--collect-all=tkinter",               # 收集tkinter所有文件
        "--collect-all=watchdog",
        "PC_Auto_Unzip.py"                     # 源文件
    ]
    
    # 如果有图标文件，添加到命令中
    if icon_file:
        cmd.insert(-1, f"--icon={icon_file}")
        cmd.insert(-1, f"--add-data={icon_file};.")
    
    try:
        print("正在执行打包命令...")
        print("命令:", " ".join(cmd))
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PC_AUTO_UNZIP构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ PC_AUTO_UNZIP构建失败")
        print(f"错误信息: {e.stderr}")
        return False

def create_deployment_package():
    """创建部署包"""
    print("📦 创建部署包...")
    
    if not os.path.exists("dist"):
        print("❌ dist目录不存在")
        return False
    
    # 复制必要文件到dist目录
    files_to_copy = [
        "app.ico"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, "dist/")
            print(f"复制文件: {file}")
    
    # 创建启动脚本
    create_start_script()
    
    # 创建使用说明
    create_readme()
    
    print("✅ 部署包创建完成")
    return True

def create_start_script():
    """创建启动脚本"""
    start_script = """@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    PC_AUTO_UNZIP 文件处理工具
echo ========================================
echo.
echo 正在启动程序...
echo.
echo 功能说明：
echo - 监控Y盘文件变化
echo - 自动解压压缩文件
echo - 提取飞机号和日期信息
echo - 复制到指定目录并重命名
echo.
echo 使用方法：
echo 1. 程序启动后默认为暂停状态
echo 2. 点击"开始"按钮启动监控
echo 3. 点击"暂停"按钮停止监控
echo.

start "" "PC_AUTO_UNZIP.exe"

echo 程序已启动！
echo.
timeout /t 3 >nul
"""
    
    with open("dist/启动PC_AUTO_UNZIP.bat", "w", encoding="gbk") as f:
        f.write(start_script)
    
    print("创建启动脚本: 启动PC_AUTO_UNZIP.bat")

def create_readme():
    """创建使用说明"""
    readme_content = """# PC_AUTO_UNZIP 使用说明

## 🚀 快速开始

### 启动程序
- **方式一**: 双击运行 `PC_AUTO_UNZIP.exe`
- **方式二**: 双击运行 `启动PC_AUTO_UNZIP.bat`

## 📋 功能说明

### 🎯 主要功能
1. **文件监控**: 持续监控Y盘指定路径的文件变化
2. **智能日期范围**: 自动计算前7天到后3天的监控范围
3. **压缩文件解压**: 支持ZIP、RAR、7Z格式自动解压
4. **飞机号提取**: 从文件夹名和MSG.DAT文件提取飞机号
5. **日期信息提取**: 支持多种日期格式智能识别
6. **双路径复制**: 复制到DATA_BAK和QAR_PC目录
7. **文件重命名**: 按规则重命名为标准格式

### 🎮 操作方式
- **默认状态**: 程序启动时为暂停状态
- **开始监控**: 点击"▶️ 开始"按钮启动文件监控
- **暂停监控**: 点击"⏸️ 暂停"按钮停止监控
- **查看日志**: 点击"📋 查看日志"打开日志目录
- **打开目录**: 点击"📁 打开监控目录"访问Y盘

## ⚙️ 配置信息

### 📁 路径配置
- **监控路径**: Y: (主监控目录)
- **临时路径**: Y:\\Data_Monitor (临时处理目录)
- **DATA_BAK路径**: Z:\\DATA_BAK\\FDIMU_PC (备份目录)
- **QAR_PC路径**: Z:\\DATA_BAK\\QAR_PC (QAR处理目录)
- **日志目录**: D:\\AUTO_QAR (日志存储目录)

### 📅 监控范围
- **日期范围**: 当前日期前7天到后3天
- **自动更新**: 跨日期时自动刷新范围
- **实时显示**: 界面显示当前有效监控范围

## 🔧 处理流程

### 📂 文件扫描
1. 扫描Y盘下yyyy-mm格式的年月目录
2. 进入yyyymmdd格式的日期目录
3. 检查QAR_PC.log判断处理状态
4. 增量复制新文件到临时目录

### 📦 文件解压
1. 扫描临时目录中的压缩文件
2. 支持ZIP、RAR、7Z格式解压
3. 解压后删除原压缩文件
4. 保留目录结构

### ✈️ 信息提取
1. 从文件夹名提取飞机号和日期
2. 从MSG.DAT文件提取补充信息
3. 综合判断最终的飞机号和日期
4. 验证数据文件夹有效性

### 📋 文件复制
1. 复制到Z:\\DATA_BAK\\FDIMU_PC (按原结构)
2. 复制到Z:\\DATA_BAK\\QAR_PC (重命名格式)
3. 重命名为: B-xxxx_yyyymmdd0000.pc
4. 记录详细处理日志

## 📊 界面说明

### 🖥️ 主界面
- **标题区域**: 程序名称和运行状态指示器
- **配置信息**: 显示所有路径配置和监控范围
- **状态卡片**: 运行时间、处理统计、错误计数
- **控制按钮**: 开始/暂停、查看日志、打开目录
- **实时日志**: 滚动显示处理过程和状态

### 🎯 状态指示
- **🟢 绿色圆点**: 监控运行中
- **🔴 红色圆点**: 监控已暂停
- **状态文本**: 显示当前运行状态
- **实时更新**: 处理统计实时刷新

## 📝 日志系统

### 📁 日志文件
- **本地日志**: 每个处理目录的QAR_PC.log
- **全局日志**: D:\\AUTO_QAR\\QAR_PC.log
- **程序日志**: D:\\AUTO_QAR\\PC_UNZIP.log
- **详细记录**: 时间、文件名、类型、结果

### 🔍 日志内容
- 文件复制操作记录
- 压缩文件解压记录
- 飞机号和日期提取记录
- 错误和异常处理记录

## 🔧 故障排除

### ❌ 常见问题
1. **监控路径不存在**: 检查Y盘是否可访问
2. **解压失败**: 确认安装了7-Zip或WinRAR
3. **权限错误**: 确保对目标路径有写入权限
4. **依赖库缺失**: 程序会自动检测并提示

### 📞 技术支持
- 查看日志文件了解详细错误信息
- 检查配置路径是否正确
- 确认所需的解压软件已安装

## 📦 文件说明

- `PC_AUTO_UNZIP.exe` - 主程序文件
- `启动PC_AUTO_UNZIP.bat` - 启动脚本
- `app.ico` - 程序图标
- `使用说明.md` - 本说明文档

---

**版本**: v1.0  
**构建时间**: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """  
**特点**: 智能文件监控和处理工具
"""
    
    with open("dist/使用说明.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("PC_AUTO_UNZIP 打包工具")
    print("="*50)
    
    # 检查PyInstaller
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return
    
    # 执行构建步骤
    steps = [
        ("构建可执行文件", build_pc_auto_unzip),
        ("创建部署包", create_deployment_package)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，构建中止")
            return
    
    print("\n🎉 打包完成！")
    print("\n📦 部署包内容:")
    print("- PC_AUTO_UNZIP.exe         (主程序文件)")
    print("- 启动PC_AUTO_UNZIP.bat     (启动脚本)")
    print("- app.ico                   (程序图标)")
    print("- 使用说明.md               (详细说明)")
    
    print("\n✨ 程序特点:")
    print("- 智能文件监控和处理")
    print("- 支持多种压缩格式解压")
    print("- 自动提取飞机号和日期")
    print("- 双路径复制和重命名")
    print("- 现代化GUI界面")
    
    print("\n📋 使用方法:")
    print("1. 将dist目录下的所有文件复制到目标电脑")
    print("2. 双击运行 PC_AUTO_UNZIP.exe")
    print("3. 点击'开始'按钮启动监控")

if __name__ == "__main__":
    from datetime import datetime
    main()
    input("\n按回车键退出...")
