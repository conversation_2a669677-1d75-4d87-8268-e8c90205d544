"""
构建FIM Vietnam最终集成版本
解决所有问题的单文件版本
"""

import os
import sys
import subprocess
import shutil

def build_final_version():
    """构建最终集成版本"""
    print("🔨 构建FIM Vietnam最终集成版本...")
    
    # 检查源文件
    if not os.path.exists("FIM_Vietnam.py"):
        print("❌ 找不到源文件: FIM_Vietnam.py")
        return False
    
    # 检查图标文件
    if not os.path.exists("app.ico"):
        print("⚠️ 找不到图标文件: app.ico")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                           # 单文件
        "--windowed",                          # 无控制台窗口
        "--name=FIM_Vietnam_Final",            # 可执行文件名
        "--icon=app.ico",                      # 主图标
        "--add-data=app.ico;.",                # 包含主图标
        "--hidden-import=tkinter",             # 确保包含tkinter
        "--hidden-import=tkinter.ttk",
        "--hidden-import=watchdog",
        "--hidden-import=watchdog.observers",
        "--hidden-import=watchdog.events",
        "--hidden-import=rarfile",
        "--hidden-import=py7zr",
        "--hidden-import=pystray",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--collect-all=tkinter",               # 收集tkinter所有文件
        "--collect-all=watchdog",
        "FIM_Vietnam.py"                       # 源文件
    ]
    
    # 如果有绿色和红色图标，也包含进去
    if os.path.exists("app_green.png"):
        cmd.insert(-1, "--add-data=app_green.png;.")
    if os.path.exists("app_red.png"):
        cmd.insert(-1, "--add-data=app_red.png;.")
    
    try:
        print("正在执行打包命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 最终版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 最终版本构建失败")
        print(f"错误信息: {e.stderr}")
        return False

def create_deployment_package():
    """创建部署包"""
    print("📦 创建最终部署包...")
    
    if not os.path.exists("dist"):
        print("❌ dist目录不存在")
        return False
    
    # 复制必要文件到dist目录
    files_to_copy = [
        "app.ico",
        "app_green.png", 
        "app_red.png"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, "dist/")
            print(f"复制文件: {file}")
    
    # 创建启动脚本
    create_start_script()
    
    # 创建使用说明
    create_final_readme()
    
    print("✅ 最终部署包创建完成")
    return True

def create_start_script():
    """创建启动脚本"""
    start_script = """@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    FIM Vietnam 最终集成版本
echo ========================================
echo.
echo 正在启动程序...
echo 程序启动后将自动开始文件监控
echo.
echo 特点：
echo - 集成所有功能到单个文件
echo - 支持GUI界面和系统托盘
echo - 完全退出时清理所有进程
echo - 解决Windows Server兼容性问题
echo.

start "" "FIM_Vietnam_Final.exe"

echo 程序已启动！
echo.
timeout /t 3 >nul
"""
    
    with open("dist/启动FIM_Vietnam.bat", "w", encoding="gbk") as f:
        f.write(start_script)
    
    print("创建启动脚本: 启动FIM_Vietnam.bat")

def create_final_readme():
    """创建最终使用说明"""
    readme_content = """# FIM Vietnam 最终集成版本

## 🎉 问题解决状态

### ✅ 已解决的问题
1. **双击托盘图标问题** - 改进了托盘实现，增强兼容性
2. **Windows Server 2025兼容性** - 优化了系统托盘权限处理
3. **后台服务问题** - 完全移除服务依赖，纯前端运行
4. **代码整合** - 所有功能集成到单个FIM_Vietnam.py文件
5. **界面更新问题** - 实现了实时GUI更新回调机制

## 🚀 最终版本特点

### 💡 **单文件集成**
- ✅ 所有功能集成到一个Python文件
- ✅ 所有依赖自动检测和处理
- ✅ 可选功能优雅降级（如托盘功能）

### 🖥️ **GUI界面**
- ✅ 实时状态显示和更新
- ✅ 监控信息实时刷新
- ✅ 处理进度实时显示
- ✅ 错误统计实时更新

### 📱 **系统托盘**
- ✅ 兼容性增强，支持Windows Server
- ✅ 双击恢复窗口（如果支持）
- ✅ 右键菜单完整功能
- ✅ 优雅降级到普通窗口模式

### 🔧 **进程管理**
- ✅ 完全前端运行，无后台服务
- ✅ 退出时完全清理所有进程
- ✅ 使用os._exit(0)确保彻底退出

## 📋 使用方法

### 🚀 启动程序
```bash
# 方式一：直接运行
双击 FIM_Vietnam_Final.exe

# 方式二：使用启动脚本
双击 启动FIM_Vietnam.bat
```

### 🎯 操作流程
1. **启动程序** → 自动开始文件监控
2. **查看状态** → 界面实时显示处理信息
3. **最小化** → 点击最小化按钮到任务栏
4. **隐藏到托盘** → 点击关闭按钮(X)到系统托盘
5. **恢复窗口** → 双击托盘图标或右键菜单
6. **完全退出** → 右键托盘图标选择退出

## ⚙️ 监控配置

- **监控路径**: Z:\\Vietnam\\PC卡
- **备份路径**: Z:\\DATA_BAK\\FDIMU_PC
- **AirFASE路径**: D:\\AirFASE\\FIMRoot\\ARJ21
- **目标飞机**: B-652G, B-656E

## 🔧 兼容性说明

### 💻 **系统兼容性**
- ✅ Windows 10/11
- ✅ Windows Server 2019/2022/2025
- ✅ 自动适配系统托盘权限

### 📦 **依赖处理**
- **必需**: tkinter（Python内置）
- **可选**: watchdog（文件监控）
- **可选**: rarfile, py7zr（压缩文件处理）
- **可选**: pystray, PIL（系统托盘）

### 🛡️ **错误处理**
- 缺少依赖时优雅降级
- 系统托盘不可用时使用普通窗口
- 监控路径不存在时显示错误信息

## 🎊 **最终特性总结**

### ✅ **完全解决的问题**
- [x] 双击托盘图标恢复窗口
- [x] Windows Server 2025兼容性
- [x] 完全移除后台服务依赖
- [x] 单文件集成所有功能
- [x] GUI界面实时更新

### 🚀 **用户体验**
- **即开即用**: 双击启动，立即开始监控
- **实时反馈**: 界面信息实时更新
- **完全控制**: 用户完全掌控程序状态
- **兼容性强**: 适配各种Windows系统

## 📁 文件说明

- `FIM_Vietnam_Final.exe` - 最终集成版主程序
- `启动FIM_Vietnam.bat` - 启动脚本
- `app.ico` - 程序图标
- `app_green.png` - 绿色状态图标（可选）
- `app_red.png` - 红色状态图标（可选）

---

**版本**: 最终集成版 v1.0  
**特点**: 单文件集成，解决所有已知问题  
**兼容性**: Windows 10+ / Windows Server 2019+
"""
    
    with open("dist/使用说明.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("FIM Vietnam 最终版本构建工具")
    print("="*50)
    
    # 执行构建步骤
    steps = [
        ("构建最终版本", build_final_version),
        ("创建部署包", create_deployment_package)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，构建中止")
            return
    
    print("\n🎉 最终版本构建完成！")
    print("\n📦 部署包内容:")
    print("- FIM_Vietnam_Final.exe     (最终集成版主程序)")
    print("- 启动FIM_Vietnam.bat       (启动脚本)")
    print("- app.ico                   (程序图标)")
    print("- 使用说明.md               (详细说明)")
    
    print("\n✨ 解决的问题:")
    print("- ✅ 双击托盘图标恢复窗口")
    print("- ✅ Windows Server 2025兼容性")
    print("- ✅ 完全移除后台服务")
    print("- ✅ 单文件集成所有功能")
    print("- ✅ GUI界面实时更新")
    
    print("\n📋 使用方法:")
    print("1. 将dist目录下的所有文件复制到目标电脑")
    print("2. 双击运行 FIM_Vietnam_Final.exe")
    print("3. 程序启动即开始监控，关闭即完全退出")

if __name__ == "__main__":
    from datetime import datetime
    main()
    input("\n按回车键退出...")
