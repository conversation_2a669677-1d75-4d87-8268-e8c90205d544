"""
构建GUI版本的FIM Vietnam
包含完整的图形界面和系统托盘功能
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """安装所需依赖"""
    print("📦 安装依赖包...")
    
    dependencies = [
        'pyinstaller',
        'watchdog', 
        'rarfile', 
        'py7zr', 
        'psutil',
        'pystray',
        'pillow'
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                          check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装 {dep} 失败: {e}")
            return False
    
    print("✅ 所有依赖安装完成")
    return True

def create_icons():
    """创建高质量图标"""
    print("🎨 创建高质量图标...")
    try:
        subprocess.run([sys.executable, "create_icons.py"], check=True)
        print("✅ 图标创建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 图标创建失败: {e}")
        return False

def build_gui_executable():
    """构建GUI可执行文件"""
    print("🔨 构建GUI可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                           # 单文件
        "--windowed",                          # 无控制台窗口
        "--name=FIM_Vietnam_GUI",              # 可执行文件名
        "--icon=app_hd.ico",                   # 主图标
        "--add-data=app_hd.ico;.",             # 包含主图标
        "--add-data=app_green_hd.ico;.",       # 包含绿色图标
        "--add-data=app_red_hd.ico;.",         # 包含红色图标
        "--add-data=app_green.ico;.",          # 包含绿色图标(备用)
        "--add-data=app_red.ico;.",            # 包含红色图标(备用)
        "--hidden-import=rarfile",             # 隐式导入
        "--hidden-import=py7zr",
        "--hidden-import=watchdog",
        "--hidden-import=psutil",
        "--hidden-import=pystray",
        "--hidden-import=PIL",
        "--collect-all=pystray",               # 收集所有相关文件
        "--collect-all=PIL",
        "FIM_Vietnam_WithGUI.py"               # 源文件
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ GUI版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ GUI版本构建失败")
        print(f"错误信息: {e.stderr}")
        return False

def build_service_executable():
    """构建纯服务版本可执行文件"""
    print("🔨 构建服务版本可执行文件...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console",                           # 保留控制台
        "--name=FIM_Vietnam_Service",
        "--icon=app_hd.ico",
        "--hidden-import=rarfile",
        "--hidden-import=py7zr", 
        "--hidden-import=watchdog",
        "--hidden-import=psutil",
        "FIM_Vietnam.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 服务版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 服务版本构建失败")
        print(f"错误信息: {e.stderr}")
        return False

def create_deployment_package():
    """创建部署包"""
    print("📦 创建部署包...")
    
    # 确保dist目录存在
    if not os.path.exists("dist"):
        print("❌ dist目录不存在")
        return False
    
    # 复制必要文件到dist目录
    files_to_copy = [
        "app_hd.ico",
        "app_green_hd.ico", 
        "app_red_hd.ico",
        "app_green.ico",
        "app_red.ico",
        "启动FIM_Vietnam.bat"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, "dist/")
            print(f"复制文件: {file}")
    
    # 创建Windows服务配置
    print("创建Windows服务配置...")
    try:
        os.chdir("dist")
        subprocess.run([sys.executable, "../create_windows_service.py"], check=True)
        os.chdir("..")
        print("✅ Windows服务配置创建完成")
    except Exception as e:
        print(f"❌ Windows服务配置创建失败: {e}")
        os.chdir("..")
    
    # 创建使用说明
    create_readme()
    
    print("✅ 部署包创建完成")
    return True

def create_readme():
    """创建使用说明"""
    readme_content = """# FIM Vietnam 自动化监控服务

## 🚀 快速开始

### 方式一：GUI界面模式（推荐）
双击运行 `FIM_Vietnam_GUI.exe`

### 方式二：选择运行模式
双击运行 `启动FIM_Vietnam.bat`，然后选择：
1. GUI界面模式 - 带图形界面，支持系统托盘
2. 命令行模式 - 无图形界面，控制台运行
3. 纯服务模式 - 后台运行，无界面

### 方式三：Windows服务模式
1. 以管理员身份运行 `install_service.bat`
2. 运行 `start_service.bat` 启动服务
3. 服务将开机自动启动

## 📊 GUI界面功能

- **实时状态监控**: 服务运行状态、处理统计、错误计数
- **运行时间显示**: 服务持续运行时间
- **任务进度跟踪**: 当前处理任务和最后活动时间
- **一键控制**: 启动/停止/重启服务
- **日志查看**: 快速打开日志目录
- **系统托盘**: 最小化到托盘，状态图标显示
- **任务栏状态**: 最小化时显示简要状态

## 🎯 系统托盘功能

- **绿色图标**: 服务正常运行
- **红色图标**: 服务已停止
- **右键菜单**: 显示窗口、启动/停止服务、退出程序
- **状态提示**: 鼠标悬停显示详细状态

## ⚙️ 配置信息

- **监控路径**: Z:\\Vietnam\\PC卡
- **备份路径**: Z:\\DATA_BAK\\FDIMU_PC
- **AirFASE路径**: D:\\AirFASE\\FIMRoot\\ARJ21
- **目标飞机**: B-652G, B-656E

## 📝 文件说明

- `FIM_Vietnam_GUI.exe` - GUI版本主程序
- `FIM_Vietnam_Service.exe` - 纯服务版本
- `启动FIM_Vietnam.bat` - 启动选择脚本
- `install_service.bat` - 安装Windows服务
- `start_service.bat` - 启动服务
- `stop_service.bat` - 停止服务
- `check_status.bat` - 检查服务状态

## 🔧 故障排除

1. **GUI无法启动**: 确保安装了7-Zip或WinRAR
2. **服务无法启动**: 检查监控路径是否存在
3. **托盘图标异常**: 重启程序或检查图标文件
4. **权限错误**: 以管理员身份运行相关脚本

## 📞 技术支持

如遇问题，请查看logs目录下的日志文件，或联系技术支持。

---
构建时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
版本: GUI增强版 v2.0
"""
    
    with open("dist/使用说明.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("FIM Vietnam GUI版本构建工具")
    print("="*50)
    
    # 检查源文件
    required_files = [
        "FIM_Vietnam.py",
        "FIM_Vietnam_GUI.py", 
        "FIM_Vietnam_WithGUI.py",
        "create_icons.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 找不到源文件: {file}")
            return
    
    # 执行构建步骤
    steps = [
        ("安装依赖", install_dependencies),
        ("创建图标", create_icons),
        ("构建GUI版本", build_gui_executable),
        ("构建服务版本", build_service_executable),
        ("创建部署包", create_deployment_package)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，构建中止")
            return
    
    print("\n🎉 构建完成！")
    print("\n📦 部署包内容:")
    print("- FIM_Vietnam_GUI.exe        (GUI版本主程序)")
    print("- FIM_Vietnam_Service.exe    (纯服务版本)")
    print("- 启动FIM_Vietnam.bat        (启动选择脚本)")
    print("- install_service.bat        (安装Windows服务)")
    print("- 各种图标文件               (高清图标)")
    print("- 使用说明.md                (详细说明文档)")
    
    print("\n📋 使用方法:")
    print("1. 将dist目录下的所有文件复制到目标电脑")
    print("2. 双击运行 FIM_Vietnam_GUI.exe 启动GUI版本")
    print("3. 或运行 启动FIM_Vietnam.bat 选择运行模式")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
