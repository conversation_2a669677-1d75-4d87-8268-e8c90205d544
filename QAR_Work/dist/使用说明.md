# PC_AUTO_UNZIP 使用说明

## 🚀 快速开始

### 启动程序
- **方式一**: 双击运行 `PC_AUTO_UNZIP.exe`
- **方式二**: 双击运行 `启动PC_AUTO_UNZIP.bat`

## 📋 功能说明

### 🎯 主要功能
1. **文件监控**: 持续监控Y盘指定路径的文件变化
2. **智能日期范围**: 自动计算前7天到后3天的监控范围
3. **压缩文件解压**: 支持ZIP、RAR、7Z格式自动解压
4. **飞机号提取**: 从文件夹名和MSG.DAT文件提取飞机号
5. **日期信息提取**: 支持多种日期格式智能识别
6. **双路径复制**: 复制到DATA_BAK和QAR_PC目录
7. **文件重命名**: 按规则重命名为标准格式

### 🎮 操作方式
- **默认状态**: 程序启动时为暂停状态
- **开始监控**: 点击"▶️ 开始"按钮启动文件监控
- **暂停监控**: 点击"⏸️ 暂停"按钮停止监控
- **查看日志**: 点击"📋 查看日志"打开日志目录
- **打开目录**: 点击"📁 打开监控目录"访问Y盘

## ⚙️ 配置信息

### 📁 路径配置
- **监控路径**: Y: (主监控目录)
- **临时路径**: Y:\Data_Monitor (临时处理目录)
- **DATA_BAK路径**: Z:\DATA_BAK\FDIMU_PC (备份目录)
- **QAR_PC路径**: Z:\DATA_BAK\QAR_PC (QAR处理目录)
- **日志目录**: D:\AUTO_QAR (日志存储目录)

### 📅 监控范围
- **日期范围**: 当前日期前7天到后3天
- **自动更新**: 跨日期时自动刷新范围
- **实时显示**: 界面显示当前有效监控范围

## 🔧 处理流程

### 📂 文件扫描
1. 扫描Y盘下yyyy-mm格式的年月目录
2. 进入yyyymmdd格式的日期目录
3. 检查QAR_PC.log判断处理状态
4. 增量复制新文件到临时目录

### 📦 文件解压
1. 扫描临时目录中的压缩文件
2. 支持ZIP、RAR、7Z格式解压
3. 解压后删除原压缩文件
4. 保留目录结构

### ✈️ 信息提取
1. 从文件夹名提取飞机号和日期
2. 从MSG.DAT文件提取补充信息
3. 综合判断最终的飞机号和日期
4. 验证数据文件夹有效性

### 📋 文件复制
1. 复制到Z:\DATA_BAK\FDIMU_PC (按原结构)
2. 复制到Z:\DATA_BAK\QAR_PC (重命名格式)
3. 重命名为: B-xxxx_yyyymmdd0000.pc
4. 记录详细处理日志

## 📊 界面说明

### 🖥️ 主界面
- **标题区域**: 程序名称和运行状态指示器
- **配置信息**: 显示所有路径配置和监控范围
- **状态卡片**: 运行时间、处理统计、错误计数
- **控制按钮**: 开始/暂停、查看日志、打开目录
- **实时日志**: 滚动显示处理过程和状态

### 🎯 状态指示
- **🟢 绿色圆点**: 监控运行中
- **🔴 红色圆点**: 监控已暂停
- **状态文本**: 显示当前运行状态
- **实时更新**: 处理统计实时刷新

## 📝 日志系统

### 📁 日志文件
- **本地日志**: 每个处理目录的QAR_PC.log
- **全局日志**: D:\AUTO_QAR\QAR_PC.log
- **程序日志**: D:\AUTO_QAR\PC_UNZIP.log
- **详细记录**: 时间、文件名、类型、结果

### 🔍 日志内容
- 文件复制操作记录
- 压缩文件解压记录
- 飞机号和日期提取记录
- 错误和异常处理记录

## 🔧 故障排除

### ❌ 常见问题
1. **监控路径不存在**: 检查Y盘是否可访问
2. **解压失败**: 确认安装了7-Zip或WinRAR
3. **权限错误**: 确保对目标路径有写入权限
4. **依赖库缺失**: 程序会自动检测并提示

### 📞 技术支持
- 查看日志文件了解详细错误信息
- 检查配置路径是否正确
- 确认所需的解压软件已安装

## 📦 文件说明

- `PC_AUTO_UNZIP.exe` - 主程序文件
- `启动PC_AUTO_UNZIP.bat` - 启动脚本
- `app.ico` - 程序图标
- `使用说明.md` - 本说明文档

---

**版本**: v1.0  
**构建时间**: 2025-07-27 13:17:45  
**特点**: 智能文件监控和处理工具
