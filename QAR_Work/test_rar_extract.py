import os
import shutil
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

def test_rar_extraction():
    """测试RAR文件解压功能"""
    print("RAR文件解压测试工具")
    print("="*50)
    
    # 检查解压工具
    print("1. 检查解压工具...")
    tools = check_extraction_tools()
    
    if not tools:
        print("❌ 未找到任何解压工具！")
        print("\n请安装以下软件之一：")
        print("- WinRAR: https://www.winrar.com/")
        print("- 7-Zip: https://www.7-zip.org/")
        return False
    
    print("✅ 找到以下解压工具：")
    for tool in tools:
        print(f"   - {tool}")
    
    # 测试解压
    print("\n2. 测试解压功能...")
    rar_file = input("请输入RAR文件路径（或按回车跳过）: ").strip()
    
    if rar_file and os.path.exists(rar_file):
        test_extract_rar(rar_file)
    else:
        print("跳过实际解压测试")
    
    return True

def check_extraction_tools():
    """检查可用的解压工具"""
    tools = []
    
    # 检查WinRAR
    winrar_paths = [
        r"C:\Program Files\WinRAR\WinRAR.exe",
        r"C:\Program Files (x86)\WinRAR\WinRAR.exe",
        r"C:\Program Files\WinRAR\UnRAR.exe",
        r"C:\Program Files (x86)\WinRAR\UnRAR.exe"
    ]
    
    for path in winrar_paths:
        if os.path.exists(path):
            tools.append(f"WinRAR: {path}")
            break
    
    # 检查7-Zip
    sevenzip_paths = [
        r"C:\Program Files\7-Zip\7z.exe",
        r"C:\Program Files (x86)\7-Zip\7z.exe"
    ]
    
    for path in sevenzip_paths:
        if os.path.exists(path):
            tools.append(f"7-Zip: {path}")
            break
    
    # 检查rarfile库
    try:
        import rarfile
        tools.append("Python rarfile库")
    except ImportError:
        print("⚠️  rarfile库未安装，运行: pip install rarfile")
    
    # 检查py7zr库
    try:
        import py7zr
        tools.append("Python py7zr库")
    except ImportError:
        print("⚠️  py7zr库未安装，运行: pip install py7zr")
    
    return tools

def test_extract_rar(rar_file):
    """测试解压RAR文件"""
    extract_dir = os.path.join(os.path.dirname(rar_file), "test_extract")
    
    # 清理之前的测试目录
    if os.path.exists(extract_dir):
        shutil.rmtree(extract_dir)
    
    os.makedirs(extract_dir, exist_ok=True)
    
    print(f"解压文件: {rar_file}")
    print(f"解压到: {extract_dir}")
    
    # 方法1: 使用WinRAR
    success = extract_with_winrar(rar_file, extract_dir)
    if success:
        print("✅ WinRAR解压成功")
        list_extracted_files(extract_dir)
        return
    
    # 方法2: 使用7-Zip
    success = extract_with_7zip(rar_file, extract_dir)
    if success:
        print("✅ 7-Zip解压成功")
        list_extracted_files(extract_dir)
        return
    
    # 方法3: 使用rarfile库
    success = extract_with_rarfile(rar_file, extract_dir)
    if success:
        print("✅ rarfile库解压成功")
        list_extracted_files(extract_dir)
        return
    
    print("❌ 所有解压方法都失败了")

def extract_with_winrar(rar_file, extract_dir):
    """使用WinRAR解压"""
    winrar_paths = [
        r"C:\Program Files\WinRAR\WinRAR.exe",
        r"C:\Program Files (x86)\WinRAR\WinRAR.exe"
    ]
    
    for winrar_path in winrar_paths:
        if os.path.exists(winrar_path):
            try:
                print(f"尝试使用WinRAR: {winrar_path}")
                cmd = [winrar_path, 'x', '-y', rar_file, extract_dir + os.sep]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    return True
                else:
                    print(f"WinRAR错误: {result.stderr}")
            except Exception as e:
                print(f"WinRAR异常: {str(e)}")
    
    return False

def extract_with_7zip(rar_file, extract_dir):
    """使用7-Zip解压"""
    sevenzip_paths = [
        r"C:\Program Files\7-Zip\7z.exe",
        r"C:\Program Files (x86)\7-Zip\7z.exe"
    ]
    
    for sevenzip_path in sevenzip_paths:
        if os.path.exists(sevenzip_path):
            try:
                print(f"尝试使用7-Zip: {sevenzip_path}")
                cmd = [sevenzip_path, 'x', f'-o{extract_dir}', '-y', rar_file]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    return True
                else:
                    print(f"7-Zip错误: {result.stderr}")
            except Exception as e:
                print(f"7-Zip异常: {str(e)}")
    
    return False

def extract_with_rarfile(rar_file, extract_dir):
    """使用rarfile库解压"""
    try:
        import rarfile
        print("尝试使用rarfile库")
        
        # 设置unrar工具路径
        unrar_tool = find_unrar_tool()
        if unrar_tool:
            rarfile.UNRAR_TOOL = unrar_tool
            print(f"使用unrar工具: {unrar_tool}")
        
        with rarfile.RarFile(rar_file, 'r') as rar_ref:
            rar_ref.extractall(extract_dir)
        
        return True
        
    except ImportError:
        print("rarfile库未安装")
        return False
    except Exception as e:
        print(f"rarfile库错误: {str(e)}")
        return False

def find_unrar_tool():
    """查找unrar工具"""
    possible_paths = [
        r"C:\Program Files\WinRAR\UnRAR.exe",
        r"C:\Program Files (x86)\WinRAR\UnRAR.exe",
        r"C:\Program Files\7-Zip\7z.exe",
        r"C:\Program Files (x86)\7-Zip\7z.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 检查系统PATH
    if shutil.which("unrar"):
        return "unrar"
    if shutil.which("7z"):
        return "7z"
    
    return None

def list_extracted_files(extract_dir):
    """列出解压的文件"""
    print("\n解压的文件:")
    try:
        for root, dirs, files in os.walk(extract_dir):
            level = root.replace(extract_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
    except Exception as e:
        print(f"列出文件失败: {str(e)}")

if __name__ == "__main__":
    test_rar_extraction()
    input("\n按回车键退出...")
