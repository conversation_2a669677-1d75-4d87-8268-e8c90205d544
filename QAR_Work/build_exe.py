"""
FIM_Vietnam.py 打包脚本
用于创建独立的可执行文件，无需Python环境
"""

import os
import sys
import subprocess

def install_pyinstaller():
    """安装PyInstaller"""
    print("检查PyInstaller...")
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
    except ImportError:
        print("📦 正在安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 无控制台窗口（后台运行）
        "--name=FIM_Vietnam_Service",   # 可执行文件名称
        "--icon=app.ico",              # 图标文件（如果存在）
        "--add-data=app.ico;.",        # 包含图标文件
        "--hidden-import=rarfile",      # 隐式导入
        "--hidden-import=py7zr",
        "--hidden-import=watchdog",
        "--distpath=dist",             # 输出目录
        "--workpath=build",            # 临时目录
        "--specpath=.",                # spec文件位置
        "FIM_Vietnam.py"               # 源文件
    ]
    
    # 如果没有图标文件，移除图标相关参数
    if not os.path.exists("app.ico"):
        cmd = [arg for arg in cmd if not arg.startswith("--icon") and not arg.startswith("--add-data")]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        print(f"📁 可执行文件位置: dist/FIM_Vietnam_Service.exe")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print(f"错误信息: {e.stderr}")
        return False

def create_service_files():
    """创建服务相关文件"""
    print("📝 创建服务配置文件...")
    
    # 创建服务安装脚本
    install_service_bat = """@echo off
echo 安装FIM Vietnam服务...
echo.

REM 复制可执行文件到系统目录
set SERVICE_DIR=C:\\FIM_Vietnam
if not exist "%SERVICE_DIR%" mkdir "%SERVICE_DIR%"

copy "FIM_Vietnam_Service.exe" "%SERVICE_DIR%\\"
copy "install_service.bat" "%SERVICE_DIR%\\"
copy "uninstall_service.bat" "%SERVICE_DIR%\\"
copy "start_service.bat" "%SERVICE_DIR%\\"
copy "stop_service.bat" "%SERVICE_DIR%\\"

echo 文件复制完成！
echo.
echo 服务安装位置: %SERVICE_DIR%
echo.
echo 请手动执行以下步骤：
echo 1. 将 %SERVICE_DIR%\\start_service.bat 添加到开机启动项
echo 2. 或使用任务计划程序设置开机自动运行
echo.
pause
"""
    
    # 创建启动脚本
    start_service_bat = """@echo off
echo 启动FIM Vietnam监控服务...
cd /d "C:\\FIM_Vietnam"
start "" "FIM_Vietnam_Service.exe"
echo 服务已启动！
"""
    
    # 创建停止脚本
    stop_service_bat = """@echo off
echo 停止FIM Vietnam监控服务...
taskkill /f /im "FIM_Vietnam_Service.exe" 2>nul
echo 服务已停止！
pause
"""
    
    # 创建卸载脚本
    uninstall_service_bat = """@echo off
echo 卸载FIM Vietnam服务...
echo.

REM 停止服务
taskkill /f /im "FIM_Vietnam_Service.exe" 2>nul

REM 删除服务文件
set SERVICE_DIR=C:\\FIM_Vietnam
if exist "%SERVICE_DIR%" (
    echo 删除服务目录: %SERVICE_DIR%
    rmdir /s /q "%SERVICE_DIR%"
)

echo 服务卸载完成！
pause
"""
    
    # 写入文件
    with open("dist/install_service.bat", "w", encoding="gbk") as f:
        f.write(install_service_bat)
    
    with open("dist/start_service.bat", "w", encoding="gbk") as f:
        f.write(start_service_bat)
    
    with open("dist/stop_service.bat", "w", encoding="gbk") as f:
        f.write(stop_service_bat)
    
    with open("dist/uninstall_service.bat", "w", encoding="gbk") as f:
        f.write(uninstall_service_bat)
    
    print("✅ 服务配置文件创建完成！")

def main():
    """主函数"""
    print("FIM Vietnam 可执行文件构建工具")
    print("="*50)
    
    # 检查源文件
    if not os.path.exists("FIM_Vietnam.py"):
        print("❌ 找不到源文件 FIM_Vietnam.py")
        return
    
    # 安装PyInstaller
    install_pyinstaller()
    
    # 构建可执行文件
    if build_executable():
        # 创建服务文件
        create_service_files()
        
        print("\n🎉 构建完成！")
        print("\n📦 部署包内容:")
        print("- FIM_Vietnam_Service.exe  (主程序)")
        print("- install_service.bat      (安装脚本)")
        print("- start_service.bat        (启动脚本)")
        print("- stop_service.bat         (停止脚本)")
        print("- uninstall_service.bat    (卸载脚本)")
        
        print("\n📋 部署步骤:")
        print("1. 将 dist 目录下的所有文件复制到目标电脑")
        print("2. 在目标电脑上运行 install_service.bat")
        print("3. 按照提示设置开机自动启动")
        
    else:
        print("❌ 构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
