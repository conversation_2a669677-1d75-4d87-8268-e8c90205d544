import os
import shutil
import sys
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QFileDialog, QWidget,
                             QProgressBar, QMessageBox, QGroupBox, QFrame, QDesktopWidget)
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont, QPalette, QFontDatabase


class FileCopyApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ZIP文件定时复制工具")

        # 获取屏幕信息和DPI缩放
        self.setup_display_settings()

        # 设置窗口尺寸（根据DPI自适应，调整为更紧凑的尺寸）
        window_width = int(580 * self.scale_factor)
        window_height = int(420 * self.scale_factor)
        self.setGeometry(100, 100, window_width, window_height)

        # 设置窗口样式（DPI自适应）
        padding_size = max(8, int(10 * self.scale_factor))
        border_radius = max(4, int(6 * self.scale_factor))
        margin_size = max(10, int(12 * self.scale_factor))

        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            QGroupBox {{
                font-weight: bold;
                border: {max(1, int(2 * self.scale_factor))}px solid #cccccc;
                border-radius: {border_radius}px;
                margin-top: 1ex;
                padding-top: {margin_size}px;
                background-color: white;
                font-size: {self.base_font_size}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {margin_size}px;
                padding: 0 {padding_size}px 0 {padding_size}px;
                color: #2c3e50;
                font-size: {self.large_font_size}px;
                font-weight: bold;
            }}
            QLabel {{
                color: #2c3e50;
                font-size: {self.base_font_size}px;
            }}
            QLineEdit {{
                border: 1px solid #bdc3c7;
                border-radius: {max(3, int(4 * self.scale_factor))}px;
                padding: {padding_size}px;
                font-size: {self.base_font_size}px;
                background-color: white;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QLineEdit:focus {{
                border-color: #3498db;
                border-width: {max(1, int(2 * self.scale_factor))}px;
            }}
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {border_radius}px;
                padding: {padding_size}px {int(padding_size * 1.5)}px;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                min-height: {max(25, int(30 * self.scale_factor))}px;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #21618c;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
            QProgressBar {{
                border: 1px solid #bdc3c7;
                border-radius: {border_radius}px;
                text-align: center;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                background-color: #ecf0f1;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QProgressBar::chunk {{
                background-color: #27ae60;
                border-radius: {max(3, int(5 * self.scale_factor))}px;
            }}
        """)

        # 初始化变量
        self.source_path = "E:\\ZIP"
        self.target_path = "D:\\NON-WGL\\CAST"
        self.copied_files = []
        self.total_files = 0
        self.current_file_index = 0
        self.start_time = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.copy_next_file)
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.folder_check_timer = QTimer()
        self.folder_check_timer.timeout.connect(self.check_target_folder)
        self.is_running = False
        self.waiting_seconds = 0  # 改为等待时间计数器

        # 创建UI
        self.init_ui()

        # 扫描源文件夹
        self.scan_source_folder()

    def setup_display_settings(self):
        """设置显示相关参数"""
        # 获取屏幕信息
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        # 计算DPI缩放因子
        app = QApplication.instance()
        if app:
            # 获取主屏幕的DPI
            screen = app.primaryScreen()
            dpi = screen.logicalDotsPerInch()
            # 标准DPI是96，计算缩放因子
            self.scale_factor = max(1.0, dpi / 96.0)

            # 对于4K显示器，进一步调整
            if self.screen_width >= 3840:  # 4K或更高分辨率
                self.scale_factor = max(self.scale_factor, 1.5)
            elif self.screen_width >= 2560:  # 2K分辨率
                self.scale_factor = max(self.scale_factor, 1.25)
        else:
            self.scale_factor = 1.0

        # 计算基础字体大小（字体大一号）
        self.base_font_size = max(10, int(11 * self.scale_factor))
        self.large_font_size = max(11, int(13 * self.scale_factor))
        self.huge_font_size = max(36, int(44 * self.scale_factor))

        print(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")
        print(f"DPI缩放因子: {self.scale_factor:.2f}")
        print(f"字体大小: 基础={self.base_font_size}, 大={self.large_font_size}, 超大={self.huge_font_size}")

    def init_ui(self):
        # 主窗口布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # DPI自适应的间距和边距（调整为更紧凑）
        spacing = max(8, int(10 * self.scale_factor))
        margin = max(12, int(15 * self.scale_factor))

        main_layout.setSpacing(spacing)
        main_layout.setContentsMargins(margin, margin, margin, margin)

        # 路径设置分组
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout()
        path_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的控件尺寸（调整为更紧凑）
        label_width = max(60, int(70 * self.scale_factor))
        button_width = max(60, int(70 * self.scale_factor))
        input_height = max(25, int(30 * self.scale_factor))
        button_height = max(25, int(30 * self.scale_factor))

        # 源路径设置
        source_layout = QHBoxLayout()
        source_label = QLabel("源文件夹:")
        source_label.setMinimumWidth(label_width)
        source_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.source_edit = QLineEdit(self.source_path)
        self.source_edit.setReadOnly(True)
        self.source_edit.setMinimumHeight(input_height)
        source_button = QPushButton("浏览...")
        source_button.setMinimumWidth(button_width)
        source_button.setMinimumHeight(button_height)
        source_button.clicked.connect(self.select_source_folder)
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_edit, 1)
        source_layout.addWidget(source_button)

        # 目标路径设置
        target_layout = QHBoxLayout()
        target_label = QLabel("目标文件夹:")
        target_label.setMinimumWidth(label_width)
        target_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.target_edit = QLineEdit(self.target_path)
        self.target_edit.setReadOnly(True)
        self.target_edit.setMinimumHeight(input_height)
        target_button = QPushButton("浏览...")
        target_button.setMinimumWidth(button_width)
        target_button.setMinimumHeight(button_height)
        target_button.clicked.connect(self.select_target_folder)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_edit, 1)
        target_layout.addWidget(target_button)

        path_layout.addLayout(source_layout)
        path_layout.addLayout(target_layout)
        path_group.setLayout(path_layout)

        # 状态信息分组
        status_group = QGroupBox("复制状态")
        status_layout = QVBoxLayout()
        status_layout.setSpacing(10)

        # 文件信息
        self.file_info_label = QLabel("等待开始...")
        self.file_info_label.setAlignment(Qt.AlignCenter)
        info_padding = max(6, int(8 * self.scale_factor))
        info_radius = max(3, int(4 * self.scale_factor))
        self.file_info_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
            }}
        """)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setMinimumHeight(max(18, int(22 * self.scale_factor)))

        # 时间信息
        self.time_info_label = QLabel("已用时间: 0:00:00 | 预计剩余: --:--:-- | 预计结束: --:--:--")
        self.time_info_label.setAlignment(Qt.AlignCenter)
        time_padding = max(4, int(5 * self.scale_factor))
        self.time_info_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                padding: {time_padding}px;
            }}
        """)

        # 完成状态信息
        self.completion_label = QLabel("")
        self.completion_label.setAlignment(Qt.AlignCenter)
        self.completion_label.setVisible(False)
        completion_padding = max(8, int(10 * self.scale_factor))
        completion_radius = max(4, int(6 * self.scale_factor))
        self.completion_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #27ae60;
                padding: {completion_padding}px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: {completion_radius}px;
                margin: 5px;
            }}
        """)

        status_layout.addWidget(self.file_info_label)
        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.time_info_label)
        status_layout.addWidget(self.completion_label)
        status_group.setLayout(status_layout)

        # 等待时间显示分组
        countdown_group = QGroupBox("等待时间")
        countdown_layout = QVBoxLayout()
        countdown_layout.setAlignment(Qt.AlignCenter)

        self.countdown_label = QLabel("00:00")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        countdown_font = QFont()
        countdown_font.setPointSize(self.huge_font_size)
        countdown_font.setBold(True)
        self.countdown_label.setFont(countdown_font)

        countdown_padding = max(12, int(15 * self.scale_factor))
        countdown_margin = max(6, int(8 * self.scale_factor))
        countdown_radius = max(6, int(8 * self.scale_factor))
        countdown_border = max(1, int(2 * self.scale_factor))

        self.countdown_label.setStyleSheet(f"""
            QLabel {{
                color: #e74c3c;
                background-color: #fdf2f2;
                border: {countdown_border}px solid #fadbd8;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {self.huge_font_size}px;
                font-weight: bold;
            }}
        """)

        countdown_layout.addWidget(self.countdown_label)
        countdown_group.setLayout(countdown_layout)

        # 控制按钮分组
        control_group = QGroupBox("操作控制")
        control_layout = QHBoxLayout()
        control_layout.setSpacing(max(10, int(12 * self.scale_factor)))

        # DPI自适应的按钮尺寸（字体大一号）
        button_height = max(32, int(38 * self.scale_factor))
        button_width = max(90, int(110 * self.scale_factor))
        button_font_size = max(11, int(13 * self.scale_factor))

        self.start_button = QPushButton("🚀 开始复制")
        self.start_button.setMinimumHeight(button_height)
        self.start_button.setMinimumWidth(button_width)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #27ae60;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
            QPushButton:pressed {{
                background-color: #1e8449;
            }}
        """)
        self.start_button.clicked.connect(self.start_copying)

        self.cancel_button = QPushButton("⏹ 取消操作")
        self.cancel_button.setMinimumHeight(button_height)
        self.cancel_button.setMinimumWidth(button_width)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
            QPushButton:pressed {{
                background-color: #a93226;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        self.cancel_button.clicked.connect(self.cancel_copying)
        self.cancel_button.setEnabled(False)

        control_layout.addStretch()
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.cancel_button)
        control_layout.addStretch()
        control_group.setLayout(control_layout)

        # 添加到主布局
        main_layout.addWidget(path_group)
        main_layout.addWidget(status_group)
        main_layout.addWidget(countdown_group)
        main_layout.addWidget(control_group)
        main_layout.addStretch()

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def select_source_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择源文件夹", self.source_path)
        if folder:
            self.source_path = folder
            self.source_edit.setText(folder)
            self.scan_source_folder()

    def select_target_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择目标文件夹", self.target_path)
        if folder:
            self.target_path = folder
            self.target_edit.setText(folder)

    def scan_source_folder(self):
        # 确保Copied目录存在
        copied_dir = os.path.join(self.source_path, "Copied")
        if not os.path.exists(copied_dir):
            os.makedirs(copied_dir)

        # 获取所有ZIP文件
        self.zip_files = [f for f in os.listdir(self.source_path)
                          if f.lower().endswith('.zip') and not f.startswith('~$')]
        self.total_files = len(self.zip_files)
        self.current_file_index = 0

        # 更新UI
        self.update_file_info()

    def update_file_info(self):
        remaining = self.total_files - self.current_file_index
        self.file_info_label.setText(
            f"找到 {self.total_files} 个ZIP文件 | 已复制 {self.current_file_index} | 剩余 {remaining}"
        )
        self.progress_bar.setMaximum(self.total_files)
        self.progress_bar.setValue(self.current_file_index)

        if self.is_running and self.start_time:
            elapsed = datetime.now() - self.start_time
            if self.current_file_index > 0:
                avg_time_per_file = elapsed / self.current_file_index
                estimated_remaining = avg_time_per_file * remaining
                estimated_end = datetime.now() + estimated_remaining

                self.time_info_label.setText(
                    f"已用时间: {str(elapsed).split('.')[0]} | "
                    f"预计剩余: {str(estimated_remaining).split('.')[0]} | "
                    f"预计结束: {estimated_end.strftime('%H:%M:%S')}"
                )

    def update_countdown(self):
        """更新等待时间显示"""
        self.waiting_seconds += 1
        # 格式化为 MM:SS
        minutes = self.waiting_seconds // 60
        seconds = self.waiting_seconds % 60
        self.countdown_label.setText(f"{minutes:02d}:{seconds:02d}")

    def check_target_folder(self):
        """检查目标文件夹是否为空"""
        if not self.is_running or self.current_file_index >= self.total_files:
            return

        try:
            # 检查目标文件夹是否为空
            target_files = [f for f in os.listdir(self.target_path)
                            if f.lower().endswith('.zip')]

            if not target_files:  # 如果目标文件夹为空
                # 停止检查定时器，准备复制
                self.folder_check_timer.stop()
                # 5秒后执行复制
                QTimer.singleShot(5000, self.copy_next_file)
            # 否则不做任何操作，继续等待下次检查
        except Exception as e:
            print(f"检查目标文件夹出错: {str(e)}")

    def start_copying(self):
        if self.total_files == 0:
            QMessageBox.warning(self, "警告", "源文件夹中没有找到ZIP文件!")
            return

        # 确保目标路径存在
        try:
            if not os.path.exists(self.target_path):
                os.makedirs(self.target_path)
                QMessageBox.information(self, "信息", f"目标路径不存在，已自动创建: {self.target_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目标路径: {str(e)}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 隐藏完成信息标签
        self.completion_label.setVisible(False)

        # 启动计时器
        self.waiting_seconds = 0
        self.countdown_label.setText("00:00")
        self.countdown_timer.start(1000)  # 每秒更新一次
        self.folder_check_timer.start(10000)  # 每10秒检查一次文件夹

        # 立即检查目标文件夹
        self.check_target_folder()

    def copy_next_file(self):
        if self.current_file_index >= self.total_files or not self.is_running:
            self.finish_copying()
            return

        # 获取当前文件
        current_file = self.zip_files[self.current_file_index]
        source_file = os.path.join(self.source_path, current_file)
        target_file = os.path.join(self.target_path, current_file)
        copied_dir = os.path.join(self.source_path, "Copied")

        try:
            # 确保Copied目录存在
            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)

            # 复制文件到目标路径
            shutil.copy2(source_file, target_file)

            # 移动源文件到Copied目录
            shutil.move(source_file, os.path.join(copied_dir, current_file))

            self.current_file_index += 1
            self.update_file_info()

            # 如果还有文件，重置等待时间并重新开始检查
            if self.current_file_index < self.total_files and self.is_running:
                self.waiting_seconds = 0
                self.countdown_label.setText("00:00")
                self.folder_check_timer.start(10000)  # 重新启动文件夹检查
            else:
                self.finish_copying()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制文件时出错: {str(e)}")
            self.finish_copying()

    def cancel_copying(self):
        self.is_running = False
        self.timer.stop()
        self.countdown_timer.stop()
        self.folder_check_timer.stop()
        self.cancel_button.setEnabled(False)
        self.start_button.setEnabled(True)

        # 在原窗体上显示取消信息
        self.completion_label.setText("⚠️ 复制操作已取消")
        self.completion_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #e74c3c;
                padding: {max(8, int(10 * self.scale_factor))}px;
                background-color: #fdf2f2;
                border: 2px solid #e74c3c;
                border-radius: {max(4, int(6 * self.scale_factor))}px;
                margin: 5px;
            }}
        """)
        self.completion_label.setVisible(True)

    def finish_copying(self):
        self.is_running = False
        self.timer.stop()
        self.countdown_timer.stop()
        self.folder_check_timer.stop()
        self.cancel_button.setEnabled(False)
        self.start_button.setEnabled(True)

        # 在原窗体上显示完成信息
        if self.current_file_index >= self.total_files:
            self.completion_label.setText("✅ 所有文件已复制完成!")
        else:
            self.completion_label.setText(f"✅ 复制了 {self.current_file_index} 个文件")

        self.completion_label.setVisible(True)


def setup_high_dpi_support():
    """设置高DPI支持和字体渲染优化"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)

    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 设置DPI缩放策略
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # 启用字体抗锯齿和渲染优化
    if hasattr(Qt, 'AA_UseDesktopOpenGL'):
        QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)

    # 启用字体子像素渲染
    if hasattr(Qt, 'AA_UseSoftwareOpenGL'):
        QApplication.setAttribute(Qt.AA_UseSoftwareOpenGL, False)


if __name__ == "__main__":
    # 在创建QApplication之前设置高DPI支持
    setup_high_dpi_support()

    app = QApplication(sys.argv)

    # 设置默认字体（确保中文显示正常）
    font_db = QFontDatabase()

    # 尝试设置系统默认字体
    default_font = QFont()
    default_font.setFamily("Microsoft YaHei UI")  # 微软雅黑
    if not font_db.families().__contains__("Microsoft YaHei UI"):
        default_font.setFamily("SimHei")  # 黑体
    if not font_db.families().__contains__("SimHei"):
        default_font.setFamily("Arial Unicode MS")  # 备用字体

    default_font.setHintingPreference(QFont.PreferFullHinting)
    default_font.setStyleStrategy(QFont.PreferAntialias)
    app.setFont(default_font)

    # 创建并显示窗口
    window = FileCopyApp()
    window.show()

    # 居中显示窗口
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    window_rect = window.geometry()
    x = (screen_rect.width() - window_rect.width()) // 2
    y = (screen_rect.height() - window_rect.height()) // 2
    window.move(x, y)

    sys.exit(app.exec_())
