"""
FIM Vietnam 前端版本
程序启动即开始监控，关闭即完全退出
不使用后台服务模式
"""

import os
import sys
import json
import time
import threading
import subprocess
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox
import pystray
from PIL import Image, ImageTk
from pathlib import Path

# 导入监控功能
from FIM_Vietnam import (
    VietnamDataProcessor, VietnamFileHandler, SystemMonitor,
    MON_PATH, DATA_BAK_PATH, AIRFASE_PATH, TARGET_AIRCRAFT,
    logger, setup_logging, cleanup_old_logs
)
from watchdog.observers import Observer

class FIMVietnamFrontend:
    """FIM Vietnam 前端主程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_styles()
        self.create_widgets()
        self.setup_tray()
        
        # 监控相关
        self.monitor = SystemMonitor()
        self.observer = None
        self.monitoring_active = False
        
        # 启动监控
        self.start_monitoring()
        
        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind('<Unmap>', self.on_minimize)
        
        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("FIM Vietnam 监控中心 - 运行中")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置图标 - 使用原始图标
        try:
            if os.path.exists("app.ico"):
                self.root.iconbitmap("app.ico")
        except:
            pass
        
        # 设置背景色
        self.root.configure(bg='#f0f2f5')
    
    def setup_variables(self):
        """设置变量"""
        self.current_task = tk.StringVar(value="正在启动...")
        self.uptime = tk.StringVar(value="00:00:00")
        self.processed_files = tk.IntVar(value=0)
        self.processed_folders = tk.IntVar(value=0)
        self.error_count = tk.IntVar(value=0)
        self.last_activity = tk.StringVar(value="刚刚")
        
        # 启动时间
        self.start_time = datetime.now()
        
        # 托盘相关
        self.tray_icon = None
        self.is_minimized_to_tray = False
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'), 
                       background='#f0f2f5', foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'),
                       background='#f0f2f5', foreground='#34495e')
        style.configure('Status.TLabel', font=('Microsoft YaHei', 10),
                       background='#f0f2f5', foreground='#7f8c8d')
        style.configure('Value.TLabel', font=('Microsoft YaHei', 11, 'bold'),
                       background='#f0f2f5', foreground='#2980b9')
        
        # 按钮样式
        style.configure('Action.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(20, 10))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.create_header(main_frame)
        
        # 状态卡片区域
        self.create_status_cards(main_frame)
        
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        
        # 详细信息区域
        self.create_details_section(main_frame)
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(header_frame, text="🛡️ FIM Vietnam 监控中心", 
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # 状态指示器
        self.status_indicator = ttk.Label(header_frame, text="●", 
                                         font=('Microsoft YaHei', 20),
                                         foreground='#27ae60')
        self.status_indicator.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态文本
        self.status_text = ttk.Label(header_frame, text="监控运行中", 
                                    style='Header.TLabel')
        self.status_text.pack(side=tk.RIGHT)
    
    def create_status_cards(self, parent):
        """创建状态卡片"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 配置网格
        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)
        
        # 运行时间卡片
        self.create_card(cards_frame, "⏱️ 运行时间", self.uptime, 0, 0)
        
        # 处理统计卡片
        self.stats_card = self.create_card(cards_frame, "📊 处理统计", "", 0, 1)
        self.stats_label = ttk.Label(self.stats_card, text="文件: 0 | 文件夹: 0", 
                                    style='Value.TLabel')
        self.stats_label.pack()
        
        # 错误统计卡片
        self.create_card(cards_frame, "⚠️ 错误统计", self.error_count, 0, 2)
        
        # 当前任务卡片
        self.create_card(cards_frame, "🔄 当前任务", self.current_task, 1, 0, columnspan=2)
        
        # 最后活动卡片
        self.create_card(cards_frame, "📅 最后活动", self.last_activity, 1, 2)
    
    def create_card(self, parent, title, value_var, row, col, columnspan=1):
        """创建单个状态卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="15")
        card_frame.grid(row=row, column=col, columnspan=columnspan, 
                       sticky="ew", padx=5, pady=5)
        
        if isinstance(value_var, tk.StringVar) or isinstance(value_var, tk.IntVar):
            value_label = ttk.Label(card_frame, textvariable=value_var, 
                                   style='Value.TLabel')
            value_label.pack()
        elif value_var:  # 如果有值就显示
            value_label = ttk.Label(card_frame, text=str(value_var), 
                                   style='Value.TLabel')
            value_label.pack()
        
        return card_frame
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 重新处理按钮
        self.reprocess_btn = ttk.Button(button_frame, text="🔄 重新处理现有数据", 
                                       command=self.reprocess_data,
                                       style='Action.TButton')
        self.reprocess_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看日志按钮
        self.log_btn = ttk.Button(button_frame, text="📋 查看日志", 
                                 command=self.view_logs,
                                 style='Action.TButton')
        self.log_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开目录按钮
        self.folder_btn = ttk.Button(button_frame, text="📁 打开监控目录", 
                                    command=self.open_monitor_folder,
                                    style='Action.TButton')
        self.folder_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 退出程序按钮
        self.exit_btn = ttk.Button(button_frame, text="❌ 退出程序", 
                                  command=self.exit_application,
                                  style='Action.TButton')
        self.exit_btn.pack(side=tk.RIGHT)
    
    def create_details_section(self, parent):
        """创建详细信息区域"""
        details_frame = ttk.LabelFrame(parent, text="📈 实时监控日志", padding="15")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本显示区域
        self.details_text = tk.Text(details_frame, height=10, wrap=tk.WORD,
                                   font=('Consolas', 9), bg='#2c3e50', fg='#ecf0f1',
                                   insertbackground='#ecf0f1')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, 
                                 command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始信息
        self.add_log_message("FIM Vietnam 前端版本启动")
        self.add_log_message("正在初始化文件监控...")
    
    def add_log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.details_text.insert(tk.END, formatted_message)
        self.details_text.see(tk.END)
        
        # 限制行数
        lines = self.details_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.details_text.delete("1.0", "10.0")
    
    def setup_tray(self):
        """设置系统托盘"""
        try:
            # 使用原始绿色图标作为默认托盘图标
            icon_path = "app_green.png" if os.path.exists("app_green.png") else "app.ico"

            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示主窗口", self.show_window),
                pystray.MenuItem("重新处理数据", self.reprocess_data),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("退出程序", self.exit_application)
            )

            # 创建托盘图标
            if icon_path.endswith('.png'):
                image = Image.open(icon_path)
            else:
                image = Image.open(icon_path)

            # 设置双击事件处理
            self.tray_icon = pystray.Icon("FIM_Vietnam", image,
                                         "FIM Vietnam 监控中", menu,
                                         default_action=self.show_window)

        except Exception as e:
            print(f"托盘设置失败: {str(e)}")
            self.tray_icon = None

    def update_tray_icon(self, status):
        """更新托盘图标颜色"""
        if not self.tray_icon:
            return

        try:
            if status == 'green':
                # 使用原始绿色图标
                icon_path = "app_green.png" if os.path.exists("app_green.png") else "app.ico"
            else:
                # 使用原始红色图标
                icon_path = "app_red.png" if os.path.exists("app_red.png") else "app.ico"

            if os.path.exists(icon_path):
                image = Image.open(icon_path)
                self.tray_icon.icon = image

                # 更新提示文本
                if status == 'green':
                    files = self.processed_files.get()
                    self.tray_icon.title = f"FIM Vietnam 运行中 | 文件: {files}"
                else:
                    self.tray_icon.title = "FIM Vietnam 已停止"

        except Exception as e:
            print(f"更新托盘图标失败: {str(e)}")

    def start_monitoring(self):
        """启动文件监控"""
        try:
            self.add_log_message("正在启动文件监控...")

            # 检查监控路径
            if not os.path.exists(MON_PATH):
                self.add_log_message(f"错误：监控路径不存在 - {MON_PATH}")
                self.current_task.set("错误：监控路径不存在")
                return

            # 创建目标路径
            os.makedirs(DATA_BAK_PATH, exist_ok=True)
            os.makedirs(AIRFASE_PATH, exist_ok=True)

            # 更新状态
            self.monitor.update_status("启动监控", {
                "mon_path": MON_PATH,
                "data_bak_path": DATA_BAK_PATH,
                "airfase_path": AIRFASE_PATH,
                "target_aircraft": TARGET_AIRCRAFT
            })

            # 首次处理现有数据
            self.add_log_message("处理现有数据...")
            self.current_task.set("处理现有数据")

            # 在后台线程中处理现有数据
            threading.Thread(target=self.process_existing_data, daemon=True).start()

            # 设置文件系统监控
            event_handler = VietnamFileHandler(self.monitor)
            self.observer = Observer()
            self.observer.schedule(event_handler, MON_PATH, recursive=True)
            self.observer.start()

            self.monitoring_active = True
            self.add_log_message("文件监控已启动")
            self.current_task.set("监控中 - 等待新文件")

            logger.info("FIM Vietnam 前端版本启动完成")

        except Exception as e:
            self.add_log_message(f"启动监控失败: {str(e)}")
            self.current_task.set("启动失败")
            logger.error(f"启动监控失败: {str(e)}")

    def process_existing_data(self):
        """处理现有数据（后台线程）"""
        try:
            processor = VietnamDataProcessor(self.monitor)
            processor.process_new_data()

            # 更新界面
            self.root.after(0, lambda: self.current_task.set("监控中 - 等待新文件"))
            self.root.after(0, lambda: self.add_log_message("现有数据处理完成"))

        except Exception as e:
            self.root.after(0, lambda: self.add_log_message(f"处理现有数据失败: {str(e)}"))
            logger.error(f"处理现有数据失败: {str(e)}")

    def update_loop(self):
        """状态更新循环"""
        while True:
            try:
                # 更新运行时间
                uptime_delta = datetime.now() - self.start_time
                uptime_str = str(uptime_delta).split('.')[0]  # 去掉微秒
                self.root.after(0, lambda: self.uptime.set(uptime_str))

                # 读取监控状态
                if hasattr(self.monitor, 'processed_files'):
                    files = self.monitor.processed_files
                    folders = self.monitor.processed_folders
                    errors = self.monitor.errors

                    self.root.after(0, lambda: self.processed_files.set(files))
                    self.root.after(0, lambda: self.processed_folders.set(folders))
                    self.root.after(0, lambda: self.error_count.set(errors))
                    self.root.after(0, lambda: self.stats_label.configure(text=f"文件: {files} | 文件夹: {folders}"))

                # 更新最后活动时间
                if hasattr(self.monitor, 'last_activity'):
                    last_time = self.monitor.last_activity
                    time_diff = datetime.now() - last_time
                    if time_diff.total_seconds() < 60:
                        activity_text = "刚刚"
                    elif time_diff.total_seconds() < 3600:
                        minutes = int(time_diff.total_seconds() // 60)
                        activity_text = f"{minutes}分钟前"
                    else:
                        hours = int(time_diff.total_seconds() // 3600)
                        activity_text = f"{hours}小时前"

                    self.root.after(0, lambda: self.last_activity.set(activity_text))

                # 更新托盘图标和提示
                if self.tray_icon:
                    self.update_tray_icon('green')  # 运行中显示绿色图标

                time.sleep(2)  # 每2秒更新一次

            except Exception as e:
                print(f"更新循环错误: {str(e)}")
                time.sleep(5)

    def reprocess_data(self):
        """重新处理现有数据"""
        self.add_log_message("开始重新处理现有数据...")
        self.current_task.set("重新处理数据中...")

        # 在后台线程中处理
        threading.Thread(target=self.process_existing_data, daemon=True).start()

    def view_logs(self):
        """查看日志"""
        try:
            log_dir = "logs"
            if os.path.exists(log_dir):
                os.startfile(log_dir)
            else:
                messagebox.showinfo("提示", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开日志目录失败: {str(e)}")

    def open_monitor_folder(self):
        """打开监控目录"""
        try:
            if os.path.exists(MON_PATH):
                os.startfile(MON_PATH)
            else:
                messagebox.showwarning("警告", f"监控目录不存在: {MON_PATH}")
        except Exception as e:
            messagebox.showerror("错误", f"打开监控目录失败: {str(e)}")

    def on_minimize(self, event):
        """窗口最小化事件"""
        if event.widget == self.root:
            # 更新任务栏标题显示状态（不显示程序名，节约空间）
            files = self.processed_files.get()
            folders = self.processed_folders.get()
            self.root.title(f"运行中 | 文件:{files} 文件夹:{folders}")

    def on_closing(self):
        """窗口关闭事件 - 点击X时最小化到托盘，类似微信"""
        # 最小化到系统托盘，不退出程序
        if self.tray_icon:
            self.root.withdraw()
            self.is_minimized_to_tray = True
            self.start_tray()
            self.add_log_message("程序已最小化到系统托盘（类似微信）")
            # 更新托盘图标为运行状态
            self.update_tray_icon('green')
        else:
            # 如果托盘不可用，则直接退出
            self.exit_application()

    def start_tray(self):
        """启动系统托盘"""
        if self.tray_icon:
            threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.is_minimized_to_tray = False
        # 恢复窗口标题
        self.root.title("FIM Vietnam 监控中心 - 运行中")
        if self.tray_icon:
            self.tray_icon.stop()

    def exit_application(self, icon=None, item=None):
        """退出应用程序"""
        self.add_log_message("正在退出程序...")

        # 停止文件监控
        if self.observer:
            try:
                self.observer.stop()
                self.observer.join(timeout=5)
                self.add_log_message("文件监控已停止")
            except:
                pass

        # 停止托盘
        if self.tray_icon:
            self.tray_icon.stop()

        # 更新状态
        if self.monitor:
            self.monitor.update_status("程序退出", {"exit_time": datetime.now().isoformat()})

        logger.info("FIM Vietnam 程序退出")

        # 退出程序
        self.root.quit()
        self.root.destroy()

        # 强制退出所有线程
        os._exit(0)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    # 检查依赖
    try:
        import pystray
        from PIL import Image
        from watchdog.observers import Observer
    except ImportError as e:
        print(f"缺少依赖库: {str(e)}")
        print("请运行: pip install pystray pillow watchdog")
        input("按回车键退出...")
        return

    # 设置进程优先级
    try:
        import psutil
        p = psutil.Process()
        p.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if os.name == 'nt' else 10)
        print("已设置进程为低优先级")
    except ImportError:
        print("psutil未安装，无法设置进程优先级")
    except Exception as e:
        print(f"设置进程优先级失败: {str(e)}")

    # 启动GUI
    app = FIMVietnamFrontend()
    app.run()

if __name__ == "__main__":
    main()
