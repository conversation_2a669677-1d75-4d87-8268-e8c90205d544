import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import os
import tkinter as tk
from tkinter import filedialog
import sys


def select_and_process_files():
    # 初始化Tkinter（不显示窗口）
    root = tk.Tk()
    root.withdraw()

    # 选择原始CSV文件
    csv_file = filedialog.askopenfilename(
        title="选择原始数据文件",
        filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
    )
    if not csv_file:
        print("操作取消：未选择文件")
        sys.exit(0)

    # 自动生成关联文件名
    file_dir = os.path.dirname(csv_file)
    base_name = os.path.splitext(os.path.basename(csv_file))[0]

    dict_file = os.path.join(file_dir, f"Parm_{base_name}.xlsx")
    output_file = os.path.join(file_dir, f"{base_name}.xlsx")

    # 验证字典文件存在
    if not os.path.exists(dict_file):
        print(f"错误：找不到字典文件 {dict_file}")
        sys.exit(1)

    # 静默处理文件
    print("\n正在处理文件...")
    print(f"原始数据: {csv_file}")
    print(f"字典文件: {dict_file}")
    print(f"输出文件: {output_file}")

    try:
        # ===== 1. 数据读取和基础处理 =====
        dict_df = pd.read_excel(dict_file)
        param_dict = dict(zip(dict_df['序号'], dict_df['中文名']))
        data_df = pd.read_csv(csv_file, delimiter=';', header=None)
        data_df.columns = [param_dict.get(col + 1, f'参数{col + 1}') for col in range(len(data_df.columns))]

        # ===== 2. 数据排序处理 =====
        # 检查排序列是否存在
        sort_columns = []
        if '起飞日期' in data_df.columns:
            sort_columns.append(('起飞日期', False))  # 降序
        if '飞机号' in data_df.columns:
            sort_columns.append(('飞机号', True))  # 升序
        if '起飞时间' in data_df.columns:
            sort_columns.append(('起飞时间', False))  # 降序

        if sort_columns:
            print("\n正在执行排序...")
            sort_order = [f"{col}({'升序' if asc else '降序'})" for col, asc in sort_columns]
            print(f"排序顺序: {', '.join(sort_order)}")

            data_df.sort_values(
                by=[col for col, asc in sort_columns],
                ascending=[asc for col, asc in sort_columns],
                inplace=True
            )
        else:
            print("\n警告：未找到排序列，跳过排序步骤")

        # ===== 3. Excel格式处理 =====
        temp_file = "temp.xlsx"
        with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
            data_df.to_excel(writer, index=False)
            ws = writer.sheets['Sheet1']

            # 格式设置
            ws.freeze_panes = 'A2'
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(
                        horizontal='center',
                        vertical='center',
                        wrap_text=False
                    )

        # ===== 4. 列宽自动调整 =====
        wb = load_workbook(temp_file)
        ws = wb.active

        for col in ws.columns:
            max_len = max(
                sum(2 if ord(c) > 127 else 1 for c in str(cell.value))
                for cell in col if cell.value
            ) or 10  # 默认最小宽度
            ws.column_dimensions[col[0].column_letter].width = max_len + 1

        # ===== 5. 最终保存 =====
        wb.save(output_file)
        os.remove(temp_file)

        print(f"\n处理完成！结果已保存到:\n{output_file}")
        sys.exit(0)

    except Exception as e:
        print(f"\n处理失败: {str(e)}")
        if os.path.exists("temp.xlsx"):
            os.remove("temp.xlsx")
        sys.exit(1)


if __name__ == "__main__":
    print("=== CSV转Excel转换器（带排序功能） ===")
    select_and_process_files()
