import pandas as pd
import os
import tkinter as tk
from tkinter import filedialog
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from datetime import datetime, timedelta
import sys


def auto_convert_to_numeric(value):
    """智能转换为数值，失败则返回原值"""
    if pd.isna(value) or value == '':
        return value

    try:
        if str(value).isdigit():
            return int(value)
        float_val = float(value)
        return int(float_val) if float_val.is_integer() else float_val
    except (ValueError, TypeError):
        return value


def parse_time(time_str):
    """将时间字符串转换为datetime对象"""
    try:
        return datetime.strptime(time_str, "%H:%M:%S")
    except (ValueError, TypeError):
        return None


def is_same_flight(row1, row2):
    """判断两行是否为同一航班"""
    # 检查飞机号、起飞日期、着陆机场是否相同
    if (row1['飞机号'] != row2['飞机号'] or
            row1['起飞日期'] != row2['起飞日期'] or
            row1['着陆机场'] != row2['着陆机场']):
        return False

    # 检查落地时间差是否小于2分钟
    time1 = parse_time(row1['落地时间'])
    time2 = parse_time(row2['落地时间'])

    if time1 is None or time2 is None:
        return False

    return abs(time1 - time2) < timedelta(minutes=2)


def compare_rows(row1, row2):
    """比较两行数据，返回应保留的行索引"""
    # 1. 比较空值数量
    null_count1 = row1.isnull().sum()
    null_count2 = row2.isnull().sum()

    if null_count1 > null_count2:
        return 1  # 保留row2
    elif null_count2 > null_count1:
        return 0  # 保留row1

    # 2. 比较0值数量
    zero_count1 = (row1 == 0).sum()
    zero_count2 = (row2 == 0).sum()

    if zero_count1 > zero_count2:
        return 1  # 保留row2
    elif zero_count2 > zero_count1:
        return 0  # 保留row1

    # 3. 默认保留第一行
    return 0


def remove_duplicate_flights(df):
    """去除重复航班数据"""
    i = 0
    while i < len(df) - 1:
        row1 = df.iloc[i]
        row2 = df.iloc[i + 1]

        if is_same_flight(row1, row2):
            if row1.equals(row2):
                # 完全相同，删除下一行
                df = df.drop(index=df.index[i + 1]).reset_index(drop=True)
            else:
                # 有差异，按优先级比较
                keep_index = compare_rows(row1, row2)
                if keep_index == 0:
                    df = df.drop(index=df.index[i + 1]).reset_index(drop=True)
                else:
                    df = df.drop(index=df.index[i]).reset_index(drop=True)
        else:
            i += 1
    return df


def read_csv_with_smart_conversion(csv_file, param_dict):
    """读取CSV并智能转换数值内容"""
    try:
        # 确定最大列数
        with open(csv_file, 'r', encoding='utf-8') as f:
            max_columns = max(len(line.strip().split(';')) for line in f)

        # 生成动态列名
        column_names = [
            str(param_dict.get(i + 1, f'Col_{i + 1}')) if i < len(param_dict)
            else f'Extra_{i - len(param_dict) + 1}'
            for i in range(max_columns)
        ]

        # 读取数据
        df = pd.read_csv(
            csv_file,
            delimiter=';',
            header=None,
            names=column_names,
            dtype=str,
            engine='c',
            on_bad_lines='warn'
        )

        # 智能转换数值列
        for col in df.columns:
            if not any(kw in col for kw in ['日期', '时间', 'Date', 'Time']):
                df[col] = df[col].apply(auto_convert_to_numeric)
                if pd.api.types.is_numeric_dtype(df[col]):
                    df[col] = pd.to_numeric(
                        df[col],
                        errors='coerce',
                        downcast='integer' if df[col].notna().all() and (df[col] % 1 == 0).all() else None
                    )

        return df

    except Exception as e:
        print(f"CSV读取错误: {str(e)}", file=sys.stderr)
        raise


def save_to_excel_with_format(df, output_file):
    """保存Excel并设置格式（优化列宽计算）"""
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)
        worksheet = writer.sheets['Sheet1']

        # 设置居中和对齐
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        for row in worksheet.iter_rows():
            for cell in row:
                cell.alignment = center_alignment

        # 精确计算每列宽度（同时考虑标题和内容）
        for idx, col in enumerate(df.columns, 1):
            col_letter = get_column_letter(idx)

            # 计算标题宽度（考虑中文字符）
            header_width = sum(2 if ord(c) > 127 else 1 for c in str(col))

            # 计算内容最大宽度
            if len(df[col]) > 0:
                content_width = df[col].astype(str).apply(
                    lambda x: sum(2 if ord(c) > 127 else 1 for c in str(x))
                ).max()
            else:
                content_width = 0

            # 最终列宽 = 最大值 + 2个字符缓冲（最小宽度为5）
            column_width = max(max(header_width, content_width) + 2, 5)

            # 设置列宽（限制最大宽度为50）
            worksheet.column_dimensions[col_letter].width = min(column_width, 50)

        # 额外设置日期列的特定格式
        date_columns = [col for col in df.columns if any(kw in col for kw in ['日期', '时间', 'Date', 'Time'])]
        for col in date_columns:
            col_idx = df.columns.get_loc(col) + 1
            for cell in worksheet[get_column_letter(col_idx)]:
                if cell.row > 1:  # 跳过标题行
                    cell.number_format = 'yyyy-mm-dd hh:mm:ss' if '时间' in col else 'yyyy-mm-dd'


def select_and_process_files():
    try:
        root = tk.Tk()
        root.withdraw()

        csv_file = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if not csv_file:
            print("未选择文件，程序退出", file=sys.stderr)
            sys.exit(0)

        output_file = os.path.splitext(csv_file)[0] + ".xlsx"
        base_name = os.path.splitext(os.path.basename(csv_file))[0]
        dict_file = os.path.join(os.path.dirname(csv_file), f"Parm_{base_name}.xlsx")

        if not os.path.exists(dict_file):
            raise FileNotFoundError(f"字典文件不存在: {dict_file}")

        print(f"\n处理开始:\n输入文件: {csv_file}\n输出文件: {output_file}")

        # 1. 读取数据
        print("[1/4] 读取数据并智能转换...")
        dict_df = pd.read_excel(dict_file)
        param_dict = dict(zip(dict_df['序号'], dict_df['中文名']))
        data_df = read_csv_with_smart_conversion(csv_file, param_dict)

        # 2. 按优先级排序
        print("[2/4] 执行排序...")
        sort_priority = ['起飞日期', '飞机号', '落地时间']
        available_cols = [col for col in sort_priority if col in data_df.columns]

        if available_cols:
            data_df = data_df.sort_values(
                by=available_cols,
                ascending=[True] * len(available_cols),
                na_position='first'
            )

        # 3. 数据去重
        print("[3/4] 执行数据去重...")
        required_columns = ['飞机号', '起飞日期', '着陆机场', '落地时间']
        if all(col in data_df.columns for col in required_columns):
            data_df = remove_duplicate_flights(data_df)
        else:
            print("警告: 缺少必要的去重字段，跳过去重步骤", file=sys.stderr)

        # 4. 保存并设置格式
        print("[4/4] 保存Excel并设置格式...")
        save_to_excel_with_format(data_df, output_file)

        print(f"\n处理完成！结果已保存到:\n{output_file}")
        sys.exit(0)  # 正常退出

    except Exception as e:
        print(f"\n处理失败: {str(e)}", file=sys.stderr)
        sys.exit(1)  # 异常退出


if __name__ == "__main__":
    print("=== CSV智能转换工具 ===")
    select_and_process_files()
