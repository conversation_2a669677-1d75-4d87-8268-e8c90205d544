FIM Vietnam 自动化监控服务 - 部署包
========================================

📦 部署包内容：
- FIM_Vietnam_Service.exe    主程序（10.4MB）
- FIM_Vietnam_Task.xml       任务计划程序配置
- install_service.bat        安装服务脚本
- start_service.bat          启动服务脚本
- stop_service.bat           停止服务脚本
- uninstall_service.bat      卸载服务脚本
- check_status.bat           检查状态脚本
- 部署说明.txt               本文件

🚀 快速部署步骤：

1. 【复制文件】
   将本目录下的所有文件复制到目标电脑的固定位置
   推荐位置：C:\FIM_Vietnam\

2. 【安装解压工具】
   下载并安装 7-Zip（免费）：https://www.7-zip.org/
   或者 WinRAR：https://www.winrar.com/

3. 【安装服务】
   右键点击 install_service.bat
   选择"以管理员身份运行"
   看到"[OK] 服务安装成功！"表示完成

4. 【启动服务】
   双击运行 start_service.bat

5. 【验证运行】
   双击运行 check_status.bat
   应该看到：
   [OK] 服务已安装在任务计划程序中
   [OK] 服务进程正在运行

6. 【测试自动启动】
   重启电脑，等待30秒后再次运行 check_status.bat

📊 监控和日志：

程序会自动创建以下文件：
- logs/fim_vietnam_YYYYMMDD.log      主日志
- logs/fim_vietnam_error_YYYYMMDD.log 错误日志  
- logs/fim_vietnam_progress_YYYYMMDD.log 进度日志
- status.json                        实时状态

📋 监控路径配置：
- 监控路径：Z:\Vietnam\PC卡
- 备份路径：Z:\DATA_BAK\FDIMU_PC
- AirFASE路径：D:\AirFASE\FIMRoot\ARJ21
- 目标飞机：B-652G, B-656E

⚙️ 服务特性：
- 开机自动启动（延迟30秒）
- 失败自动重启（最多3次）
- 低优先级运行，不影响系统性能
- 实时文件监控，CPU占用几乎为0
- 自动清理7天前的日志文件

🔧 管理命令：
- 启动服务：start_service.bat
- 停止服务：stop_service.bat
- 检查状态：check_status.bat
- 卸载服务：uninstall_service.bat（需管理员权限）

📞 故障排除：
1. 服务无法启动 → 检查监控路径是否存在
2. 解压失败 → 确认已安装7-Zip或WinRAR
3. 权限错误 → 以管理员身份运行相关脚本

✅ 测试验证：
本部署包已在以下环境测试通过：
- 成功处理真实的B-652G飞行数据
- 正确解析MSG.DAT文件（57条记录）
- 成功复制到两个目标位置
- 文件监控和日志记录正常工作
- RAR/ZIP/7Z解压功能正常

📝 版本信息：
- 构建时间：2025-07-18 15:35
- Python版本：3.11.9
- 包含模块：watchdog, rarfile, py7zr, psutil
- 文件大小：10.4MB（单文件，无需Python环境）

========================================
如有问题，请查看日志文件或联系技术支持。
